server:
  port: 8080
  servlet:
    context-path: /admin

spring:
  application:
    name: procurement-platform
  # 缓存相关配置
  cache:
    type: redis
  # 上传文件大小限制
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  cloud:
    nacos:
      config:
        enabled: false
      discovery:
        enabled: false
    gateway:
      enabled: false
      redis.enabled: false
    sentinel:
      enabled: false
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: dev

## spring security 对外暴露接口设置
security:
  micro: false
  oauth2:
    client:
      ignore-urls:
        - /webjars/**
        - /v3/api-docs/**
        - /doc.html
        - /swagger-ui.html
        - /swagger-ui/**
        - /swagger-resources
        - /token/check_token
        - /error
        - /druid/**
        - /actuator/**
        - /code/**

#--------------如下配置尽量不要变动-------------
# mybatis-plus 配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: auto
      where-strategy: not_empty
      insert-strategy: not_empty
      update-strategy: not_null
  type-handlers-package: com.ylz.saas.common.data.handler
  configuration:
    jdbc-type-for-null: 'null'
    call-setters-on-nulls: true
    shrink-whitespaces-in-sql: true
# 查询以下前缀的表，不输出日志
saas:
  mybatis:
    skip-table:
      - QRTZ_  #quartz 定时任务的表
      - ACT_   #工作流相关的表

  serviceTypeCode: BST250804112512001

# netty-socketio 配置
socketio:
  host: 0.0.0.0
  port: 9090
  # 单条消息最大长度，3M
  maxFramePayloadLength: 1048576
  # HTTP请求最大长度，默认为-1（无限制）
  maxHttpContentLength: 1048576
  # 连接池连接数
  bossCount: 1
  # 线程池数量，默认为10
  workCount: 100
  # 协议升级超时时间（毫秒），默认10秒。HTTP握手升级为ws协议超时时间
  upgradeTimeout: 100000
  # 心跳超时时间（毫秒），默认60秒
  pingTimeout: 600000
  # 是否允许自定义请求，默认值false
  allowCustomRequests: true
  # 心跳间隔时间 默认25s
  pingInterval: 25000
  # namespaces 以逗号隔开 每个空间需要对应一个Bean的名字，XXXMessageEventHandler,如chatMessageEventHandler
  namespaces: /chat,/comment