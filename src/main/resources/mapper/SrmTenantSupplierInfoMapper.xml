<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenantSupplierInfoMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenantSupplierInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="platformSupplierId" column="platform_supplier_id" jdbcType="BIGINT"/>
            <result property="supplierCode" column="supplier_code" jdbcType="VARCHAR"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="supplierShortName" column="supplier_short_name" jdbcType="VARCHAR"/>
            <result property="supplierType" column="supplier_type" jdbcType="VARCHAR"/>
            <result property="enterpriseNature" column="enterprise_nature" jdbcType="VARCHAR"/>
            <result property="supplierCategory" column="supplier_category" jdbcType="VARCHAR"/>
            <result property="supplierStatus" column="supplier_status" jdbcType="VARCHAR"/>
            <result property="supplierSource" column="supplier_source" jdbcType="VARCHAR"/>
            <result property="approvalStatus" column="approval_status" jdbcType="VARCHAR"/>
            <result property="serviceStartDate" column="service_start_date" jdbcType="DATE"/>
            <result property="serviceEndDate" column="service_end_date" jdbcType="DATE"/>
            <result property="legalPerson" column="legal_person" jdbcType="VARCHAR"/>
            <result property="legalPersonId" column="legal_person_id" jdbcType="VARCHAR"/>
            <result property="legalPersonPhone" column="legal_person_phone" jdbcType="VARCHAR"/>
            <result property="registeredAddress" column="registered_address" jdbcType="VARCHAR"/>
            <result property="postalCode" column="postal_code" jdbcType="VARCHAR"/>
            <result property="fax" column="fax" jdbcType="VARCHAR"/>
            <result property="annualRevenue" column="annual_revenue" jdbcType="DECIMAL"/>
            <result property="companyProfile" column="company_profile" jdbcType="VARCHAR"/>
            <result property="businessScope" column="business_scope" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,
        platform_supplier_id,supplier_code,supplier_name,
        supplier_short_name,supplier_type,enterprise_nature,
        supplier_category,supplier_status,supplier_source,
        approval_status,service_start_date,service_end_date,
        legal_person,legal_person_id,legal_person_phone,
        registered_address,postal_code,fax,
        annual_revenue,company_profile,business_scope,
        remark,del_flag,create_by,create_by_name,
        create_time,update_by,update_by_name,update_time,is_all_dept
    </sql>

    <!-- 供应商信息VO的ResultMap，包含联系人信息 -->
    <resultMap id="SupplierInfoVoResultMap" type="com.ylz.saas.vo.SrmTenantSupplierInfoVo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="platformSupplierId" column="platform_supplier_id" jdbcType="BIGINT"/>
        <result property="supplierCode" column="supplier_code" jdbcType="VARCHAR"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="supplierShortName" column="supplier_short_name" jdbcType="VARCHAR"/>
        <result property="supplierType" column="supplier_type" jdbcType="VARCHAR"/>
        <result property="enterpriseNature" column="enterprise_nature" jdbcType="VARCHAR"/>
        <result property="supplierCategory" column="supplier_category" jdbcType="VARCHAR"/>
        <result property="supplierStatus" column="supplier_status" jdbcType="VARCHAR"/>
        <result property="supplierSource" column="supplier_source" jdbcType="VARCHAR"/>
        <result property="approvalStatus" column="approval_status" jdbcType="VARCHAR"/>
        <result property="serviceStartDate" column="service_start_date" jdbcType="TIMESTAMP"/>
        <result property="serviceEndDate" column="service_end_date" jdbcType="TIMESTAMP"/>
        <result property="legalPerson" column="legal_person" jdbcType="VARCHAR"/>
        <result property="legalPersonId" column="legal_person_id" jdbcType="VARCHAR"/>
        <result property="legalPersonPhone" column="legal_person_phone" jdbcType="VARCHAR"/>
        <result property="registeredAddress" column="registered_address" jdbcType="VARCHAR"/>
        <result property="postalCode" column="postal_code" jdbcType="VARCHAR"/>
        <result property="fax" column="fax" jdbcType="VARCHAR"/>
        <result property="annualRevenue" column="annual_revenue" jdbcType="DECIMAL"/>
        <result property="companyProfile" column="company_profile" jdbcType="VARCHAR"/>
        <result property="businessScope" column="business_scope" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="registeredCapital" column="registered_capital" jdbcType="DECIMAL"/>
        <result property="socialCreditCode" column="social_credit_code" jdbcType="VARCHAR"/>
        <result property="establishDate" column="establish_date" jdbcType="TIMESTAMP"/>
        <!-- 联系人信息 -->
        <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
        <result property="contactPhone" column="contact_phone" jdbcType="VARCHAR"/>
        <!-- 邀请状态相关字段 -->
        <result property="hasInvited" column="has_invited" jdbcType="BOOLEAN"/>
        <result property="inviteStatus" column="invite_status" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="showInviteButton" column="show_invite_button" jdbcType="BOOLEAN"/>
        <!-- isAllDept和部门名称字段 -->
        <result property="isAllDept" column="is_all_dept" jdbcType="BOOLEAN"/>
        <result property="deptNames" column="dept_names" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 分页查询我的租户供应商信息（联查联系人） -->
    <select id="getMyTenantSupplierPageWithContact" resultMap="SupplierInfoVoResultMap">
        SELECT
        si.id,
        si.platform_supplier_id,
        si.supplier_code,
        si.supplier_name,
        si.supplier_short_name,
        si.supplier_type,
        si.enterprise_nature,
        si.supplier_category,
        si.supplier_status,
        si.supplier_source,
        si.status,
        CASE
            WHEN si.supplier_source = 'SRM_SYSTEM' THEN si.approval_status
            WHEN si.supplier_source = 'SUPPLIER_REGISTER' THEN sd.approval_status
            ELSE si.approval_status
        END as approval_status,
        si.service_start_date,
        si.service_end_date,
        si.legal_person,
        si.legal_person_id,
        si.legal_person_phone,
        si.registered_address,
        si.postal_code,
        si.fax,
        si.annual_revenue,
        si.company_profile,
        si.business_scope,
        si.remark,
        si.del_flag,
        si.create_by,
        si.create_by_name,
        si.create_time,
        si.update_by,
        si.update_by_name,
        si.update_time,
        si.is_all_dept,
        psi.registered_capital,
        psi.social_credit_code,
        psi.establish_date,
        sc.contact_name,
        sc.contact_phone,
        CASE
            WHEN si.is_all_dept = 1 THEN '全部组织'
            ELSE GROUP_CONCAT(DISTINCT dept_all.name ORDER BY dept_all.name SEPARATOR ',')
        END as dept_names
        FROM srm_tenant_supplier_info si
        LEFT JOIN srm_platform_supplier_info psi ON si.platform_supplier_id = psi.id
        LEFT JOIN srm_tenant_supplier_dept sd ON si.id = sd.tenant_supplier_id AND sd.del_flag = 0
        LEFT JOIN sys_dept dept_all ON sd.dept_id = dept_all.dept_id
        LEFT JOIN (
        SELECT
        sc1.tenant_supplier_id,
        sc1.contact_name,
        sc1.contact_phone
        FROM srm_tenant_supplier_contact sc1
        WHERE sc1.del_flag = 0
        AND sc1.create_time = (
        SELECT MIN(sc2.create_time)
        FROM srm_tenant_supplier_contact sc2
        WHERE sc2.tenant_supplier_id = sc1.tenant_supplier_id
        AND sc2.del_flag = 0
        )
        GROUP BY sc1.tenant_supplier_id, sc1.contact_name, sc1.contact_phone
        ) sc ON si.id = sc.tenant_supplier_id
        WHERE si.del_flag = 0
        AND (
            si.supplier_source = 'SRM_SYSTEM'
            OR
            (si.supplier_source = 'SUPPLIER_REGISTER' AND sd.approval_status = 'INVITE_APPROVED')
        )
        <if test="approvalStatus != null and approvalStatus != ''">
            AND (
                CASE
                    WHEN si.supplier_source = 'SRM_SYSTEM' THEN si.approval_status = #{approvalStatus}
                    WHEN si.supplier_source = 'SUPPLIER_REGISTER' THEN sd.approval_status = #{approvalStatus}
                    ELSE si.approval_status = #{approvalStatus}
                END
            )
        </if>
        <if test="supplierName != null and supplierName != ''">
            AND si.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
        </if>
        <if test="supplierTypes != null and supplierTypes.size() > 0">
            AND si.supplier_type IN
            <foreach collection="supplierTypes" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="enterpriseNatures != null and enterpriseNatures.size() > 0">
            AND si.enterprise_nature IN
            <foreach collection="enterpriseNatures" item="nature" open="(" separator="," close=")">
                #{nature}
            </foreach>
        </if>
        <if test="status != null and status != ''">
            AND si.status = #{status}
        </if>
        <if test="deptId != null">
            AND (si.is_all_dept = 1 OR sd.dept_id = #{deptId})
        </if>
        GROUP BY si.id, si.tenant_id, si.platform_supplier_id, si.supplier_code, si.supplier_name,
                 si.supplier_short_name, si.supplier_type, si.enterprise_nature, si.supplier_category,
                 si.supplier_status, si.supplier_source, si.status, si.approval_status,
                 si.service_start_date, si.service_end_date, si.legal_person, si.legal_person_id,
                 si.legal_person_phone, si.registered_address, si.postal_code, si.fax,
                 si.annual_revenue, si.company_profile, si.business_scope, si.remark,
                 si.del_flag, si.create_by, si.create_by_name, si.create_time,
                 si.update_by, si.update_by_name, si.update_time, si.is_all_dept,
                 psi.registered_capital, psi.social_credit_code, psi.establish_date,
                 sc.contact_name, sc.contact_phone
        ORDER BY si.id DESC
    </select>

    <!-- 分页查询租户供应商信息（用于邀请功能） -->
    <select id="getTenantSupplierPageWithInviteStatus" resultMap="SupplierInfoVoResultMap">
        SELECT
        si.id,
        si.platform_supplier_id,
        si.supplier_code,
        si.supplier_name,
        si.supplier_short_name,
        si.supplier_type,
        si.enterprise_nature,
        si.supplier_category,
        si.supplier_status,
        si.supplier_source,
        si.approval_status,
        si.service_start_date,
        si.service_end_date,
        si.legal_person,
        si.legal_person_id,
        si.legal_person_phone,
        si.registered_address,
        si.postal_code,
        si.fax,
        si.annual_revenue,
        si.company_profile,
        si.business_scope,
        si.remark,
        si.del_flag,
        si.create_by,
        si.create_by_name,
        si.create_time,
        si.update_by,
        si.update_by_name,
        si.update_time,
        si.is_all_dept,
        si.status,
        psi.registered_capital,
        psi.social_credit_code,
        psi.establish_date,
        sc.contact_name,
        sc.contact_phone,
        CASE WHEN current_dept_invite.id IS NOT NULL THEN TRUE ELSE FALSE END as has_invited,
        current_dept_invite.approval_status as invite_status,
        CASE WHEN current_dept_invite.id IS NULL THEN TRUE ELSE FALSE END as show_invite_button,
        CASE
            WHEN si.is_all_dept = 1 THEN '全部组织'
            ELSE GROUP_CONCAT(DISTINCT dept_all.name ORDER BY dept_all.name SEPARATOR ',')
        END as dept_names
        FROM srm_tenant_supplier_info si
        LEFT JOIN srm_platform_supplier_info psi ON si.platform_supplier_id = psi.id
        LEFT JOIN srm_tenant_supplier_dept current_dept_invite ON si.id = current_dept_invite.tenant_supplier_id
            AND current_dept_invite.dept_id = #{deptId}
            AND current_dept_invite.relation_type = 'INVITE'
            AND current_dept_invite.del_flag = 0
        LEFT JOIN srm_tenant_supplier_dept sd_all ON si.id = sd_all.tenant_supplier_id
            AND sd_all.del_flag = 0
        LEFT JOIN sys_dept dept_all ON sd_all.dept_id = dept_all.dept_id
        LEFT JOIN (
            SELECT
            sc1.tenant_supplier_id,
            sc1.contact_name,
            sc1.contact_phone
            FROM srm_tenant_supplier_contact sc1
            WHERE sc1.del_flag = 0
            AND sc1.create_time = (
                SELECT MIN(sc2.create_time)
                FROM srm_tenant_supplier_contact sc2
                WHERE sc2.tenant_supplier_id = sc1.tenant_supplier_id
                AND sc2.del_flag = 0
            )
            GROUP BY sc1.tenant_supplier_id, sc1.contact_name, sc1.contact_phone
        ) sc ON si.id = sc.tenant_supplier_id
        WHERE si.del_flag = 0
          and si.supplier_source = 'SUPPLIER_REGISTER'
        AND si.approval_status = 'APPROVED'
        <if test="supplierName != null and supplierName != ''">
            AND si.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
        </if>
        <if test="supplierTypes != null and supplierTypes.size() > 0">
            AND si.supplier_type IN
            <foreach collection="supplierTypes" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="enterpriseNatures != null and enterpriseNatures.size() > 0">
            AND si.enterprise_nature IN
            <foreach collection="enterpriseNatures" item="nature" open="(" separator="," close=")">
                #{nature}
            </foreach>
        </if>
        <if test="status != null and status != ''">
            AND si.status = #{status}
        </if>
        GROUP BY si.id, si.tenant_id, si.platform_supplier_id, si.supplier_code, si.supplier_name,
                 si.supplier_short_name, si.supplier_type, si.enterprise_nature, si.supplier_category,
                 si.supplier_status, si.supplier_source, si.approval_status, si.service_start_date,
                 si.service_end_date, si.legal_person, si.legal_person_id, si.legal_person_phone,
                 si.registered_address, si.postal_code, si.fax, si.annual_revenue,
                 si.company_profile, si.business_scope, si.remark, si.del_flag,
                 si.create_by, si.create_by_name, si.create_time, si.update_by,
                 si.update_by_name, si.update_time, si.is_all_dept, si.status,
                 psi.registered_capital, psi.social_credit_code, psi.establish_date,
                 sc.contact_name, sc.contact_phone, current_dept_invite.id, current_dept_invite.approval_status
        ORDER BY si.id DESC
    </select>
</mapper>
