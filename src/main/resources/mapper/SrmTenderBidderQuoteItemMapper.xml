<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderBidderQuoteItemMapper">

    <select id="queryQuoteRoundCount" resultType="com.ylz.saas.vo.QuoteRoundCountVo">
        SELECT
            tenant_supplier_id AS tenantSupplierId,
            COUNT(distinct round_no) AS roundCount
        FROM
            srm_tender_bidder_quote_item
        WHERE
            section_id = #{sectionId}
            AND tender_bidder_quote_item.tenant_supplier_id IN
        <foreach item="item" collection="tenantSupplierIds" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by
            tenant_supplier_id
    </select>

    <select id="queryQuoteStatistics" resultType="com.ylz.saas.vo.QuoteStatisticsVo">
        SELECT
            tenant_supplier_id AS tenantSupplierId,
            COUNT(material_code) AS quoteMaterialCount,
            SUM(quote_amount) AS totalQuoteAmount,
            quote_ip
        FROM
            srm_tender_bidder_quote_item
        WHERE
            section_id = #{sectionId}
            AND round_no = #{roundNo}
            AND tenant_supplier_id IN
        <foreach item="item" collection="tenantSupplierIds" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by
            tenant_supplier_id
    </select>

    <select id="queryQuoteRoundCountByNoticeAndProject" resultType="com.ylz.saas.vo.QuoteRoundCountVo">
        SELECT
            tenant_supplier_id AS tenantSupplierId,
            COUNT(distinct round_no) AS roundCount
        FROM
            srm_tender_bidder_quote_item
        WHERE
            project_id = #{projectId}
            AND notice_id = #{noticeId}
            AND section_id = #{sectionId}
        group by
            tenant_supplier_id
    </select>

    <select id="queryQuoteStatisticsByNoticeAndProject" resultType="com.ylz.saas.vo.QuoteStatisticsVo">
        select
            tenant_supplier_id AS tenantSupplierId,
            COUNT(material_code) AS quoteMaterialCount,
            SUM(min_quote_amount) AS totalQuoteAmount,
            quote_ip
            from (
                    SELECT
                        tenant_supplier_id,
                        material_code,
                        quote_ip,
                        min(quote_amount) AS min_quote_amount
                    FROM
                        srm_tender_bidder_quote_item
                    WHERE
                        notice_id = #{noticeId}
                        AND project_id = #{projectId}
                        AND section_id = #{sectionId}
                        AND round_no = #{roundNo}
                        AND tenant_supplier_id IN
                    <foreach item="item" collection="tenantSupplierIds" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    group by
                        tenant_supplier_id, material_code
                ) tmp
        group by
            tenant_supplier_id
    </select>

    <!-- 查询报价项目列表（联查供应商名称） -->
    <select id="queryQuoteItemsWithSupplierName" resultType="com.ylz.saas.entity.SrmTenderBidderQuoteItem">
        SELECT
            qi.id,
            qi.tenant_id,
            qi.dept_id,
            qi.project_id,
            qi.notice_id,
            qi.section_id,
            qi.tenant_supplier_id,
            qi.tender_supplier_response_id,
            qi.project_item_id,
            qi.procurement_project_payment_id,
            qi.round_no,
            qi.material_code,
            qi.material_name,
            qi.spec_model,
            qi.unit,
            qi.required_quantity,
            qi.quote_quantity,
            qi.quote_price,
            qi.quote_amount,
            qi.delivery_period,
            qi.quote_time,
            qi.quote_ip,
            qi.awarded,
            qi.awarded_quantity,
            qi.remark,
            qi.del_flag,
            qi.create_by,
            qi.create_by_name,
            qi.create_time,
            qi.update_by,
            qi.update_by_name,
            qi.update_time,
            si.supplier_name
        FROM srm_tender_bidder_quote_item qi
        LEFT JOIN srm_tenant_supplier_info si ON qi.tenant_supplier_id = si.id AND si.del_flag = 0
        WHERE qi.section_id = #{sectionId}
            AND qi.del_flag = 0
            <if test="roundNo != null">
                AND qi.round_no = #{roundNo}
            </if>
            <if test="tenantSupplierId != null">
                AND qi.tenant_supplier_id = #{tenantSupplierId}
            </if>
            <if test="projectItemIds != null and projectItemIds.size() > 0">
                AND qi.project_item_id IN
                <foreach item="item" collection="projectItemIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        ORDER BY qi.material_code ASC
    </select>

    <!-- 根据noticeId和projectId查询报价项目列表（联查供应商名称） -->
    <select id="queryQuoteItemsByNoticeAndProject" resultType="com.ylz.saas.entity.SrmTenderBidderQuoteItem">
        SELECT
            qi.id,
            qi.tenant_id,
            qi.dept_id,
            qi.project_id,
            qi.notice_id,
            qi.section_id,
            qi.tenant_supplier_id,
            qi.tender_supplier_response_id,
            qi.project_item_id,
            qi.procurement_project_payment_id,
            qi.round_no,
            qi.material_code,
            qi.material_name,
            qi.spec_model,
            qi.unit,
            qi.required_quantity,
            qi.available_quantity,
            qi.quote_price,
            qi.quote_amount,
            qi.quote_time,
            qi.quote_ip,
            qi.awarded,
            qi.awarded_quantity,
            qi.remark,
            qi.del_flag,
            qi.create_by,
            qi.create_by_name,
            qi.create_time,
            qi.update_by,
            qi.update_by_name,
            qi.update_time,
            si.supplier_name
        FROM srm_tender_bidder_quote_item qi
        LEFT JOIN srm_tenant_supplier_info si ON qi.tenant_supplier_id = si.id AND si.del_flag = 0
        WHERE qi.notice_id = #{noticeId}
            AND qi.project_id = #{projectId}
            AND qi.del_flag = 0
            <if test="sectionId != null">
                AND qi.section_id = #{sectionId}
            </if>
            <if test="roundNo != null">
                AND qi.round_no = #{roundNo}
            </if>
            <if test="awarded != null and awarded">
                AND qi.awarded = 1
            </if>
            <if test="tenantSupplierId != null">
                AND qi.tenant_supplier_id = #{tenantSupplierId}
            </if>
            <if test="projectItemIds != null and projectItemIds.size() > 0">
                AND qi.project_item_id IN
                <foreach item="item" collection="projectItemIds" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        ORDER BY qi.material_code ASC
    </select>

    <!-- 根据noticeId和projectId查询中标明细列表（联查供应商名称） -->
    <select id="queryAwardedItemsWithSupplierName" resultType="com.ylz.saas.entity.SrmTenderBidderQuoteItem">
        SELECT
        qi.*,
        si.supplier_name,
        bul.location_name as usageLocationName,
        bul.address as usageLocationAddress,
        concat(bul.province, '/', bul.city, '/', bul.district) as usageLocationRegion,
        bqi.indicator_name as qualityIndicatorName
        FROM srm_tender_bidder_quote_item qi
        left join srm_procurement_project_item sppi on qi.project_item_id = sppi.id
        left join base_usage_location bul on sppi.usage_location_id = bul.id
        left join base_quality_indicator bqi on sppi.quality_indicator_id = bqi.id
        LEFT JOIN srm_tenant_supplier_info si ON qi.tenant_supplier_id = si.id AND si.del_flag = 0
        WHERE qi.notice_id = #{noticeId}
        AND qi.project_id = #{projectId}
        AND qi.awarded = 1
        AND qi.del_flag = 0
        and qi.round_no = (select max(stbqi.round_no) from srm_tender_bidder_quote_item stbqi
                                                      where stbqi.notice_id = #{noticeId}
                                                        AND stbqi.project_id = #{projectId}
                                                        AND stbqi.awarded = 1
                                                        AND stbqi.del_flag = 0
                                                        <if test="sectionId !=null">
                                                            and stbqi.section_id = #{sectionId}
                                                        </if>
                                                        <if test="tenantSupplierId !=null">
                                                            and stbqi.tenant_supplier_id = #{tenantSupplierId}
                                                        </if>
                                                        <if test="materialCode != null and materialCode != ''">
                                                            AND stbqi.material_code LIKE CONCAT('%', #{materialCode}, '%')
                                                        </if>
                                                        <if test="materialName != null and materialName != ''">
                                                            AND stbqi.material_name LIKE CONCAT('%', #{materialName}, '%')
                                                        </if>)
        <if test="sectionId !=null">
            and qi.section_id = #{sectionId}
        </if>
        <if test="tenantSupplierId !=null">
            and qi.tenant_supplier_id = #{tenantSupplierId}
        </if>
        <if test="materialCode != null and materialCode != ''">
            AND qi.material_code LIKE CONCAT('%', #{materialCode}, '%')
        </if>
        <if test="materialName != null and materialName != ''">
            AND qi.material_name LIKE CONCAT('%', #{materialName}, '%')
        </if>
        ORDER BY qi.material_code ASC
    </select>

    <!-- 根据noticeId和projectId查询中标供应商列表 -->
    <select id="queryAwardedSuppliers" resultType="com.ylz.saas.resp.AwardedSupplierResp">
        SELECT
        si.id AS tenantSupplierId,
        si.supplier_code AS supplierCode,
        si.supplier_name AS supplierName,
        si.supplier_short_name AS supplierShortName,
        si.supplier_type AS supplierType,
        si.enterprise_nature AS enterpriseNature,
        si.supplier_category AS supplierCategory,
        si.supplier_status AS supplierStatus,
        si.legal_person AS legalPerson,
        si.legal_person_phone AS legalPersonPhone,
        si.registered_address AS registeredAddress,
        si.service_start_date AS serviceStartDate,
        si.service_end_date AS serviceEndDate,
        si.annual_revenue AS annualRevenue,
        si.company_profile AS companyProfile,
        si.business_scope AS businessScope,
        si.remark AS remark,
        si.status AS status,
        sc.contact_name AS contactName,
        sc.contact_phone AS contactPhone,
        qi.section_id,
        qi.result_id,
        sn.notice_status,
        sn.signature_status,
        COUNT(DISTINCT qi.material_code) AS awardedMaterialCount,
        (select bqi.quote_amount from srm_tender_bidder_quote_item bqi
            INNER JOIN srm_tenant_supplier_info tsi ON bqi.tenant_supplier_id = tsi.id
                                 WHERE bqi.notice_id = #{noticeId}
        AND bqi.project_id = #{projectId}
        AND bqi.awarded = 1
        AND bqi.del_flag = 0
        <if test="supplierName != null and supplierName != ''">
            AND tsi.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
        </if>
        order by bqi.round_no desc limit 1) AS totalAwardedAmount,
        (select bqi.awarded_quantity from srm_tender_bidder_quote_item bqi
        INNER JOIN srm_tenant_supplier_info tsi ON bqi.tenant_supplier_id = tsi.id
        WHERE bqi.notice_id = #{noticeId}
        AND bqi.project_id = #{projectId}
        AND bqi.awarded = 1
        AND bqi.del_flag = 0
        <if test="supplierName != null and supplierName != ''">
            AND tsi.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
        </if>
        order by bqi.round_no desc limit 1) AS totalAwardedQuantity
        FROM srm_tender_bidder_quote_item qi
        INNER JOIN srm_tenant_supplier_info si ON qi.tenant_supplier_id = si.id AND si.del_flag = 0
        LEFT JOIN (
        SELECT
        sc1.tenant_supplier_id,
        sc1.contact_name,
        sc1.contact_phone
        FROM srm_tenant_supplier_contact sc1
        WHERE sc1.del_flag = 0
        AND sc1.create_time = (
        SELECT MAX(sc2.create_time)
        FROM srm_tenant_supplier_contact sc2
        WHERE sc2.tenant_supplier_id = sc1.tenant_supplier_id
        AND sc2.del_flag = 0
        )
        ) sc ON si.id = sc.tenant_supplier_id
        left join srm_tender_award_notice sn on
        qi.tenant_supplier_id = sn.tenant_supplier_id
        and sn.notice_id = qi.notice_id
        and sn.project_id = qi.project_id
        and qi.section_id = sn.section_id
        WHERE qi.notice_id = #{noticeId}
        AND qi.project_id = #{projectId}
        AND qi.awarded = 1
        AND qi.del_flag = 0
        <if test="supplierName != null and supplierName != ''">
            AND si.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
        </if>
        GROUP BY qi.section_id,si.id, si.supplier_code, si.supplier_name, si.supplier_short_name,
        si.supplier_type, si.enterprise_nature, si.supplier_category, si.supplier_status,
        si.legal_person, si.legal_person_phone, si.registered_address,
        si.service_start_date, si.service_end_date, si.annual_revenue,
        si.company_profile, si.business_scope, si.remark, si.status,
        sc.contact_name, sc.contact_phone
        ORDER BY si.supplier_name ASC
    </select>


    <select id="getQuoteLessThanThresholdSupplierList" resultType="java.lang.String">
        select material_code from srm_tender_bidder_quote_item
        where notice_id = #{params.noticeId}
        and section_id = #{params.sectionId}
        and round_no = #{params.quoteRound}
        group by material_code
        having count(distinct tenant_supplier_id)  &lt; 3
    </select>

    <select id="queryQuoteDetailBySupplier" resultType="com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierRoundResp$SupplierQuoteInfo">
        SELECT
            q.tenant_supplier_id AS tenantSupplierId,
            si.supplier_name AS supplierName,
            sr.contact_person AS contactPerson,
            sr.contact_phone AS contactPhone,
            sr.quote_status AS quoteStatus,
            q.quote_ip AS quoteIp,
            q.quote_time AS quoteTime,
            q.round_no AS roundNo,
            SUM(q.quote_amount) AS totalQuoteAmount
        FROM srm_tender_bidder_quote_item q
        LEFT JOIN srm_tenant_supplier_info si ON q.tenant_supplier_id = si.id
        LEFT JOIN srm_tender_supplier_response sr ON q.tenant_supplier_id = sr.tenant_supplier_id
            AND q.section_id = sr.section_id
            AND q.notice_id = sr.notice_id
        WHERE q.project_id = #{projectId}
            AND q.section_id = #{sectionId}
            AND q.notice_id = #{noticeId}
            <if test="supplierName != null and supplierName != ''">
                AND si.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
            </if>
        GROUP BY q.tenant_supplier_id, q.round_no, si.supplier_name, sr.contact_person,
                 sr.contact_phone, sr.quote_status, q.quote_ip, q.quote_time
        ORDER BY q.round_no, SUM(q.quote_amount) ASC
    </select>

    <select id="queryQuoteDetailByMaterial" resultType="com.ylz.saas.resp.SrmTenderQuoteDetailByMaterialRoundResp$MaterialQuoteInfo">
        SELECT
            q.id AS quoteItemId,
            q.material_code AS materialCode,
            q.material_name AS materialName,
            q.spec_model AS specModel,
            q.unit AS unit,
            q.required_quantity AS requiredQuantity,
            q.available_quantity AS availableQuantity,
            q.quote_price AS quotePrice,
            q.quote_amount AS quoteAmount,
            q.quote_time AS quoteTime,
            q.quote_ip AS quoteIp,
            q.round_no AS roundNo
        FROM srm_tender_bidder_quote_item q
        WHERE q.project_id = #{projectId}
            AND q.section_id = #{sectionId}
            AND q.notice_id = #{noticeId}
            AND q.del_flag = 0
            <if test="materialName != null and materialName != ''">
                AND q.material_name LIKE CONCAT('%', #{materialName}, '%')
            </if>
            <if test="materialCode != null and materialCode != ''">
                AND q.material_code LIKE CONCAT('%', #{materialCode}, '%')
            </if>
        ORDER BY q.round_no, q.material_code, q.material_name
    </select>

    <select id="queryQuoteDetailByMaterialWithSuppliers" resultType="com.ylz.saas.resp.SrmTenderQuoteDetailByMaterialRoundResp$MaterialQuoteInfo">
        SELECT
            q.id AS quoteItemId,
            q.material_code AS materialCode,
            q.material_name AS materialName,
            q.spec_model AS specModel,
            q.unit AS unit,
            q.required_quantity AS requiredQuantity,
            q.available_quantity AS availableQuantity,
            q.quote_price AS quotePrice,
            q.quote_amount AS quoteAmount,
            q.quote_time AS quoteTime,
            q.quote_ip AS quoteIp,
            q.round_no AS roundNo
        FROM srm_tender_bidder_quote_item q
        WHERE q.project_id = #{projectId}
            AND q.section_id = #{sectionId}
            AND q.notice_id = #{noticeId}
            AND q.del_flag = 0
            AND q.tenant_supplier_id IN
            <foreach item="item" collection="validSupplierIds" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test="materialName != null and materialName != ''">
                AND q.material_name LIKE CONCAT('%', #{materialName}, '%')
            </if>
            <if test="materialCode != null and materialCode != ''">
                AND q.material_code LIKE CONCAT('%', #{materialCode}, '%')
            </if>
        ORDER BY q.round_no, q.material_code, q.material_name
    </select>

</mapper>
