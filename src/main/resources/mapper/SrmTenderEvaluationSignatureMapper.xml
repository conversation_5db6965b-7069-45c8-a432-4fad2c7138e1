<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderEvaluationSignatureMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderEvaluationSignature">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
        <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
        <result property="evaluationId" column="evaluation_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="signatureStatus" column="signature_status" jdbcType="VARCHAR"/>
        <result property="signatureTime" column="signature_time" jdbcType="TIMESTAMP"/>
        <result property="signatureComment" column="signature_comment" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,project_id,notice_id,section_id,
        evaluation_id,user_id,user_name,signature_status,signature_time,
        signature_comment,del_flag,create_by,create_by_name,create_time,
        update_by,update_by_name,update_time
    </sql>



    <!-- 根据标段ID和用户ID查询签名记录 -->
    <select id="getBySectionIdAndUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM srm_tender_evaluation_signature
        WHERE section_id = #{sectionId}
          AND user_id = #{userId}
          AND del_flag = 0
        LIMIT 1
    </select>

    <!-- 统计标段下已签名的人数 -->
    <select id="countSignedBySectionId" resultType="int">
        SELECT COUNT(*)
        FROM srm_tender_evaluation_signature
        WHERE section_id = #{sectionId}
          AND signature_status = 'SIGNED'
          AND del_flag = 0
    </select>

    <!-- 统计标段下总的评标人员数 -->
    <select id="countTotalBySectionId" resultType="int">
        SELECT COUNT(*)
        FROM srm_tender_evaluation_signature
        WHERE section_id = #{sectionId}
          AND del_flag = 0
    </select>

</mapper>
