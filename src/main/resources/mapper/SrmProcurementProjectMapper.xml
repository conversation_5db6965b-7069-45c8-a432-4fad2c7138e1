<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmProcurementProjectMapper">



    <!-- 供应商分页查询（禁用数据权限控制） -->
    <select id="selectPageForSupplier" resultType="com.ylz.saas.entity.SrmProcurementProject">
        SELECT
           *
        FROM srm_procurement_project
        ${ew.customSqlSegment}
    </select>

    <!-- 小程序分页查询（关联多表查询） -->
    <select id="pageForApplet" resultType="com.ylz.saas.entity.SrmProcurementProject">
        SELECT DISTINCT
            pp.id,
            pp.tenant_id,
            pp.dept_id,
            pp.purchase_dept_id,
            pp.buyer_dept_id,
            pp.project_code,
            pp.project_name,
            pp.service_type_id,
            pp.sourcing_type,
            pp.sourcing_method,
            pp.invite_method,
            pp.invite_receipt,
            pp.province,
            pp.city,
            pp.district,
            pp.address,
            pp.budget_amount,
            pp.purchase_reason,
            pp.supplier_requirement,
            pp.project_desc,
            pp.agency_org_id,
            pp.agency_org_name,
            pp.multi_section,
            pp.pre_qualification,
            pp.register_start_time,
            pp.register_end_time,
            pp.bid_open_time,
            pp.progress_status,
            pp.status,
            pp.del_flag,
            pp.create_by_id,
            pp.create_by,
            pp.create_by_name,
            pp.create_time,
            pp.update_by_id,
            pp.update_by,
            pp.update_by_name,
            pp.update_time,
            pp.quote_start_time,
            pp.quote_end_time,
            ppi.material_code,
            ppi.material_name,
            ppi.unit,
            ppi.required_quantity,
            ppi.usage_location_id,
            bul.location_name AS usageLocationName,
            CONCAT(bul.province, '/', bul.city, '/', bul.district) AS usageLocationRegion,
            bul.address AS usageLocationAddress,
            tn.notice_title AS demandName,
            spi.type bizType,
            spi.approval_status
        FROM srm_procurement_project pp
        LEFT JOIN srm_procurement_project_item ppi ON pp.id = ppi.project_id AND ppi.del_flag = 0
        LEFT JOIN base_usage_location bul ON ppi.usage_location_id = bul.id AND bul.del_flag = 0
        LEFT JOIN srm_tender_notice tn ON pp.id = tn.project_id AND tn.del_flag = 0
        left join srm_process_instance spi ON pp.id = spi.biz_id AND spi.del_flag = 0
        WHERE pp.del_flag = 0
            <if test="req.searchContent != null and req.searchContent != ''">
                AND (
                    pp.project_code LIKE CONCAT('%', #{req.searchContent}, '%')
                    OR ppi.material_name LIKE CONCAT('%', #{req.searchContent}, '%')
                    OR tn.notice_title LIKE CONCAT('%', #{req.searchContent}, '%')
                    OR bul.location_name LIKE CONCAT('%', #{req.searchContent}, '%')
                )
            </if>
            <if test="req.progressStatus != null and req.progressStatus.size() > 0">
                AND pp.progress_status IN
                <foreach collection="req.progressStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="req.approveStatus != null and req.approveStatus.size() > 0">
                AND pp.status IN
                <foreach collection="req.approveStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="req.sourcingType != null and req.sourcingType.name() != null and req.sourcingType.name() != ''">
                AND pp.sourcing_type = #{req.sourcingType}
            </if>
            <if test="req.nodeStatus != null and req.nodeStatus.size() > 0">
                AND
                <foreach collection="req.nodeStatus" item="status" open="(" separator="or" close=")">
                    <if test="status.name() == 'INVITE_AUDITING'">
                        (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVING')
                    </if>
                    <if test="status.name() == 'INVITE_REVOKE'">
                        (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                    </if>
                    <if test="status.name() == 'INVITE_REJECTED'">
                        (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                    </if>
                    <if test="status.name() == 'INVITE_APPROVED'">
                        (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE')
                    </if>

                    <if test="status.name() == 'NOTICE_AUDITING'">
                        (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVING')
                    </if>
                    <if test="status.name() == 'NOTICE_REVOKE'">
                        (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                    </if>
                    <if test="status.name() == 'NOTICE_REJECTED'">
                        (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                    </if>
                    <if test="status.name() == 'NOTICE_APPROVED'">
                        (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE')
                    </if>

                    <if test="status.name() == 'ENSILAGE_AUDITING'">
                        (spi.type = 'SRM_TENDER_ENSILAGE_AUDIT' and spi.approval_status = 'APPROVING')
                    </if>
                    <if test="status.name() == 'ENSILAGE_REVOKE'">
                        (spi.type = 'SRM_TENDER_ENSILAGE_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                    </if>
                    <if test="status.name() == 'ENSILAGE_REJECTED'">
                        (spi.type = 'SRM_TENDER_ENSILAGE_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                    </if>
                    <if test="status.name() == 'ENSILAGE_APPROVED'">
                        (spi.type = 'SRM_TENDER_ENSILAGE_AUDIT' and spi.approval_status = 'APPROVE')
                    </if>

                    <if test="status.name() == 'TENDER_DOC_AUDITING'">
                        (spi.type = 'SRM_TENDER_DOC_AUDIT' and spi.approval_status = 'APPROVING')
                    </if>
                    <if test="status.name() == 'TENDER_DOC_REVOKE'">
                        (spi.type = 'SRM_TENDER_DOC_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                    </if>
                    <if test="status.name() == 'TENDER_DOC_REJECTED'">
                        (spi.type = 'SRM_TENDER_DOC_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                    </if>
                    <if test="status.name() == 'TENDER_DOC_APPROVED'">
                        (spi.type = 'SRM_TENDER_DOC_AUDIT' and spi.approval_status = 'APPROVE')
                    </if>

                    <if test="status.name() == 'CHANGE_AUDITING'">
                        (spi.type = 'SRM_CHANGE_AUDIT' and spi.approval_status = 'APPROVING')
                    </if>
                    <if test="status.name() == 'CHANGE_REVOKE'">
                        (spi.type = 'SRM_CHANGE_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                    </if>
                    <if test="status.name() == 'CHANGE_REJECTED'">
                        (spi.type = 'SRM_CHANGE_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                    </if>
                    <if test="status.name() == 'CHANGE_APPROVED'">
                        (spi.type = 'SRM_CHANGE_AUDIT' and spi.approval_status = 'APPROVE')
                    </if>

                    <if test="status.name() == 'FAILED_AUDITING'">
                        (spi.type = 'SRM_TENDER_FAILED_AUDIT' and spi.approval_status = 'APPROVING')
                    </if>
                    <if test="status.name() == 'FAILED_REVOKE'">
                        (spi.type = 'SRM_TENDER_FAILED_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                    </if>
                    <if test="status.name() == 'FAILED_REJECTED'">
                        (spi.type = 'SRM_TENDER_FAILED_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                    </if>
                    <if test="status.name() == 'FAILED_APPROVED'">
                        (spi.type = 'SRM_TENDER_FAILED_AUDIT' and spi.approval_status = 'APPROVE')
                    </if>

                    <if test="status.name() == 'AWARD_AUDITING'">
                        (spi.type = 'SRM_TENDER_BID_AUDIT' and spi.approval_status = 'APPROVING')
                    </if>
                    <if test="status.name() == 'AWARD_REVOKE'">
                        (spi.type = 'SRM_TENDER_BID_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                    </if>
                    <if test="status.name() == 'AWARD_REJECTED'">
                        (spi.type = 'SRM_TENDER_BID_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                    </if>
                    <if test="status.name() == 'AWARD_APPROVED'">
                        (spi.type = 'SRM_TENDER_BID_AUDIT' and spi.approval_status = 'APPROVE')
                    </if>

                    <if test="status.name() == 'PUBLICITY_AUDITING'">
                        (spi.type = 'SRM_PUBLICITY_AUDITING' and spi.approval_status = 'APPROVING')
                    </if>
                    <if test="status.name() == 'PUBLICITY_REVOKE'">
                        (spi.type = 'SRM_PUBLICITY_AUDITING' and spi.approval_status = 'APPROVE_REVOKE')
                    </if>
                    <if test="status.name() == 'PUBLICITY_REJECTED'">
                        (spi.type = 'SRM_PUBLICITY_AUDITING' and spi.approval_status = 'APPROVE_REJECT')
                    </if>
                    <if test="status.name() == 'PUBLICITY_APPROVED'">
                        (spi.type = 'SRM_PUBLICITY_AUDITING' and spi.approval_status = 'APPROVE')
                    </if>

                    <if test="status.name() == 'PUBLIC_NOTICE_AUDITING'">
                        (spi.type = 'SRM_BID_NOTICE_AUDITING' and spi.approval_status = 'APPROVING')
                    </if>
                    <if test="status.name() == 'PUBLIC_NOTICE_REVOKE'">
                        (spi.type = 'SRM_BID_NOTICE_AUDITING' and spi.approval_status = 'APPROVE_REVOKE')
                    </if>
                    <if test="status.name() == 'PUBLIC_NOTICE_REJECTED'">
                        (spi.type = 'SRM_BID_NOTICE_AUDITING' and spi.approval_status = 'APPROVE_REJECT')
                    </if>
                    <if test="status.name() == 'PUBLIC_NOTICE_APPROVED'">
                        (spi.type = 'SRM_BID_NOTICE_AUDITING' and spi.approval_status = 'APPROVE')
                    </if>
                </foreach>
            </if>
            <if test="req.quoteStartTime != null">
                AND pp.quote_start_time >= #{req.quoteStartTime}
            </if>
            <if test="req.quoteEndTime != null">
                AND pp.quote_end_time &lt;= #{req.quoteEndTime}
            </if>
        ORDER BY pp.create_time DESC
    </select>

    <select id="pageForAppletForSupplier" resultType="com.ylz.saas.entity.SrmProcurementProject">
        SELECT DISTINCT
        pp.id,
        pp.tenant_id,
        pp.dept_id,
        pp.purchase_dept_id,
        pp.buyer_dept_id,
        pp.project_code,
        pp.project_name,
        pp.service_type_id,
        pp.sourcing_type,
        pp.sourcing_method,
        pp.invite_method,
        pp.invite_receipt,
        pp.province,
        pp.city,
        pp.district,
        pp.address,
        pp.budget_amount,
        pp.purchase_reason,
        pp.supplier_requirement,
        pp.project_desc,
        pp.agency_org_id,
        pp.agency_org_name,
        pp.multi_section,
        pp.pre_qualification,
        pp.register_start_time,
        pp.register_end_time,
        pp.bid_open_time,
        pp.progress_status,
        pp.status,
        pp.del_flag,
        pp.create_by_id,
        pp.create_by,
        pp.create_by_name,
        pp.create_time,
        pp.update_by_id,
        pp.update_by,
        pp.update_by_name,
        pp.update_time,
        pp.quote_start_time,
        pp.quote_end_time,
        ppi.material_code,
        ppi.material_name,
        ppi.unit,
        ppi.required_quantity,
        ppi.usage_location_id,
        bul.location_name AS usageLocationName,
        CONCAT(bul.province, '/', bul.city, '/', bul.district) AS usageLocationRegion,
        bul.address AS usageLocationAddress,
        tn.notice_title AS demandName,
        spi.type bizType,
        spi.approval_status
        FROM srm_procurement_project pp
        LEFT JOIN srm_procurement_project_item ppi ON pp.id = ppi.project_id AND ppi.del_flag = 0
        LEFT JOIN base_usage_location bul ON ppi.usage_location_id = bul.id AND bul.del_flag = 0
        LEFT JOIN srm_tender_notice tn ON pp.id = tn.project_id AND tn.del_flag = 0
        left join srm_process_instance spi ON pp.id = spi.biz_id AND spi.del_flag = 0
        WHERE pp.del_flag = 0
        <if test="req.searchContent != null and req.searchContent != ''">
            AND (
            pp.project_code LIKE CONCAT('%', #{req.searchContent}, '%')
            OR ppi.material_name LIKE CONCAT('%', #{req.searchContent}, '%')
            OR tn.notice_title LIKE CONCAT('%', #{req.searchContent}, '%')
            OR bul.location_name LIKE CONCAT('%', #{req.searchContent}, '%')
            )
        </if>
        <if test="req.progressStatus != null and req.progressStatus.size() > 0">
            AND pp.progress_status IN
            <foreach collection="req.progressStatus" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="req.approveStatus != null and req.approveStatus.size() > 0">
            AND pp.status IN
            <foreach collection="req.approveStatus" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="req.sourcingType != null and req.sourcingType.name() != null and req.sourcingType.name() != ''">
            AND pp.sourcing_type = #{req.sourcingType}
        </if>
        <if test="req.nodeStatus != null and req.nodeStatus.size() > 0">
            AND
            <foreach collection="req.nodeStatus" item="status" open="(" separator="or" close=")">
                <if test="status.name() == 'INVITE_AUDITING'">
                    (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVING')
                </if>
                <if test="status.name() == 'INVITE_REVOKE'">
                    (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                </if>
                <if test="status.name() == 'INVITE_REJECTED'">
                    (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                </if>
                <if test="status.name() == 'INVITE_APPROVED'">
                    (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE')
                </if>

                <if test="status.name() == 'NOTICE_AUDITING'">
                    (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVING')
                </if>
                <if test="status.name() == 'NOTICE_REVOKE'">
                    (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                </if>
                <if test="status.name() == 'NOTICE_REJECTED'">
                    (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                </if>
                <if test="status.name() == 'NOTICE_APPROVED'">
                    (spi.type = 'SRM_TENDER_NOTICE_AUDIT' and spi.approval_status = 'APPROVE')
                </if>

                <if test="status.name() == 'ENSILAGE_AUDITING'">
                    (spi.type = 'SRM_TENDER_ENSILAGE_AUDIT' and spi.approval_status = 'APPROVING')
                </if>
                <if test="status.name() == 'ENSILAGE_REVOKE'">
                    (spi.type = 'SRM_TENDER_ENSILAGE_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                </if>
                <if test="status.name() == 'ENSILAGE_REJECTED'">
                    (spi.type = 'SRM_TENDER_ENSILAGE_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                </if>
                <if test="status.name() == 'ENSILAGE_APPROVED'">
                    (spi.type = 'SRM_TENDER_ENSILAGE_AUDIT' and spi.approval_status = 'APPROVE')
                </if>

                <if test="status.name() == 'TENDER_DOC_AUDITING'">
                    (spi.type = 'SRM_TENDER_DOC_AUDIT' and spi.approval_status = 'APPROVING')
                </if>
                <if test="status.name() == 'TENDER_DOC_REVOKE'">
                    (spi.type = 'SRM_TENDER_DOC_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                </if>
                <if test="status.name() == 'TENDER_DOC_REJECTED'">
                    (spi.type = 'SRM_TENDER_DOC_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                </if>
                <if test="status.name() == 'TENDER_DOC_APPROVED'">
                    (spi.type = 'SRM_TENDER_DOC_AUDIT' and spi.approval_status = 'APPROVE')
                </if>

                <if test="status.name() == 'CHANGE_AUDITING'">
                    (spi.type = 'SRM_CHANGE_AUDIT' and spi.approval_status = 'APPROVING')
                </if>
                <if test="status.name() == 'CHANGE_REVOKE'">
                    (spi.type = 'SRM_CHANGE_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                </if>
                <if test="status.name() == 'CHANGE_REJECTED'">
                    (spi.type = 'SRM_CHANGE_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                </if>
                <if test="status.name() == 'CHANGE_APPROVED'">
                    (spi.type = 'SRM_CHANGE_AUDIT' and spi.approval_status = 'APPROVE')
                </if>

                <if test="status.name() == 'FAILED_AUDITING'">
                    (spi.type = 'SRM_TENDER_FAILED_AUDIT' and spi.approval_status = 'APPROVING')
                </if>
                <if test="status.name() == 'FAILED_REVOKE'">
                    (spi.type = 'SRM_TENDER_FAILED_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                </if>
                <if test="status.name() == 'FAILED_REJECTED'">
                    (spi.type = 'SRM_TENDER_FAILED_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                </if>
                <if test="status.name() == 'FAILED_APPROVED'">
                    (spi.type = 'SRM_TENDER_FAILED_AUDIT' and spi.approval_status = 'APPROVE')
                </if>

                <if test="status.name() == 'AWARD_AUDITING'">
                    (spi.type = 'SRM_TENDER_BID_AUDIT' and spi.approval_status = 'APPROVING')
                </if>
                <if test="status.name() == 'AWARD_REVOKE'">
                    (spi.type = 'SRM_TENDER_BID_AUDIT' and spi.approval_status = 'APPROVE_REVOKE')
                </if>
                <if test="status.name() == 'AWARD_REJECTED'">
                    (spi.type = 'SRM_TENDER_BID_AUDIT' and spi.approval_status = 'APPROVE_REJECT')
                </if>
                <if test="status.name() == 'AWARD_APPROVED'">
                    (spi.type = 'SRM_TENDER_BID_AUDIT' and spi.approval_status = 'APPROVE')
                </if>

                <if test="status.name() == 'PUBLICITY_AUDITING'">
                    (spi.type = 'SRM_PUBLICITY_AUDITING' and spi.approval_status = 'APPROVING')
                </if>
                <if test="status.name() == 'PUBLICITY_REVOKE'">
                    (spi.type = 'SRM_PUBLICITY_AUDITING' and spi.approval_status = 'APPROVE_REVOKE')
                </if>
                <if test="status.name() == 'PUBLICITY_REJECTED'">
                    (spi.type = 'SRM_PUBLICITY_AUDITING' and spi.approval_status = 'APPROVE_REJECT')
                </if>
                <if test="status.name() == 'PUBLICITY_APPROVED'">
                    (spi.type = 'SRM_PUBLICITY_AUDITING' and spi.approval_status = 'APPROVE')
                </if>

                <if test="status.name() == 'PUBLIC_NOTICE_AUDITING'">
                    (spi.type = 'SRM_BID_NOTICE_AUDITING' and spi.approval_status = 'APPROVING')
                </if>
                <if test="status.name() == 'PUBLIC_NOTICE_REVOKE'">
                    (spi.type = 'SRM_BID_NOTICE_AUDITING' and spi.approval_status = 'APPROVE_REVOKE')
                </if>
                <if test="status.name() == 'PUBLIC_NOTICE_REJECTED'">
                    (spi.type = 'SRM_BID_NOTICE_AUDITING' and spi.approval_status = 'APPROVE_REJECT')
                </if>
                <if test="status.name() == 'PUBLIC_NOTICE_APPROVED'">
                    (spi.type = 'SRM_BID_NOTICE_AUDITING' and spi.approval_status = 'APPROVE')
                </if>
            </foreach>
        </if>
        <if test="req.quoteStartTime != null">
            AND pp.quote_start_time >= #{req.quoteStartTime}
        </if>
        <if test="req.quoteEndTime != null">
            AND pp.quote_end_time &lt;= #{req.quoteEndTime}
        </if>
        ORDER BY pp.create_time DESC
    </select>
</mapper>
