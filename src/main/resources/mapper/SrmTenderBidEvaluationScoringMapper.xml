<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderBidEvaluationScoringMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderBidEvaluationScoring">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
        <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="scoringDetailId" column="scoring_detail_id" jdbcType="BIGINT"/>
        <result property="responseId" column="response_id" jdbcType="BIGINT"/>
        <result property="tenantSupplierId" column="tenant_supplier_id" jdbcType="BIGINT"/>
        <result property="score" column="score" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="isConform" column="is_conform" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="conclusion" column="conclusion" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="ScoringQueryResultMap" type="com.ylz.saas.resp.SrmTenderBidEvaluationScoringQueryResp">
        <result property="scoringId" column="scoring_id" jdbcType="BIGINT"/>
        <result property="responseId" column="response_id" jdbcType="BIGINT"/>
        <result property="standardId" column="standard_id" jdbcType="BIGINT"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
        <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="expertName" column="expert_name" jdbcType="VARCHAR"/>
        <result property="expertCode" column="expert_code" jdbcType="VARCHAR"/>
        <result property="expertCategory" column="expert_category" jdbcType="VARCHAR"/>
        <result property="role" column="role" jdbcType="VARCHAR"/>
        <result property="tenantSupplierId" column="tenant_supplier_id" jdbcType="BIGINT"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="nodeName" column="node_name" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="itemDescription" column="item_description" jdbcType="VARCHAR"/>
        <result property="totalScore" column="total_score" jdbcType="INTEGER"/>
        <result property="maxScore" column="max_score" jdbcType="INTEGER"/>
        <result property="minScore" column="min_score" jdbcType="INTEGER"/>
        <result property="weight" column="weight" jdbcType="DOUBLE"/>
        <result property="score" column="score" jdbcType="DECIMAL"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="isConform" column="is_conform" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="scoringType" column="scoring_type" jdbcType="VARCHAR"/>
        <result property="conclusion" column="conclusion" jdbcType="VARCHAR"/>
        <result property="isScored" column="is_scored" jdbcType="BOOLEAN"/>
        <result property="quoteStatus" column="quote_status" jdbcType="VARCHAR"/>
        <result property="scoringCreateTime" column="scoring_create_time" jdbcType="TIMESTAMP"/>
        <result property="scoringUpdateTime" column="scoring_update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,project_id,notice_id,section_id,evaluation_id,
        user_id,scoring_detail_id,response_id,tenant_supplier_id,
        score,status,is_conform,type,conclusion,
        del_flag,create_by,create_by_name,create_time,
        update_by,update_by_name,update_time
    </sql>

    <!-- 查询专家对某节点的项目打分情况 -->
    <select id="getExpertScoringByNode" resultMap="ScoringQueryResultMap">
        SELECT
        es.id as standard_id,
        es.project_id,
        es.notice_id,
        es.section_id,
        es.node_name,
        es.item_name,
        es.item_description,
        es.total_score,
        es.max_score,
        es.min_score,
        es.weight,
        es.type,
        #{req.userId} as user_id,
        be.name as expert_name,
        be.expert_code,
        be.expert_category,
        sr.tenant_supplier_id,
        sr.supplier_name,
        sr.quote_status,
        sr.id as response_id,
        s.id as scoring_id,
        s.score,
        s.status,
        s.is_conform,
        s.conclusion,
        CASE WHEN s.id IS NOT NULL THEN true ELSE false END as is_scored,
        s.create_time as scoring_create_time,
        s.update_time as scoring_update_time
        FROM srm_tender_evaluation_standard es
        CROSS JOIN (
            SELECT sr.id, sr.tenant_supplier_id, sr.supplier_name, sr.quote_status
            FROM srm_tender_supplier_response sr
            WHERE sr.project_id = #{req.projectId}
            AND sr.notice_id = #{req.noticeId}
            AND sr.section_id = #{req.sectionId}
            AND sr.quote_status = 'COMPLETED'
            AND sr.del_flag = 0
            <if test="req.tenantSupplierId != null">
                AND sr.tenant_supplier_id = #{req.tenantSupplierId}
            </if>
        ) sr
        <!-- 查询指定专家：先验证该专家是否为评标小组成员 -->
        INNER JOIN srm_project_member pm ON pm.business_id = #{req.evaluationId}
        AND pm.member_type = 'EVALUATION_MEMBER'
        AND pm.user_id = #{req.userId}
        AND pm.del_flag = 0
        LEFT JOIN base_expert be ON be.user_id = pm.user_id
        LEFT JOIN srm_tender_bid_evaluation_scoring s ON es.id = s.scoring_detail_id
        AND s.user_id = pm.user_id
        AND sr.tenant_supplier_id = s.tenant_supplier_id
        AND s.evaluation_id = #{req.evaluationId}
        AND s.current_round = #{req.currentRound}
        AND (s.scoring_type = 'EXPERT_SCORING' OR s.scoring_type IS NULL)
        AND s.del_flag = 0
        WHERE es.project_id = #{req.projectId}
        AND be.del_flag = 0
        AND es.notice_id = #{req.noticeId}
        AND es.section_id = #{req.sectionId}
        <if test="req.nodeType != null and req.nodeType != ''">
            AND es.type = #{req.nodeType}
        </if>
        <if test="req.nodeName != null and req.nodeName != ''">
            AND es.node_name = #{req.nodeName}
        </if>
        AND es.del_flag = 0
        ORDER BY sr.supplier_name, be.name, es.sort_order, es.create_time
    </select>

    <!-- 根据节点名称撤回专家评分结果 -->
    <update id="revokeScoringResultByNode">
        UPDATE srm_tender_bid_evaluation_scoring s
        SET s.status = 'WAIT_SUBMIT',
            s.update_time = NOW()
        WHERE s.user_id = #{req.userId}
          AND s.project_id = #{req.projectId}
          AND s.notice_id = #{req.noticeId}
          AND s.section_id = #{req.sectionId}
          AND s.evaluation_id = #{req.evaluationId}
          AND s.del_flag = 0
          AND s.scoring_detail_id IN (
              SELECT es.id
              FROM srm_tender_evaluation_standard es
              WHERE es.project_id = #{req.projectId}
                AND es.notice_id = #{req.noticeId}
                AND es.section_id = #{req.sectionId}
                AND es.node_name = #{req.nodeName}
                AND es.del_flag = 0
          )
    </update>

    <!-- 查询节点下所有专家对各个供应商的评审/评分详情 -->
    <select id="getNodeEvaluationDetails" resultMap="ScoringQueryResultMap">
        SELECT
        es.id as standard_id,
        es.project_id,
        es.notice_id,
        es.section_id,
        es.node_name,
        es.item_name,
        es.item_description,
        es.max_score,
        es.min_score,
        es.weight,
        es.type,
        pm.user_id as user_id,
        be.name as expert_name,
        be.expert_code,
        be.expert_category,
        pm.role,
        sr.tenant_supplier_id,
        sr.supplier_name,
        sr.quote_status,
        sr.id as response_id,
        s.id as scoring_id,
        s.score,
        s.status,
        s.is_conform,
        s.type,
        s.scoring_type,
        s.conclusion,
        CASE WHEN s.id IS NOT NULL THEN true ELSE false END as is_scored,
        s.create_time as scoring_create_time,
        s.update_time as scoring_update_time
        FROM srm_tender_evaluation_standard es
        CROSS JOIN (
            SELECT sr.id, sr.tenant_supplier_id, sr.supplier_name, sr.quote_status
            FROM srm_tender_supplier_response sr
            WHERE sr.project_id = #{req.projectId}
            AND sr.notice_id = #{req.noticeId}
            AND sr.section_id = #{req.sectionId}
            AND sr.quote_status = 'COMPLETED'
            AND sr.del_flag = 0
        ) sr
        CROSS JOIN (
            SELECT pm.user_id, pm.role
            FROM srm_project_member pm
            WHERE pm.business_id = #{req.evaluationId}
            AND pm.member_type = 'EVALUATION_MEMBER'
            AND pm.del_flag = 0
        ) pm
        LEFT JOIN base_expert be ON be.user_id = pm.user_id
        LEFT JOIN srm_tender_bid_evaluation_scoring s ON es.id = s.scoring_detail_id
        AND s.user_id = pm.user_id
        AND sr.tenant_supplier_id = s.tenant_supplier_id
        AND s.evaluation_id = #{req.evaluationId}
        AND s.current_round = #{req.currentRound}
        AND s.del_flag = 0
        WHERE es.project_id = #{req.projectId}
        AND be.del_flag = 0
        AND es.notice_id = #{req.noticeId}
        AND es.section_id = #{req.sectionId}
        <if test="req.nodeName != null and req.nodeName != ''">
            AND es.node_name = #{req.nodeName}
        </if>
        AND es.del_flag = 0
        ORDER BY sr.supplier_name, pm.role DESC, be.name, es.sort_order, es.create_time
    </select>


    <select id="countExpertEvaluationCompleted" resultType="java.lang.Integer">
        SELECT count(*) from srm_tender_supplier_response tr
          join srm_tender_bid_evaluation te
               on tr.project_id = te.project_id and tr.notice_id = te.notice_id and tr.section_id = te.section_id
          join srm_project_member pm
               on te.project_id = pm.project_id and pm.business_id = te.id and pm.member_type = 'EVALUATION_MEMBER'
          left join srm_tender_bid_evaluation_scoring tes
                    on pm.project_id = tes.project_id and pm.user_id = tes.user_id and tes.evaluation_id = te.id
        where tr.notice_id = #{noticeId}
          AND tr.del_flag = 0
          and te.del_flag = 0
          and pm.del_flag = 0
          and (( tes.del_flag = 0 AND tes.scoring_type = 'EXPERT_SCORING' AND tes.STATUS = 'WAIT_SUBMIT')
                OR tes.STATUS IS NULL)
    </select>



    <!-- 检查指定公告下的标段是否配置了评标标准 -->
    <select id="hasEvaluationStandards" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN true ELSE false END
        FROM srm_tender_evaluation_standard es
        INNER JOIN srm_procurement_project_section s ON es.section_id = s.id
        INNER JOIN srm_procurement_project p ON s.project_id = p.id
        INNER JOIN srm_tender_notice tn ON p.id = tn.project_id
        WHERE tn.id = #{noticeId}
          AND es.del_flag = 0
          AND s.del_flag = 0
          AND p.del_flag = 0
          AND tn.del_flag = 0
    </select>

    <!-- 检查指定评标委员会是否配置了评标标准 -->
    <select id="hasEvaluationStandardsByEvaluationId" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(*) > 0 THEN true ELSE false END
        FROM srm_tender_evaluation_standard es
        INNER JOIN srm_tender_bid_evaluation te ON es.section_id = te.section_id
            AND es.project_id = te.project_id
            AND es.notice_id = te.notice_id
        WHERE te.id = #{evaluationId}
          AND es.del_flag = 0
          AND te.del_flag = 0
    </select>

    <!-- 检查指定标段是否已完成评标 -->
    <select id="isSectionEvaluationCompleted" resultType="java.lang.Boolean">
        SELECT CASE
            WHEN NOT EXISTS (
                SELECT 1
                FROM srm_tender_evaluation_standard es
                INNER JOIN srm_tender_bid_evaluation te ON es.section_id = te.section_id
                    AND es.project_id = te.project_id
                    AND es.notice_id = te.notice_id
                WHERE te.section_id = #{sectionId}
                  AND es.del_flag = 0
                  AND te.del_flag = 0
            ) THEN true
            WHEN NOT EXISTS (
                SELECT 1
                FROM srm_tender_supplier_response tr
                JOIN srm_tender_bid_evaluation te ON tr.project_id = te.project_id
                    AND tr.notice_id = te.notice_id
                    AND tr.section_id = te.section_id
                JOIN srm_project_member pm ON te.project_id = pm.project_id
                    AND pm.business_id = te.id
                    AND pm.member_type = 'EVALUATION_MEMBER'
                LEFT JOIN srm_tender_bid_evaluation_scoring tes ON pm.project_id = tes.project_id
                    AND pm.user_id = tes.user_id
                    AND tes.evaluation_id = te.id
                WHERE te.section_id = #{sectionId}
                  AND tr.del_flag = 0
                  AND te.del_flag = 0
                  AND pm.del_flag = 0
                  AND ((tes.del_flag = 0 AND tes.scoring_type = 'EXPERT_SCORING' AND tes.status = 'WAIT_SUBMIT')
                       OR tes.status IS NULL)
            ) THEN true
            ELSE false
        END
    </select>

</mapper>
