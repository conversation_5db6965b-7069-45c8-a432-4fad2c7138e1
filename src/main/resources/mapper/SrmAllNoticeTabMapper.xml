<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmAllNoticeTabMapper">

    <!-- 招标公告分页查询 -->
    <select id="selectTenderNoticePage" resultType="com.ylz.saas.resp.SrmAllNoticePageResp">
        SELECT 
            tn.id,
            tn.project_id AS projectId,
            tn.notice_content AS noticeContent,
            pp.sourcing_type AS sourcingMethod,
            tn.notice_title AS noticeTitle,
            pp.project_code AS projectCode,
            pp.project_name AS projectName,
            pp.invite_method AS publicCategory,
            (case when tn.status = 'PUBLISHED' then  DATE_FORMAT(tn.update_time, '%Y-%m-%d %H:%i:%s')
                else null end) AS publishTime,
            DATE_FORMAT(tn.register_start_time, '%Y-%m-%d %H:%i:%s') AS registrationStartTime,
            DATE_FORMAT(tn.register_end_time, '%Y-%m-%d %H:%i:%s') AS registrationEndTime,
            DATE_FORMAT(tn.bid_open_time, '%Y-%m-%d %H:%i:%s') AS bidOpeningTime,
            tn.contact_person AS contactPerson,
            tn.contact_phone AS contactPhone
        FROM srm_tender_notice tn
        INNER JOIN srm_procurement_project pp ON tn.project_id = pp.id
        WHERE tn.del_flag = 0 
            AND pp.del_flag = 0
            AND pp.invite_method = 'PUBLICITY'
            <if test="req.noticeTitle != null and req.noticeTitle != ''">
                AND tn.notice_title LIKE CONCAT('%', #{req.noticeTitle}, '%')
            </if>
            <if test="req.publishStartTime != null">
                AND tn.publish_notice_start_time >= #{req.publishStartTime}
            </if>
            <if test="req.publishEndTime != null">
                AND tn.publish_notice_start_time &lt;= #{req.publishEndTime}
            </if>
            <if test="req.searchContent != null and req.searchContent != ''">
                AND (pp.project_code LIKE CONCAT('%', #{req.searchContent}, '%') 
                     OR pp.project_name LIKE CONCAT('%', #{req.searchContent}, '%'))
            </if>
            <if test="req.sourcingType != null and req.sourcingType.size() > 0">
                AND pp.sourcing_type IN
                <foreach collection="req.sourcingType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="req.bidOpenStartTime != null">
                AND tn.bid_open_time >= #{req.bidOpenStartTime}
            </if>
            <if test="req.bidOpenEndTime != null">
                AND tn.bid_open_time &lt;= #{req.bidOpenEndTime}
            </if>
            <if test="req.projectLeaderName != null and req.projectLeaderName != ''">
                AND EXISTS (
                    SELECT 1 FROM srm_project_member pm
                    INNER JOIN sys_user su ON pm.user_id = su.user_id
                    WHERE pm.project_id = pp.id
                        and pm.member_type = 'PROJECT_MEMBER' and pm.role = 'PROJECT_LEADER'
                        AND pm.del_flag = 0
                        AND su.name LIKE CONCAT('%', #{req.projectLeaderName}, '%')
                )
            </if>
            <if test="req.noticeStatus != null and req.noticeStatus.name() != null and req.noticeStatus.name() != ''">
                AND tn.status = #{req.noticeStatus}
            </if>
        ORDER BY tn.update_time DESC
    </select>

    <!-- 邀请函分页查询 -->
    <select id="selectInvitePage" resultType="com.ylz.saas.resp.SrmAllInvitePageResp">
        SELECT
            tn.id,
            tn.project_id AS projectId,
            tn.notice_content AS noticeContent,
            pp.sourcing_type AS sourcingMethod,
            tn.notice_title AS noticeTitle,
            pp.project_code AS projectCode,
            pp.project_name AS projectName,
            pp.invite_method AS publicCategory,
            (case when tn.status = 'PUBLISHED' then  DATE_FORMAT(tn.update_time, '%Y-%m-%d %H:%i:%s')
            else null end) AS publishTime,
            DATE_FORMAT(tn.register_start_time, '%Y-%m-%d %H:%i:%s') AS registrationStartTime,
            DATE_FORMAT(tn.register_end_time, '%Y-%m-%d %H:%i:%s') AS registrationEndTime,
            DATE_FORMAT(tn.bid_open_time, '%Y-%m-%d %H:%i:%s') AS bidOpeningTime,
            tn.contact_person AS contactPerson,
            tn.contact_phone AS contactPhone
        FROM srm_tender_notice tn
        INNER JOIN srm_procurement_project pp ON tn.project_id = pp.id
        WHERE tn.del_flag = 0
        AND pp.del_flag = 0
        AND pp.invite_method = 'INVITE'
            <if test="req.noticeTitle != null and req.noticeTitle != ''">
                AND tn.notice_title LIKE CONCAT('%', #{req.noticeTitle}, '%')
            </if>
            <if test="req.publishStartTime != null">
                AND tn.publish_notice_start_time >= #{req.publishStartTime}
            </if>
            <if test="req.publishEndTime != null">
                AND tn.publish_notice_start_time &lt;= #{req.publishEndTime}
            </if>
            <if test="req.searchContent != null and req.searchContent != ''">
                AND (pp.project_code LIKE CONCAT('%', #{req.searchContent}, '%') 
                     OR pp.project_name LIKE CONCAT('%', #{req.searchContent}, '%'))
            </if>
            <if test="req.sourcingType != null and req.sourcingType.size() > 0">
                AND pp.sourcing_type IN
                <foreach collection="req.sourcingType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="req.bidOpenStartTime != null">
                AND tn.bid_open_time >= #{req.bidOpenStartTime}
            </if>
            <if test="req.bidOpenEndTime != null">
                AND tn.bid_open_time &lt;= #{req.bidOpenEndTime}
            </if>
            <if test="req.projectLeaderName != null and req.projectLeaderName != ''">
                AND EXISTS (
                    SELECT 1 FROM srm_project_member pm
                    INNER JOIN sys_user su ON pm.user_id = su.user_id
                    WHERE pm.project_id = pp.id
                        and pm.member_type = 'PROJECT_MEMBER' and pm.role = 'PROJECT_LEADER'
                        AND pm.del_flag = 0
                        AND su.name LIKE CONCAT('%', #{req.projectLeaderName}, '%')
                )
            </if>
            <if test="req.noticeStatus != null and req.noticeStatus.name() != null and req.noticeStatus.name() != ''">
                AND tn.status = #{req.noticeStatus}
            </if>
        ORDER BY tn.update_time DESC
    </select>


    <select id="inviteAndNoticePage" resultType="com.ylz.saas.resp.SrmAllNoticePageResp">
        SELECT
        tn.id,
        tn.project_id AS projectId,
        tn.notice_content AS noticeContent,
        pp.sourcing_type AS sourcingMethod,
        tn.notice_title AS noticeTitle,
        pp.project_code AS projectCode,
        pp.project_name AS projectName,
        pp.invite_method AS publicCategory,
        (case when tn.status = 'PUBLISHED' then  DATE_FORMAT(tn.update_time, '%Y-%m-%d %H:%i:%s')
        else null end) AS publishTime,
        DATE_FORMAT(tn.register_start_time, '%Y-%m-%d %H:%i:%s') AS registrationStartTime,
        DATE_FORMAT(tn.register_end_time, '%Y-%m-%d %H:%i:%s') AS registrationEndTime,
        DATE_FORMAT(tn.bid_open_time, '%Y-%m-%d %H:%i:%s') AS bidOpeningTime,
        tn.contact_person AS contactPerson,
        tn.contact_phone AS contactPhone,
        pp.dept_id,
        dept.name AS deptName
        FROM srm_tender_notice tn
        INNER JOIN srm_procurement_project pp ON tn.project_id = pp.id
        left join sys_dept dept on pp.dept_id = dept.dept_id
        WHERE tn.del_flag = 0
        AND pp.del_flag = 0
        <if test="req.noticeTitle != null and req.noticeTitle != ''">
            AND tn.notice_title LIKE CONCAT('%', #{req.noticeTitle}, '%')
        </if>
        <if test="req.publishStartTime != null">
            AND tn.publish_notice_start_time >= #{req.publishStartTime}
        </if>
        <if test="req.publishEndTime != null">
            AND tn.publish_notice_start_time &lt;= #{req.publishEndTime}
        </if>
        <if test="req.searchContent != null and req.searchContent != ''">
            AND (pp.project_code LIKE CONCAT('%', #{req.searchContent}, '%')
            OR pp.project_name LIKE CONCAT('%', #{req.searchContent}, '%'))
        </if>
        <if test="req.sourcingType != null and req.sourcingType.size() > 0">
            AND pp.sourcing_type IN
            <foreach collection="req.sourcingType" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="req.bidOpenStartTime != null">
            AND tn.bid_open_time >= #{req.bidOpenStartTime}
        </if>
        <if test="req.bidOpenEndTime != null">
            AND tn.bid_open_time &lt;= #{req.bidOpenEndTime}
        </if>
        <if test="req.projectLeaderName != null and req.projectLeaderName != ''">
            AND EXISTS (
            SELECT 1 FROM srm_project_member pm
            INNER JOIN sys_user su ON pm.user_id = su.user_id
            WHERE pm.project_id = pp.id
            and pm.member_type = 'PROJECT_MEMBER' and pm.role = 'PROJECT_LEADER'
            AND pm.del_flag = 0
            AND su.name LIKE CONCAT('%', #{req.projectLeaderName}, '%')
            )
        </if>
        <if test="req.noticeStatus != null and req.noticeStatus.name() != null and req.noticeStatus.name() != ''">
            AND tn.status = #{req.noticeStatus}
        </if>
        ORDER BY tn.update_time DESC
    </select>

    <!-- 公示分页查询 -->
    <select id="selectPublicityPage" resultType="com.ylz.saas.resp.SrmAllPublicityPageResp">
        SELECT
            ter.id,
            ter.notice_id,
            ter.project_id AS projectId,
            ter.publicity_content AS noticeContent,
            pp.sourcing_type AS sourcingMethod,
            pp.project_code AS projectCode,
            pp.project_name AS projectName,
            pp.invite_method AS publicCategory,
            (case when ter.publicity_status = 'PUBLISHED' then  DATE_FORMAT(ter.update_time, '%Y-%m-%d %H:%i:%s')
                else null end) publishTime,
            ter.publicity_start_time AS publicityStartTime,
            ter.publicity_end_time AS publicityEndTime,
            ter.id AS evaluationResultId,
            ter.publicity_title AS publicityTitle,
            ter.publicity_content AS publicityContent,
            ter.awarded_amount_total AS awardedAmountTotal,
            ter.tenant_supplier_id AS tenantSupplierId,
            tsi.supplier_name AS supplierName,
            ter.publicity_status AS publicityStatus,
            ter.publicity_audit_status AS publicityAuditStatus
        FROM srm_tender_evaluation_result ter
        INNER JOIN srm_procurement_project pp ON ter.project_id = pp.id
        INNER JOIN srm_tender_notice tn ON ter.notice_id = tn.id
        LEFT JOIN srm_tenant_supplier_info tsi ON ter.tenant_supplier_id = tsi.id
        WHERE ter.del_flag = 0
        AND pp.del_flag = 0
        AND tn.del_flag = 0
        AND ter.publicity_status IS NOT NULL
            <if test="req.noticeTitle != null and req.noticeTitle != ''">
                AND tn.notice_title LIKE CONCAT('%', #{req.noticeTitle}, '%')
            </if>
            <if test="req.publishStartTime != null">
                AND ter.publicity_start_time >= #{req.publishStartTime}
            </if>
            <if test="req.publishEndTime != null">
                AND ter.publicity_start_time &lt;= #{req.publishEndTime}
            </if>
            <if test="req.searchContent != null and req.searchContent != ''">
                AND (pp.project_code LIKE CONCAT('%', #{req.searchContent}, '%') 
                     OR pp.project_name LIKE CONCAT('%', #{req.searchContent}, '%'))
            </if>
            <if test="req.sourcingType != null and req.sourcingType.size() > 0">
                AND pp.sourcing_type IN
                <foreach collection="req.sourcingType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="req.projectLeaderName != null and req.projectLeaderName != ''">
                AND EXISTS (
                    SELECT 1 FROM srm_project_member pm
                    INNER JOIN sys_user su ON pm.user_id = su.user_id
                    WHERE pm.project_id = pp.id
                        and pm.member_type = 'PROJECT_MEMBER' and pm.role = 'PROJECT_LEADER'
                        AND pm.del_flag = 0
                        AND su.name LIKE CONCAT('%', #{req.projectLeaderName}, '%')
                )
            </if>
            <if test="req.noticeStatus != null and req.noticeStatus.name() != null and req.noticeStatus.name() != ''">
                AND ter.publicity_status = #{req.noticeStatus}
            </if>
        ORDER BY ter.update_time DESC
    </select>

    <!-- 公告分页查询 -->
    <select id="selectPublicNoticePage" resultType="com.ylz.saas.resp.SrmAllPublicNoticePageResp">
        SELECT
            ter.id,
            ter.notice_id,
            ter.project_id AS projectId,
            ter.notice_content AS noticeContent,
            pp.sourcing_type AS sourcingMethod,
            tn.notice_title AS noticeTitle,
            pp.project_code AS projectCode,
            pp.project_name AS projectName,
            pp.invite_method AS publicCategory,
            (case when ter.notice_status = 'PUBLISHED' then  DATE_FORMAT(ter.update_time, '%Y-%m-%d %H:%i:%s')
            else null end) publishTime,
            DATE_FORMAT(ter.notice_time, '%Y-%m-%d %H:%i:%s') AS publishTime
        FROM srm_tender_evaluation_result ter
        INNER JOIN srm_procurement_project pp ON ter.project_id = pp.id
        INNER JOIN srm_tender_notice tn ON ter.notice_id = tn.id
        WHERE ter.del_flag = 0
        AND pp.del_flag = 0
        AND tn.del_flag = 0
        AND ter.publicity_status = 'PUBLISHED'
            <if test="req.noticeTitle != null and req.noticeTitle != ''">
                AND tn.notice_title LIKE CONCAT('%', #{req.noticeTitle}, '%')
            </if>
            <if test="req.publishStartTime != null">
                AND ter.notice_time >= #{req.publishStartTime}
            </if>
            <if test="req.publishEndTime != null">
                AND ter.notice_time &lt;= #{req.publishEndTime}
            </if>
            <if test="req.searchContent != null and req.searchContent != ''">
                AND (pp.project_code LIKE CONCAT('%', #{req.searchContent}, '%') 
                     OR pp.project_name LIKE CONCAT('%', #{req.searchContent}, '%'))
            </if>
            <if test="req.sourcingType != null and req.sourcingType.size() > 0">
                AND pp.sourcing_type IN
                <foreach collection="req.sourcingType" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="req.projectLeaderName != null and req.projectLeaderName != ''">
                AND EXISTS (
                    SELECT 1 FROM srm_project_member pm
                    INNER JOIN sys_user su ON pm.user_id = su.user_id
                    WHERE pm.project_id = pp.id
                        and pm.member_type = 'PROJECT_MEMBER' and pm.role = 'PROJECT_LEADER'
                        AND pm.del_flag = 0
                        AND su.name LIKE CONCAT('%', #{req.projectLeaderName}, '%')
                )
            </if>
            <if test="req.noticeStatus != null and req.noticeStatus.name() != null and req.noticeStatus.name() != ''">
                AND ter.notice_status = #{req.noticeStatus}
            </if>
        ORDER BY ter.update_time DESC
    </select>

</mapper>
