spring:
  data:
    redis:
      host: qa-dc-saas-redis.dc-qa.yunlizhi.net
      port: 6389
      password: qNbO8BdMBC0ZH0SrUrrD
      database: 4
  # 数据库相关配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      username: saas_ayn_17
      password: 9D42Gcze1CxupHRj
      url: ********************************************************************************************************************************************************************************************************************************************************************************************************************
  ai:
    dashscope:
      api-key: "sk-********************************"

# 文件上传配置
file:
  bucketName: qa-dc-saas
  oss:
    enable: true
    path-style-access: false
    accessKey: LTAI5tMcN6GJT93Ag6Fd7kqY
    secretKey: ******************************
    endpoint: oss-cn-zhangjiakou.aliyuncs.com

# 验证码配置
aj:
  captcha:
    water-mark: canpan

# 登录报文加密根密钥 ，必须是16位
security:
  encodeKey: canpancanpancanp

# 配置文件加密根密码
jasypt:
  encryptor:
    password: saas

# swagger token url 配置
swagger:
  token-url: ${swagger.gateway}/admin/oauth2/token

# xxl-job配置
xxl:
  job:
    admin:
      addresses: http://saas-xxl-job-admin.canpanscp.com/xxl-job-admin
    executor:
      appname: procurement-platform
      port: 9991

# 三方对接配置
auth:
  client:
    url: https://ayn.canpanscp.com/api/admin/oauth2/token

# 短信登录地址配置
app:
  api-url: https://ayn.example.com