package com.ylz.saas.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 审批人信息响应类
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@Schema(description = "审批人信息响应类")
public class ApproverInfo implements Serializable {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String userName;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 显示名称（格式：姓名-组织部门）
     */
    @Schema(description = "显示名称（格式：姓名-组织部门）")
    private String displayName;

    private static final long serialVersionUID = 1L;

    /**
     * 构造显示名称
     */
    public void buildDisplayName() {
        if (userName != null && deptName != null) {
            this.displayName = userName + "-" + deptName;
        } else if (userName != null) {
            this.displayName = userName;
        }
    }
}
