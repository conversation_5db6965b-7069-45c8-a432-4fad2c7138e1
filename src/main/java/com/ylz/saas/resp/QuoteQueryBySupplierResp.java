package com.ylz.saas.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/18 14:48
 * @Description
 */
@Data
public class QuoteQueryBySupplierResp {

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 参与报价总轮次
     */
    private Integer quoteRoundCount;

    /**
     * 报价总条数
     */
    private Integer quoteMaterialCount;

    /**
     * 总报价金额
     */
    private BigDecimal totalQuoteAmount;

    /**
     * 报价IP
     */
    private String quoteIp;

    /**
     * 中标条数
     */
    private Integer awardCount;

    /**
     * 中标总价
     */
    private BigDecimal awardAmount;

    /**
     * 中标候选顺序
     */
    private String winnerCandidateOrder;

    /**
     * 是否推荐中标(0-否、1-是)
     */
    private Integer isRecommendedWinner;

    /**
     * 是否中标(0-否、1-是)
     */
    private Integer isWin;

}
