package com.ylz.saas.resp;

import com.ylz.saas.enums.WinnerCandidateOrderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 线下评标汇总供应商信息响应类
 * <AUTHOR>
 * @createDate 2025-07-14
 */
@Data
@Schema(description = "线下评标汇总供应商信息响应类")
public class SrmTenderOfflineEvaluationSummarySupplierResp implements Serializable {

    /**
     * 租户供应商ID
     */
    @Schema(description = "租户供应商ID")
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 评标总分（手动输入）
     */
    @Schema(description = "评标总分（手动输入）")
    private BigDecimal totalScore;

    /**
     * 投标价格（手动输入）
     */
    @Schema(description = "投标价格（手动输入）")
    private BigDecimal bidPrice;

    /**
     * 排名（前端生成）
     */
    @Schema(description = "排名（前端生成）")
    private Integer ranking;

    /**
     * 是否推荐中标(0-否、1-是)
     */
    @Schema(description = "是否推荐中标")
    private Integer isRecommendedWinner;

    /**
     * 中标候选顺序
     */
    @Schema(description = "中标候选顺序")
    private WinnerCandidateOrderEnum winnerCandidateOrder;

    /**
     * 中标候选顺序描述
     */
    @Schema(description = "中标候选顺序描述")
    private String winnerCandidateOrderDesc;

    private static final long serialVersionUID = 1L;
}
