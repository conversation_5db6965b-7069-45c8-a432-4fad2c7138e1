package com.ylz.saas.resp;

import com.ylz.saas.entity.SrmRecruitSupplier;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 供应商招募详情响应类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商招募详情响应类")
public class SrmRecruitSupplierDetailResp extends SrmRecruitSupplier implements Serializable {

    /**
     * 招募小组成员列表
     */
    @Schema(description = "招募小组成员列表")
    private List<MemberInfo> memberList;

    /**
     * 物资清单列表
     */
    @Schema(description = "物资清单列表")
    private List<MaterialInfo> materialList;

    /**
     * 资质要求列表
     */
    @Schema(description = "资质要求列表")
    private List<CertificateInfo> certificateList;

    /**
     * 合作媒体列表
     */
    @Schema(description = "合作媒体列表")
    private List<MediaInfo> mediaList;

    /**
     * 招募小组成员信息
     */
    @Data
    @Schema(description = "招募小组成员信息")
    public static class MemberInfo implements Serializable {
        /**
         * 成员ID
         */
        @Schema(description = "成员ID")
        private Long id;

        /**
         * 角色
         */
        @Schema(description = "角色")
        private String role;

        /**
         * 用户ID
         */
        @Schema(description = "用户ID")
        private Long userId;

        /**
         * 姓名
         */
        @Schema(description = "姓名")
        private String userName;

        /**
         * 联系电话
         */
        @Schema(description = "联系电话")
        private String contactPhone;

        /**
         * 部门ID
         */
        @Schema(description = "部门ID")
        private Long deptId;

        /**
         * 所属组织
         */
        @Schema(description = "所属组织")
        private String deptName;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 物资清单信息
     */
    @Data
    @Schema(description = "物资清单信息")
    public static class MaterialInfo implements Serializable {
        /**
         * 详情ID
         */
        @Schema(description = "详情ID")
        private Long id;

        /**
         * 招募类型
         */
        @Schema(description = "招募类型")
        private String recruitType;

        /**
         * 物料ID
         */
        @Schema(description = "物料ID")
        private Long materialId;

        /**
         * 物料编码
         */
        @Schema(description = "物料编码")
        private String materialCode;

        /**
         * 规格/型号
         */
        @Schema(description = "规格/型号")
        private String spec;

        /**
         * 计量单位
         */
        @Schema(description = "计量单位")
        private String unit;

        /**
         * 物料名称
         */
        @Schema(description = "物料名称")
        private String materialName;

        /**
         * 备注
         */
        @Schema(description = "备注")
        private String remark;

        /**
         * 工程服务内容
         */
        @Schema(description = "工程服务内容")
        private String engineeringContext;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 资质要求信息
     */
    @Data
    @Schema(description = "资质要求信息")
    public static class CertificateInfo implements Serializable {
        /**
         * 证件ID
         */
        @Schema(description = "证件ID")
        private Long id;

        /**
         * 文件名称
         */
        @Schema(description = "文件名称")
        private String documentName;

        /**
         * 枚举表ID
         */
        @Schema(description = "枚举表ID")
        private Long baseCertificateId;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 合作媒体信息
     */
    @Data
    @Schema(description = "合作媒体信息")
    public static class MediaInfo implements Serializable {
        /**
         * 媒体ID
         */
        @Schema(description = "媒体ID")
        private Long id;

        /**
         * 媒体名称
         */
        @Schema(description = "媒体名称")
        private String mediaName;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
