package com.ylz.saas.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评标专家打分响应类
 */
@Data
@Schema(description = "评标专家打分响应类")
public class SrmTenderBidEvaluationScoringResp implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 专家用户id
     */
    @Schema(description = "专家用户id")
    private Long userId;

    /**
     * 评分详情id
     */
    @Schema(description = "评分详情id")
    private Long scoringDetailId;

    /**
     * 投标响应表ID
     */
    @Schema(description = "投标响应表ID")
    private Long responseId;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long tenantSupplierId;

    /**
     * 分值（评分项）
     */
    @Schema(description = "分值（评分项）")
    private BigDecimal score;

    /**
     * 状态：待提交 已提交（评审完成） 已审核（已汇总）
     */
    @Schema(description = "状态：待提交 已提交（评审完成） 已审核（已汇总）")
    private String status;

    /**
     * 是否符合（评审项）
     */
    @Schema(description = "是否符合（评审项）")
    private Integer isConform;

    /**
     * 类型（评审项、评分项）
     */
    @Schema(description = "类型（评审项、评分项）")
    private String type;

    /**
     * 评审结论
     */
    @Schema(description = "评审结论")
    private String conclusion;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
