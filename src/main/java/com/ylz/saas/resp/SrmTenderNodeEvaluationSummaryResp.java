package com.ylz.saas.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 节点评审汇总响应类
 */
@Data
@Schema(description = "节点评审汇总响应类")
public class SrmTenderNodeEvaluationSummaryResp implements Serializable {

    /**
     * 节点名称
     */
    @Schema(description = "节点名称")
    private String nodeName;

    /**
     * 节点类型（评审项、评分项）
     */
    @Schema(description = "节点类型（评审项、评分项）")
    private String nodeType;

    /**
     * 节点权重（仅评分项有效）
     */
    @Schema(description = "节点权重（仅评分项有效）")
    private BigDecimal nodeWeight;

    /**
     * 节点总分（仅评分项有效）
     */
    @Schema(description = "节点总分（仅评分项有效）")
    private BigDecimal nodeTotalScore;

    /**
     * 专家列表
     */
    @Schema(description = "专家列表")
    private List<ExpertInfo> experts;

    /**
     * 供应商评审/评分结果列表
     */
    @Schema(description = "供应商评审/评分结果列表")
    private List<SupplierEvaluationResult> supplierResults;

    /**
     * 结论数据（评分项返回三行：结论、权重、实际得分）
     */
    @Schema(description = "结论数据")
    private List<ConclusionData> conclusionDataList;

    /**
     * 专家信息
     */
    @Data
    @Schema(description = "专家信息")
    public static class ExpertInfo implements Serializable {

        /**
         * 专家用户ID
         */
        @Schema(description = "专家用户ID")
        private Long userId;

        /**
         * 专家姓名
         */
        @Schema(description = "专家姓名")
        private String expertName;

        /**
         * 专家编码
         */
        @Schema(description = "专家编码")
        private String expertCode;

        /**
         * 专家类别
         */
        @Schema(description = "专家类别")
        private String expertCategory;

        /**
         * 是否为组长
         */
        @Schema(description = "是否为组长")
        private Boolean isLeader;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 供应商评审/评分结果
     */
    @Data
    @Schema(description = "供应商评审/评分结果")
    public static class SupplierEvaluationResult implements Serializable {

        /**
         * 供应商ID
         */
        @Schema(description = "供应商ID")
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        @Schema(description = "供应商名称")
        private String supplierName;

        /**
         * 各专家的评审/评分情况
         */
        @Schema(description = "各专家的评审/评分情况")
        private List<ExpertEvaluationDetail> expertDetails;

        /**
         * 结论（评审节点：通过/不通过，评分节点：平均分）
         */
        @Schema(description = "结论（评审节点：通过/不通过，评分节点：平均分）")
        private String conclusion;

        /**
         * 实际得分（仅评分项有效：分数总和/人数*权重）
         */
        @Schema(description = "实际得分（仅评分项有效：分数总和/人数*权重）")
        private BigDecimal actualScore;

        /**
         * 平均分（仅评分项有效）
         */
        @Schema(description = "平均分（仅评分项有效）")
        private BigDecimal averageScore;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 专家评审/评分详情
     */
    @Data
    @Schema(description = "专家评审/评分详情")
    public static class ExpertEvaluationDetail implements Serializable {

        /**
         * 专家用户ID
         */
        @Schema(description = "专家用户ID")
        private Long userId;

        /**
         * 专家姓名
         */
        @Schema(description = "专家姓名")
        private String expertName;

        /**
         * 是否为组长汇总
         */
        @Schema(description = "是否为组长汇总")
        private Boolean isLeaderSummary;

        /**
         * 评审结果（评审项：1-通过，0-不通过；评分项：具体分数）
         */
        @Schema(description = "评审结果（评审项：1-通过，0-不通过；评分项：具体分数）")
        private String result;

        /**
         * 总分（仅评分项有效，该专家对该供应商在此节点下的总分）
         */
        @Schema(description = "总分（仅评分项有效，该专家对该供应商在此节点下的总分）")
        private BigDecimal totalScore;

        /**
         * 评审结论
         */
        @Schema(description = "评审结论")
        private String conclusion;

        /**
         * 评审状态
         */
        @Schema(description = "评审状态")
        private String status;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 结论数据
     */
    @Data
    @Schema(description = "结论数据")
    public static class ConclusionData implements Serializable {

        /**
         * 数据类型（结论、权重、实际得分）
         */
        @Schema(description = "数据类型")
        private String dataType;

        /**
         * 供应商结论数据映射（供应商ID -> 结论值）
         */
        @Schema(description = "供应商结论数据映射")
        private Map<Long, String> supplierDataMap;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
