package com.ylz.saas.resp;

import com.ylz.saas.entity.SrmTenderBidEvaluationScoring;
import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 专家打分表格响应类
 */
@Data
@Schema(description = "专家打分表格响应类")
public class SrmTenderBidEvaluationScoringTableResp implements Serializable {

    /**
     * 供应商列表（表头）
     */
    @Schema(description = "供应商列表（表头）")
    private List<SupplierInfo> suppliers;

    /**
     * 评审标准行数据
     */
    @Schema(description = "评审标准行数据")
    private List<EvaluationStandardRow> evaluationStandards;

    /**
     * 节点总分
     */
    @Schema(description = "节点总分")
    private BigDecimal nodeTotalScore;

    /**
     * 节点权重
     */
    @Schema(description = "节点权重")
    private Double nodeWeight;

    /**
     * 供应商信息
     */
    @Data
    @Schema(description = "供应商信息")
    public static class SupplierInfo implements Serializable {

        /**
         * 供应商ID
         */
        @Schema(description = "供应商ID")
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        @Schema(description = "供应商名称")
        private String supplierName;

        /**
         * 供应商报价状态
         */
        @Schema(description = "供应商报价状态")
        private String quoteStatus;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 评审标准行数据
     */
    @Data
    @Schema(description = "评审标准行数据")
    public static class EvaluationStandardRow implements Serializable {

        /**
         * 评标标准ID
         */
        @Schema(description = "评标标准ID")
        private Long standardId;

        /**
         * 评审项名称
         */
        @Schema(description = "评审项名称")
        private String itemName;

        /**
         * 评审项描述
         */
        @Schema(description = "评审项描述")
        private String itemDescription;

        /**
         * 满分分值
         */
        @Schema(description = "满分分值")
        private Integer maxScore;
        /**
         * 最小分值
         */
        @Schema(description = "最小分值")
        private Integer minScore;
        /**
         * 权重
         */
        @Schema(description = "权重")
        private Double weight;

        /**
         * 类型（评审项、评分项）
         */
        @Schema(description = "类型（评审项、评分项）")
        private String type;

        /**
         * 是否为总体汇总行
         */
        @Schema(description = "是否为总体汇总行")
        private Boolean isOverallSummary;

        /**
         * 供应商打分信息列表（按供应商顺序排列）
         */
        @Schema(description = "供应商打分信息列表（按供应商顺序排列）")
        private List<SupplierScoringInfo> supplierScorings;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 供应商打分信息
     */
    @Data
    @Schema(description = "供应商打分信息")
    public static class SupplierScoringInfo implements Serializable {

        /**
         * 供应商ID
         */
        @Schema(description = "供应商ID")
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        @Schema(description = "供应商名称")
        private String tenantSupplierName;
        /**
         * 专家用户ID
         */
        @Schema(description = "专家用户ID")
        private Long userId;

        /**
         * 专家姓名
         */
        @Schema(description = "专家姓名")
        private String expertName;

        /**
         * 评分ID
         */
        @Schema(description = "评分ID")
        private Long scoringId;

        /**
         * 供应商回应ID
         */
        @Schema(description = "供应商回应ID")
        private Long responseId;

        /**
         * 分值（评分项）
         */
        @Schema(description = "分值（评分项）")
        private BigDecimal score;

        /**
         * 是否符合（评审项）
         */
        @Schema(description = "是否符合（评审项）")
        private Integer isConform;

        /**
         * 评审结论
         */
        @Schema(description = "评审结论")
        private String conclusion;

        /**
         * 状态：待提交 已提交（评审完成） 已审核（已汇总）
         */
        @Schema(description = "状态：待提交 已提交（评审完成） 已审核（已汇总）")
        private SrmTenderBidEvaluationScoring.SubmitStatus status;

        /**
         * 是否已打分
         */
        @Schema(description = "是否已打分")
        private Boolean isScored;

        /**
         * 是否已汇总
         */
        @Schema(description = "是否已汇总")
        private Boolean isSummarized;

        /**
         * 是否为组长汇总
         */
        @Schema(description = "是否为组长汇总")
        private Boolean isLeaderSummary;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
