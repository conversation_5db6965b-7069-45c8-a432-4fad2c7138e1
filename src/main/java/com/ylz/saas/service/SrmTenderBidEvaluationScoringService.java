package com.ylz.saas.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmTenderBidEvaluationScoring;
import com.ylz.saas.req.SrmTenderBidEvaluationScoringBatchReq;
import com.ylz.saas.req.SrmTenderBidEvaluationScoringPageReq;
import com.ylz.saas.req.SrmTenderBidEvaluationScoringQueryReq;
import com.ylz.saas.req.SrmTenderBidEvaluationScoringReq;
import com.ylz.saas.req.SrmTenderBidEvaluationSummarizeReq;
import com.ylz.saas.resp.SrmTenderBidEvaluationScoringQueryResp;
import com.ylz.saas.resp.SrmTenderBidEvaluationScoringResp;
import com.ylz.saas.resp.SrmTenderBidEvaluationScoringTableResp;
import com.ylz.saas.resp.SrmTenderNodeEvaluationSummaryResp;
import com.ylz.saas.resp.SrmTenderBidEvaluationScoringTableResp;

import java.util.List;

/**
 * 针对表【srm_tender_bid_evaluation_scoring(评标专家打分)】的数据库操作Service
 * <AUTHOR>
 * @createDate 2025-07-09
 */
public interface SrmTenderBidEvaluationScoringService extends IService<SrmTenderBidEvaluationScoring> {





    /**
     * 批量提交评分（支持新增和修改）
     * @param req 评分列表，当ID为空或0时新增，当ID不为空且大于0时修改
     * @return 是否成功
     */
    boolean batchSubmitScoring(SrmTenderBidEvaluationScoringBatchReq req);



    /**
     * 查询专家对某节点的项目打分情况（表格格式）
     * @param req 查询条件，userId必传：查询指定评标成员的打分情况
     * @return 表格格式的打分情况
     */
    SrmTenderBidEvaluationScoringTableResp getExpertScoringTableByNode(SrmTenderBidEvaluationScoringQueryReq req);


    /**
     * 撤回评标结果
     * @param req
     */
    boolean revokeScoringResult(SrmTenderBidEvaluationScoringQueryReq req);

    /**
     * 查询某节点下所有专家对各个供应商的打分/评审情况汇总
     * @param req 查询条件，包含projectId、noticeId、sectionId、nodeName
     * @return 节点评审汇总结果
     */
    SrmTenderNodeEvaluationSummaryResp getNodeEvaluationSummary(SrmTenderBidEvaluationScoringQueryReq req);

}
