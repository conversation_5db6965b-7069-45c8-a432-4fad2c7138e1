package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmTenderEvaluationSignature;

import java.util.List;

/**
 * 针对表【srm_tender_evaluation_signature(评标汇总签名进度表)】的数据库操作Service
 * <AUTHOR>
 * @createDate 2025-07-11
 */
public interface SrmTenderEvaluationSignatureService extends IService<SrmTenderEvaluationSignature> {


    /**
     * 评标人员签名
     * @param sectionId 标段ID
     * @param userId 用户ID
     * @param signatureComment 签名意见
     * @return 是否成功
     */
    boolean signEvaluation(Long sectionId, Long userId, String signatureComment);

    /**
     * 获取标段签名进度统计
     * @param sectionId 标段ID
     * @return 签名进度信息 [已签名人数, 总人数]
     */
    int[] getSignatureProgress(Long sectionId);

    /**
     * 检查标段是否所有人都已签名
     * @param sectionId 标段ID
     * @return 是否全部签名完成
     */
    boolean isAllSigned(Long sectionId);

    /**
     * 检查指定用户是否已对标段签名
     * @param sectionId 标段ID
     * @param userId 用户ID
     * @return 是否已签名
     */
    boolean hasUserSigned(Long sectionId, Long userId);

}
