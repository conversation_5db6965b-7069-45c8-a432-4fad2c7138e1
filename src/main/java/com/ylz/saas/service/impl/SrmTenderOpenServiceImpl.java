package com.ylz.saas.service.impl;

import cn.smallbun.screw.core.util.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.*;
import com.ylz.saas.enums.*;
import com.ylz.saas.mapper.SrmProcurementProjectSectionMapper;
import com.ylz.saas.mapper.SrmTenderOpenMapper;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.BidOpenConfigResp;
import com.ylz.saas.resp.SrmOpenTenderMaterialResp;
import com.ylz.saas.resp.SrmOpenTenderSupplierResp;
import com.ylz.saas.service.*;
import com.ylz.saas.vo.SectionQuoteRoundVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【srm_tender_open(开标表)】的数据库操作Service实现
 * @createDate 2025-06-18 11:29:38
 */
@Slf4j
@Service
public class SrmTenderOpenServiceImpl extends ServiceImpl<SrmTenderOpenMapper, SrmTenderOpen>
        implements SrmTenderOpenService {

    @Resource
    private SrmTenderNoticeService tenderNoticeService;

    @Resource
    private SrmProcurementProjectService projectService;

    @Resource
    private SrmTenderFailedLogService failedLogService;

    @Resource
    private SrmProcurementProjectSectionMapper srmProcurementProjectSectionMapper;

    @Resource
    private SrmProcessInstanceService srmProcessInstanceService;
    @Autowired
    private SrmProcurementProjectSectionService srmProcurementProjectSectionService;
    @Autowired
    private SrmProjectMemberService projectMemberService;
    @Autowired
    private SrmTenderNoticeService srmTenderNoticeService;
    @Autowired
    private SrmProcurementProjectService srmProcurementProjectService;

    @Override
    public void endOpenBid(SrmTenderOpenReq req) {
        SaasUser user = SecurityUtils.getUser();
        ExceptionUtil.checkNonNull(user, "请重新登录！");
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        SrmTenderNotice tenderNotice = tenderNoticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(tenderNotice, "公告不存在");
        Long userId = user.getId();
        Long bidOpener = tenderNotice.getBidOpener();
        if (!Objects.equals(userId, bidOpener)) {
            ExceptionUtil.checkNonNull(null, "您没有权限结束开标！");
        }

        List<SrmTenderOpen> tenderOpens = lambdaQuery().eq(SrmTenderOpen::getNoticeId, noticeId)
                .eq(SrmTenderOpen::getProjectId, projectId)
                .list();
        ExceptionUtil.checkNotEmpty(tenderOpens, "此项目，还未开标！");
        boolean allMatch = tenderOpens.stream().allMatch(
                tenderOpen -> tenderOpen.getOpenStatus() == OpenStatusEnum.END_OPENED);
        if (allMatch) {
            ExceptionUtil.checkNonNull(null, "此项目，已结束开标！");
        }
        // 修改开标状态
        lambdaUpdate().set(SrmTenderOpen::getOpenStatus, OpenStatusEnum.END_OPENED)
                .eq(SrmTenderOpen::getNoticeId, noticeId)
                .eq(SrmTenderOpen::getProjectId, projectId).update();
        // 修改采购项目状态
        SrmProcurementProject procurementProject = projectService.getById(projectId);
        projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.END_OPENED, true);


    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void openBid(SrmTenderOpenReq req) {
        SaasUser user = SecurityUtils.getUser();
        ExceptionUtil.checkNonNull(user, "请重新登录！");
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        SrmTenderNotice tenderNotice = tenderNoticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(tenderNotice, "公告不存在");
        Long userId = user.getId();
        Long bidOpener = tenderNotice.getBidOpener();
        if (!Objects.equals(userId, bidOpener)) {
            ExceptionUtil.checkNonNull(null, "您没有权限开标！");
        }
        List<Long> sectionIdList = req.getSectionIdList();
        // 获取开标信息
        List<SrmTenderOpen> tenderOpens = lambdaQuery().eq(SrmTenderOpen::getNoticeId, noticeId)
                .eq(SrmTenderOpen::getProjectId, projectId)
                .in(CollectionUtils.isNotEmpty(sectionIdList), SrmTenderOpen::getSectionId, sectionIdList)
                .eq(SrmTenderOpen::getOpenStatus, OpenStatusEnum.TO_BID_OPEN)
                .list();
        LocalDateTime currentTime = LocalDateTime.now();
        if(CollectionUtils.isNotEmpty(tenderOpens)){
            List<LocalDateTime> list = tenderOpens.stream().map(SrmTenderOpen::getOpenTime).toList();
            for (LocalDateTime openTime : list) {
                if(openTime.isAfter(currentTime)){
                    ExceptionUtil.checkNonNull(null, "此项目，还未到开标时间！");
                }
            }
        }
        ExceptionUtil.checkNotEmpty(tenderOpens, "此项目，没有待开标数据！");
        // 修改开标状态
        lambdaUpdate().set(SrmTenderOpen::getOpenTime, currentTime)
                .set(SrmTenderOpen::getOpenStatus, OpenStatusEnum.OPENED)
                .in(CollectionUtils.isNotEmpty(sectionIdList), SrmTenderOpen::getSectionId, sectionIdList)
                .eq(SrmTenderOpen::getNoticeId, noticeId)
                .eq(SrmTenderOpen::getProjectId, projectId)
                .update();
        // 修改采购项目状态
        SrmProcurementProject procurementProject = projectService.getById(projectId);
        projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.BID_OPENED, true);

        // 青贮开标后，立刻开标结束
        if(procurementProject.getSourcingType() == BaseServiceTypeFieldService.BuyWayEnum.QZXJ){
            log.info("青贮开标，立即结束开标");
            SrmTenderOpenReq endOpenBidReq = new SrmTenderOpenReq();
            endOpenBidReq.setNoticeId(noticeId);
            endOpenBidReq.setProjectId(projectId);
            endOpenBidReq.setSectionIdList(sectionIdList);
            this.endOpenBid(endOpenBidReq);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addTenderOpen(SrmTenderOpenAddReq req) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        SrmTenderNotice tenderNotice = tenderNoticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(tenderNotice, "公告不存在");
        LocalDateTime bidOpenTime = tenderNotice.getBidOpenTime();
        LocalDateTime quoteStartTime = tenderNotice.getQuoteStartTime();
        LocalDateTime quoteEndTime = tenderNotice.getQuoteEndTime();
        List<SrmProcurementProjectSection> sectionList = srmProcurementProjectSectionService.lambdaQuery().eq(SrmProcurementProjectSection::getProjectId, projectId).list();
        ExceptionUtil.checkNotEmpty(sectionList, "项目标段不存在");
        List<SrmTenderOpen> srmTenderOpens = sectionList.stream().map(section -> {
            SrmTenderOpen srmTenderOpen = new SrmTenderOpen();
            Long sectionId = section.getId();
            srmTenderOpen.setNoticeId(noticeId);
            srmTenderOpen.setProjectId(projectId);
            srmTenderOpen.setSectionId(sectionId);
            srmTenderOpen.setOpenTime(bidOpenTime);
            srmTenderOpen.setCurrentRound(1);
            srmTenderOpen.setLatestQuoteStartTime(quoteStartTime);
            srmTenderOpen.setLatestQuoteEndTime(quoteEndTime);
            // 设置开标状态，为草稿
            srmTenderOpen.setOpenStatus(OpenStatusEnum.DRAFT);
            // 删除开标信息
            lambdaUpdate().eq(SrmTenderOpen::getNoticeId, noticeId)
                    .eq(SrmTenderOpen::getProjectId, projectId)
                    .eq(SrmTenderOpen::getSectionId, sectionId)
                    .eq(SrmTenderOpen::getOpenStatus, OpenStatusEnum.DRAFT)
                    .remove();
            return srmTenderOpen;
        }).toList();
        saveBatch(srmTenderOpens);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void failedTender(SrmTenderFailedLogReq req) {
        ExceptionHandleTypeEnum exceptionHandleType = req.getExceptionHandleType();
        ExceptionTypeEnum exceptionType = req.getExceptionType();
        Long oldProjectId = req.getProjectId();
        Long oldNoticeId = req.getNoticeId();
        SrmTenderNotice oldNoticeNotice = tenderNoticeService.getById(oldNoticeId);
        ExceptionUtil.checkNonNull(oldNoticeNotice, "公告不存在");
        SrmProcurementProject project = projectService.getById(oldProjectId);
        ExceptionUtil.checkNonNull(project, "采购项目不存在");
        String projectCode = project.getProjectCode();
        SrmProcurementProject srmProcurementProject = projectService.detail(projectCode, false);
        // 是否流标公示
        Boolean isPublicity = req.getIsPublicity();
        Long failNoticeId = 0L;
        for (SrmProcurementProjectSection projectSection : srmProcurementProject.getProjectSectionList()) {
            SrmTenderFailedLog srmTenderFailedLog = new SrmTenderFailedLog();
            srmTenderFailedLog.setIsPublicity(isPublicity ? 1 : 0);
            if (isPublicity) {
                srmTenderFailedLog.setTenderFailedTitle(req.getTenderFailedTitle());
                srmTenderFailedLog.setTenderFailedContent(req.getTenderFailedContent());
            }
            srmTenderFailedLog.setRemark(req.getRemark());
            srmTenderFailedLog.setExceptionHandleType(exceptionHandleType);
            srmTenderFailedLog.setExceptionType(exceptionType);
            srmTenderFailedLog.setProjectId(req.getProjectId());
            srmTenderFailedLog.setNoticeId(req.getNoticeId());
            // 设置标段信息
            srmTenderFailedLog.setSectionId(projectSection.getId());
            srmTenderFailedLog.setTenderFailedContent(req.getTenderFailedContent());
            srmTenderFailedLog.setTenderFailedTitle(req.getTenderFailedTitle());
            // 设置状态
            srmTenderFailedLog.setApprovalStatus(ApproveStatusEnum.TO_APPROVE);
            failedLogService.save(srmTenderFailedLog);
            failNoticeId = srmTenderFailedLog.getId();
        }

        // 业务流程发起
        ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
        String projectAndNoticeId = oldProjectId + "#" + oldNoticeId;
//        Long bizId = Long.valueOf(oldProjectId+""+oldNoticeId);
        startReq.setBizKey(projectAndNoticeId);
        startReq.setBizId(oldProjectId);
        if (BaseServiceTypeFieldService.BuyWayEnum.QZXJ == project.getSourcingType()) {
            projectCode = projectCode + "&isSilage=true&noticeId="+oldNoticeId;
        } else {
            projectCode = projectCode + "&isSilage=false&noticeId="+oldNoticeId;
        }
        startReq.setArgs(List.of(failNoticeId, projectCode, oldProjectId , projectAndNoticeId));
        startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_TENDER_FAILED_AUDIT);
        startReq.setApprovalType(req.getApprovalType());
        startReq.setSpecialProcessExecutorList(req.getSpecialProcessExecutorList());
        srmProcessInstanceService.startProcessInstance(startReq);
        log.info("流标业务流程发起完成！");

    }

    @Override
    public List<SectionQuoteRoundVo> getSectionQuoteRound(Long noticeId) {
        SrmTenderNotice tenderNotice = tenderNoticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(tenderNotice, "公告不存在");
        List<SrmTenderOpen> openList = baseMapper.selectList(Wrappers.<SrmTenderOpen>lambdaQuery()
                .eq(SrmTenderOpen::getNoticeId, noticeId));
        Map<Long, Integer> sectionIdMap = Optional.ofNullable(openList).stream().flatMap(Collection::stream)
                .collect(Collectors.toMap(SrmTenderOpen::getSectionId, SrmTenderOpen::getCurrentRound, (k1, k2) -> k1));
        List<SrmProcurementProjectSection> sectionList = srmProcurementProjectSectionMapper
                .selectList(Wrappers.<SrmProcurementProjectSection>lambdaQuery()
                        .eq(SrmProcurementProjectSection::getProjectId, tenderNotice.getProjectId())
                        .eq(SrmProcurementProjectSection::getDelFlag, YesNoEnum.NO.getCode()));
        return sectionList.stream().map(item -> {
            SectionQuoteRoundVo sectionQuoteRoundVo = new SectionQuoteRoundVo();
            sectionQuoteRoundVo.setSectionId(item.getId());
            sectionQuoteRoundVo.setCurrentQuoteRound(sectionIdMap.getOrDefault(item.getId(), 1));
            return sectionQuoteRoundVo;
        }).toList();
    }


    @Override
    public List<SrmOpenTenderMaterialResp> openTenderInfoMaterial(SrmTenderOpenInfoReq req) {
        Long projectId = req.getProjectId();
        Long noticeId = req.getNoticeId();
        Long sectionId = req.getSectionId();
        SrmTenderOpen tenderOpen = lambdaQuery().eq(SrmTenderOpen::getProjectId, projectId)
                .eq(SrmTenderOpen::getNoticeId, noticeId)
                .eq(SrmTenderOpen::getSectionId, sectionId)
                .one();
        ExceptionUtil.checkNonNull(tenderOpen, "未开标，无法查看报价");
        // 调用Mapper方法，查询开标物资信息（支持物料名称模糊查询）
        Long tenantSupplierId = req.getTenantSupplierId();
        return baseMapper.selectOpenTenderMaterials(
                tenantSupplierId,
                projectId,
                noticeId,
                sectionId,
                req.getRoundNo(),
                req.getMaterialName()); // 物料名称模糊查询条件
    }

    @Override
    public List<SrmOpenTenderSupplierResp> openTenderInfoSupplier(SrmTenderOpenInfoReq req) {
        Long projectId = req.getProjectId();
        Long noticeId = req.getNoticeId();
        Long sectionId = req.getSectionId();
        SrmTenderOpen tenderOpen = lambdaQuery().eq(SrmTenderOpen::getProjectId, projectId)
                .eq(SrmTenderOpen::getNoticeId, noticeId)
                .eq(SrmTenderOpen::getSectionId, sectionId)
                .one();
        ExceptionUtil.checkNonNull(tenderOpen, "未开标，无法查看报价");
        Long tenantSupplierId = req.getTenantSupplierId();
        return baseMapper.selectOpenTenderSuppliers(
                tenantSupplierId,
                projectId,
                noticeId,
                sectionId,
                req.getRoundNo(),
                req.getSupplierName()); // 供应商名称模糊查询条件
    }

    @Override
    public SrmTenderFailedLog failedTenderDetail(Long id) {
        SrmTenderFailedLog byId = failedLogService.getById(id);
        if (byId == null) {
            return byId;
        }
        Long projectId = byId.getProjectId();
//        Long sectionId = byId.getSectionId();
//        Long noticeId = byId.getNoticeId();

        SrmProcurementProject project = projectService.getById(projectId);
        byId.setProjectName(project.getProjectName());
        return byId;
    }

    @Override
    public SrmTenderOpen getQuoteAgainBidOpenInfo(QuoteAgainBidOpenInfoQueryReq req) {
        return this.lambdaQuery()
                .eq(SrmTenderOpen::getNoticeId, req.getNoticeId())
                .eq(SrmTenderOpen::getCurrentRound, req.getRoundNo())
                .last(" limit 1")
                .one();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bidOpenConfig(BidOpenConfigReq req) {
        ExceptionUtil.checkNotEmpty(req.getSupervisionUserIdList(), "请选择监督人员");
        SrmTenderNotice tenderNotice = srmTenderNoticeService.getById(req.getNoticeId());
        ExceptionUtil.checkNonNull(tenderNotice, "招标公告不存在");
        this.lambdaUpdate().eq(SrmTenderOpen::getNoticeId, tenderNotice.getId())
                .set(SrmTenderOpen::getPublicQuote, req.getPublicQuote())
                .update();

        projectMemberService.lambdaUpdate()
                .eq(SrmProjectMember::getBusinessId, req.getNoticeId())
                .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.SUPERVISION)
                .remove();
        List<SrmProjectMember> supervisions = req.getSupervisionUserIdList().stream().map(userId -> {
            SrmProjectMember srmProjectMember = new SrmProjectMember();
            srmProjectMember.setProjectId(tenderNotice.getProjectId());
            srmProjectMember.setBusinessId(tenderNotice.getId());
            srmProjectMember.setUserId(userId);
            srmProjectMember.setMemberType(ProjectMemberTypeEnum.SUPERVISION);
            srmProjectMember.setRole(ProjectMemberRoleEnum.SUPERVISION);
            srmProjectMember.setDelFlag(0);
            return srmProjectMember;
        }).toList();
        projectMemberService.saveBatch(supervisions);
        // 开标人
        SrmProcurementProject project = srmProcurementProjectService.getById(tenderNotice.getProjectId());
        ExceptionUtil.checkNonNull(project, "项目不存在");
        HashSet<Long> newUserIds = new HashSet<>(req.getSupervisionUserIdList());
        Long bidOpenUserId = req.getBidOpenUserId();

        // 修改开标人，处理权限
        handlerProjectUserIdentityDataPermission(tenderNotice.getId(),bidOpenUserId,newUserIds, project);

    }

    /**
     * 项目权限处理
     *
     * @param noticeId 采购项目id
     * @param bidOpenUserId  tendOpenUserId
     * @param newUserIds 新增的用户id
     * @param project    项目
     */
    @Override
    public void handlerProjectUserIdentityDataPermission(Long noticeId, Long bidOpenUserId, HashSet<Long> newUserIds, SrmProcurementProject project) {
        Long projectId = project.getId();
        if (Objects.nonNull(bidOpenUserId)) {
            newUserIds.add(bidOpenUserId);
            projectMemberService.lambdaUpdate()
                    .eq(SrmProjectMember::getBusinessId, noticeId)
                    .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.BID_OPEN)
                    .remove();
            SrmProjectMember srmProjectMember = new SrmProjectMember();
            srmProjectMember.setProjectId(projectId);
            srmProjectMember.setBusinessId(noticeId);
            srmProjectMember.setUserId(bidOpenUserId);
            srmProjectMember.setMemberType(ProjectMemberTypeEnum.BID_OPEN);
            srmProjectMember.setRole(ProjectMemberRoleEnum.BID_OPEN);
            srmProjectMember.setDelFlag(0);
            projectMemberService.save(srmProjectMember);
            // 修改公告里的开标人信息
            srmTenderNoticeService.lambdaUpdate()
                    .set(SrmTenderNotice::getBidOpener, bidOpenUserId)
                    .eq(SrmTenderNotice::getId, noticeId)
                    .update();
        }
        // 其他类型的项目成员列表
        List<SrmProjectMember> existsMemberList = projectMemberService.lambdaQuery()
                .eq(SrmProjectMember::getProjectId, projectId)
                .list();
        Set<Long> existingUserIds = existsMemberList.stream().map(SrmProjectMember::getUserId).collect(Collectors.toSet());
        Set<Long> otherTypesUserIds = existsMemberList.stream()
                .filter(item -> !Objects.equals(ProjectMemberTypeEnum.BID_OPEN, item.getMemberType()))
                .map(SrmProjectMember::getUserId).collect(Collectors.toSet());
        newUserIds.addAll(otherTypesUserIds);

        // 添加权限
        srmProcurementProjectService.insertProjectUserIdentityDataPermission(project, newUserIds, existingUserIds);
    }


    @Override
    public BidOpenConfigResp getBidOpenConfig(Long noticeId) {
        SrmTenderOpen tenderOpen = this.lambdaQuery().eq(SrmTenderOpen::getNoticeId, noticeId).last(" limit 1").one();
        ExceptionUtil.checkNonNull(tenderOpen, "开标记录不存在");
        BidOpenConfigResp bidOpenConfigResp = new BidOpenConfigResp();
        bidOpenConfigResp.setPublicQuote(tenderOpen.getPublicQuote());

        List<SrmProjectMember> memberList = projectMemberService.lambdaQuery()
                .eq(SrmProjectMember::getBusinessId, noticeId)
                .in(SrmProjectMember::getMemberType, Lists.newArrayList(ProjectMemberTypeEnum.SUPERVISION, ProjectMemberTypeEnum.BID_OPEN))
                .list();
        bidOpenConfigResp.setSupervisionList(memberList.stream()
                .filter(item -> Objects.equals(item.getMemberType(), ProjectMemberTypeEnum.SUPERVISION)).toList());
        bidOpenConfigResp.setBidOpen(memberList.stream()
                .filter(item -> Objects.equals(item.getMemberType(), ProjectMemberTypeEnum.BID_OPEN)).findFirst().orElse(null));
        if (bidOpenConfigResp.getBidOpen() == null) {
            bidOpenConfigResp.setProjectLeader(projectMemberService.lambdaQuery()
                    .eq(SrmProjectMember::getBusinessId, tenderOpen.getProjectId())
                    .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                    .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER)
                    .last(" limit 1").one());
        }
        return bidOpenConfigResp;
    }
}




