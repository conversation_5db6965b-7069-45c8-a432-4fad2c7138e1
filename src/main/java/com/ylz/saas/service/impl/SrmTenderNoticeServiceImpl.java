package com.ylz.saas.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.api.dto.MessageSmsDTO;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.core.util.SpringContextHolder;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.common.sequence.generator.CodeGenerator;
import com.ylz.saas.entity.*;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.enums.BidsSegmentTypeEnum;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.InviteStatusEnum;
import com.ylz.saas.enums.OpenStatusEnum;
import com.ylz.saas.enums.ProjectMemberRoleEnum;
import com.ylz.saas.enums.ProjectMemberTypeEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.enums.TenderSupplierResponseStageEnum;
import com.ylz.saas.enums.YesNoEnum;
import com.ylz.saas.mapper.SrmTenderNoticeMapper;
import com.ylz.saas.req.AttachmentInfoReq;
import com.ylz.saas.req.BidFeeInfoReq;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.req.SrmTenderNoticeAddReq;
import com.ylz.saas.req.SrmTenderNoticeReviewReq;
import com.ylz.saas.req.SrmTenderOpenAddReq;
import com.ylz.saas.req.TenderNoticeApproveReq;
import com.ylz.saas.resp.SrmTenderNoticeAddResp;
import com.ylz.saas.service.*;
import com.ylz.saas.vo.ValidateTenderResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【srm_tender_notice(招标公告表)】的数据库操作Service实现
* @createDate 2025-06-10 14:13:56
*/
@Slf4j
@Service
public class SrmTenderNoticeServiceImpl extends ServiceImpl<SrmTenderNoticeMapper, SrmTenderNotice>
    implements SrmTenderNoticeService, ApproveRejectHookService {

    @Resource
    private SrmProcurementProjectService projectService;

    @Resource
    private SrmTenderRequirementService requirementService;

    @Resource
    private SrmProjectAttachmentService attachmentService;

    @Resource
    private SrmTenderSupplierInviteService invoiceService;

    @Resource
    private CodeGenerator codeGenerator;

    @Resource
    @Lazy
    private SrmTenderOpenService srmTenderOpenService;

    @Resource
    private SrmTenderRequirementService srmTenderRequirementService;

    @Resource
    private SrmPlatformSupplierInfoService supplierService;

    @Resource
    private SrmProcessInstanceService srmProcessInstanceService;

    @Resource
    private SrmProjectMemberService srmProjectMemberService;

    @Resource
    @Lazy
    private SrmTenderSupplierResponseService srmTenderSupplierResponseService;
    @Autowired
    private SrmProcurementProjectService srmProcurementProjectService;

    @Resource
    private AsyncSmsService asyncSmsService;

    @Resource
    private SrmTenantSupplierInfoService srmTenantSupplierInfoService;

    @Value("${app.api-url:https://ayn.canpanscp.com}")
    private String appApiUrl;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public String addTenderNotice(SrmTenderNoticeAddReq req) {
        SaasUser user = SecurityUtils.getUser();
        ExceptionUtil.checkNonNull(user, "登录用户不存在！");
        log.info("用户信息校验通过，用户ID：{}, 用户名：{}", user.getId(), user.getName());
        // 查询采购立项
        Long projectId = req.getProjectId();
        SrmProcurementProject project = projectService.getById(projectId);
        log.info("采购立项信息查询成功，项目ID：{}", projectId);
        // 必填校验
        checkTenderNoticeValidate(project, req);
        log.info("必填字段校验通过");
        // 招标要求json写入默认字符串
        for (SrmTenderNoticeAddReq.BidsSegment bidsSegment : req.getBidsSegments()) {
            String requirementContent = bidsSegment.getRequirementContent();
            if(StringUtils.isBlank(requirementContent)){
                bidsSegment.setRequirementContent("{}");
            }
        }
        // 添加招标公告
        SrmTenderNotice srmTenderNotice = saveTenderNotice(req, user, project);
        Long noticeId = srmTenderNotice.getId();
        log.info("招标公告保存成功，公告ID：{}", noticeId);
        // 修改开标人，处理权限
        SrmTenderNoticeAddReq.PurchaseDateDemand purchaseDateDemand = req.getPurchaseDateDemand();
        if (purchaseDateDemand != null) {
            Long bidOpener = purchaseDateDemand.getBidOpener();
            if(bidOpener != null){
                srmTenderOpenService.handlerProjectUserIdentityDataPermission(noticeId, bidOpener, new HashSet<>(), project);
            }
        }
        // 添加招标要求
        List<SrmTenderNoticeAddReq.BidsSegment> bidsSegments = req.getBidsSegments();
        saveBatchTenderRequirement(bidsSegments, srmTenderNotice, user);
        log.info("招标要求批量保存完成");
        // 添加附件
        List<AttachmentInfoReq> attachmentInfos = req.getAttachmentInfos();
        saveAttachments(attachmentInfos, srmTenderNotice, user);
        log.info("附件信息保存完成");
        // 业务流程发起
        ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
        startReq.setBizKey(String.valueOf(noticeId));
        startReq.setBizId(projectId);
        startReq.setArgs(List.of(project.getProjectCode(),projectId,noticeId,noticeId));
        startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_TENDER_NOTICE_AUDIT);
        startReq.setApprovalType(req.getApprovalType());
        startReq.setSpecialProcessExecutorList(req.getSpecialProcessExecutorList());
        srmProcessInstanceService.startProcessInstance(startReq);
        log.info("业务流程发起完成！");
        // 生成开标信息
        SrmTenderOpenAddReq openReq = new SrmTenderOpenAddReq();
        openReq.setNoticeId(noticeId);
        openReq.setProjectId(projectId);
        srmTenderOpenService.addTenderOpen(openReq);
        log.info("生成开标信息完成！");
        return String.valueOf(noticeId);
    }

    private void saveAttachments(List<AttachmentInfoReq> attachmentInfos, SrmTenderNotice srmTenderNotice, SaasUser user) {
        Long noticeId = srmTenderNotice.getId();
        // 删除发标公告信息附件信息
        attachmentService.lambdaUpdate()
                .eq(SrmProjectAttachment::getBusinessId, noticeId)
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.NOTICE)
                .remove();
        if (CollectionUtils.isEmpty(attachmentInfos)) {
            return;
        }
        List<SrmProjectAttachment> attachments = attachmentInfos.stream().map(info -> {
            SrmProjectAttachment attachment = new SrmProjectAttachment();
            BeanUtils.copyProperties(info, attachment);
            attachment.setBusinessType(AttachmentTypeEnum.NOTICE);
            attachment.setProjectId(srmTenderNotice.getProjectId());
            attachment.setBusinessId(noticeId);
            return attachment;
        }).toList();
        attachmentService.saveBatch(attachments);
    }

    private void saveBatchTenderRequirement(List<SrmTenderNoticeAddReq.BidsSegment> bidsSegments, SrmTenderNotice srmTenderNotice, SaasUser user) {
        if (CollectionUtils.isEmpty(bidsSegments)) {
            return;
        }
        Long noticeId = srmTenderNotice.getId();
        Long projectId = srmTenderNotice.getProjectId();
        // 是否邀请函
        boolean isInvite = bidsSegments.stream()
                .anyMatch(segment -> segment.getRequirementType() == BidsSegmentTypeEnum.INVITE_SUPPLIERS);
        // 非邀请供应商
        List<SrmTenderRequirement> srmTenderRequirements = bidsSegments.stream()
                .filter(segment -> {
                    if(isInvite){
                        return segment.getRequirementType() == BidsSegmentTypeEnum.FEE;
                    } else {
                        if (segment.getRequirementType() == BidsSegmentTypeEnum.FEE) {
                            return true; // FEE类型的数据无论amountSet是否为true都需要保存
                        } else {
                            return segment.getRequirementType() != BidsSegmentTypeEnum.INVITE_SUPPLIERS;
                        }
                    }
                }).map(info -> {
                    BidsSegmentTypeEnum requirementType = info.getRequirementType();
                    Long sectionId = info.getSectionId();
                    SrmTenderRequirement srmTenderRequirement = new SrmTenderRequirement();
                    srmTenderRequirement.setNoticeId(noticeId);
                    srmTenderRequirement.setRequirementType(requirementType);
                    srmTenderRequirement.setSectionId(sectionId);
                    srmTenderRequirement.setRequirementName(info.getRequirementName());
                    String requirementContent = info.getRequirementContent();
                    // 保证金信息设置
                    if (requirementType == BidsSegmentTypeEnum.FEE) {
                        srmTenderRequirement.setRequirementName("保证金设置");
                        requirementContent = JSONObject.toJSONString(info.getFeeInfo());
                    }
                    srmTenderRequirement.setRequirementContent(requirementContent);
                    // 删除招标要求
                    requirementService.lambdaUpdate()
                            .eq(SrmTenderRequirement::getNoticeId, noticeId)
                            .eq(SrmTenderRequirement::getSectionId, sectionId)
                            .eq(SrmTenderRequirement::getRequirementType, requirementType)
                            .remove();
                    return srmTenderRequirement;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(srmTenderRequirements)) {
            requirementService.saveBatch(srmTenderRequirements);
        }
        // 供应商邀请
        if(isInvite){
            List<SrmTenderSupplierInvite> supplierInvites = bidsSegments.stream()
                    .filter(segment -> segment.getRequirementType() == BidsSegmentTypeEnum.INVITE_SUPPLIERS)
                    .flatMap(segment -> {
                        List<SrmTenderNoticeAddReq.InviteSuppliers> inviteSuppliers = segment.getInviteSuppliers();
                        return inviteSuppliers.stream().map(supplier -> {
                            Long sectionId = segment.getSectionId();
                            SrmTenderSupplierInvite srmTenderSupplierInvite = new SrmTenderSupplierInvite();
                            srmTenderSupplierInvite.setNoticeId(noticeId);
                            srmTenderSupplierInvite.setProjectId(projectId);
                            srmTenderSupplierInvite.setSectionId(sectionId);
                            srmTenderSupplierInvite.setTenantSupplierId(supplier.getTenantSupplierId());
                            srmTenderSupplierInvite.setSupplierName(supplier.getSupplierName());
                            srmTenderSupplierInvite.setInviteStatus(InviteStatusEnum.PENDING);
                            // 删除供应商邀请
                            invoiceService.lambdaUpdate()
                                    .eq(SrmTenderSupplierInvite::getNoticeId, noticeId)
                                    .eq(SrmTenderSupplierInvite::getProjectId, projectId)
                                    .eq(SrmTenderSupplierInvite::getSectionId, sectionId)
                                    .eq(SrmTenderSupplierInvite::getInviteStatus, InviteStatusEnum.PENDING)
                                    .eq(SrmTenderSupplierInvite::getTenantSupplierId, supplier.getTenantSupplierId())
                                    .remove();
                            return srmTenderSupplierInvite;
                        });
                    }).collect(Collectors.toList());
            invoiceService.saveBatch(supplierInvites);
            SrmProcurementProject project = srmProcurementProjectService.getById(projectId);
            if (project.getInviteMethod() == InviteMethodEnum.INVITE && Objects.equals(project.getInviteReceipt(), YesNoEnum.YES.getCode())) {
                for (SrmTenderSupplierInvite invite : supplierInvites) {
                    ValidateTenderResult validateTenderResult = srmTenderSupplierResponseService.validateSupplierAndProject(noticeId, invite.getTenantSupplierId());
                    srmTenderSupplierResponseService.getOrCreateSupplierResponse(validateTenderResult, invite.getSectionId(), TenderSupplierResponseStageEnum.INVITE);
                }
            }
        }
    }

    @NotNull
    private SrmTenderNotice saveTenderNotice(SrmTenderNoticeAddReq req, SaasUser user, SrmProcurementProject procurementProject) {
        Long deptId = user.getDeptId();
        Long tenantId = user.getTenantId();
        SrmTenderNotice srmTenderNotice = new SrmTenderNotice();
        BeanUtils.copyProperties(req, srmTenderNotice);
        srmTenderNotice.setTenantId(tenantId);
        srmTenderNotice.setDeptId(deptId);
        Long projectId = procurementProject.getId();
        srmTenderNotice.setProjectId(projectId);
        srmTenderNotice.setNoticeTitle(req.getNoticeTitle());
        srmTenderNotice.setNoticeTemplateId(req.getNoticeTemplateId());
        srmTenderNotice.setNoticeContent(req.getNoticeContent());
        // 省市区，地址信息
        SrmTenderNoticeAddReq.QuotationDemand quotationDemand = req.getQuotationDemand();
        srmTenderNotice.setProvince(quotationDemand.getProvince());
        srmTenderNotice.setCity(quotationDemand.getCity());
        srmTenderNotice.setDistrict(quotationDemand.getDistrict());
        srmTenderNotice.setAddress(quotationDemand.getAddress());
        // 报价要求
        srmTenderNotice.setIncludeTax(quotationDemand.getIncludeTax());
        srmTenderNotice.setCertificateType(quotationDemand.getCertificateType());
        // 评审规则
        srmTenderNotice.setEvaluationMethod(req.getEvaluationMethod());
        // 联系人信息
        SrmTenderNoticeAddReq.ContactInfo contactInfo = req.getContactInfo();
        srmTenderNotice.setContactPerson(contactInfo.getContactPerson());
        srmTenderNotice.setContactPhone(contactInfo.getContactPhone());
        srmTenderNotice.setContactFixedPhone(contactInfo.getContactFixedPhone());
        srmTenderNotice.setContactEmail(contactInfo.getContactEmail());
        // 采购日期要求
        SrmTenderNoticeAddReq.PurchaseDateDemand purchaseDateDemand = req.getPurchaseDateDemand();
        srmTenderNotice.setRegisterStartTime(purchaseDateDemand.getRegisterStartTime());
        srmTenderNotice.setRegisterEndTime(purchaseDateDemand.getRegisterEndTime());
        srmTenderNotice.setAuditStartTime(purchaseDateDemand.getAuditStartTime());
        srmTenderNotice.setAuditEndTime(purchaseDateDemand.getAuditEndTime());
        srmTenderNotice.setQuoteStartTime(purchaseDateDemand.getQuoteStartTime());
        srmTenderNotice.setQuoteEndTime(purchaseDateDemand.getQuoteEndTime());
        srmTenderNotice.setBidOpenTime(purchaseDateDemand.getBidOpenTime());
        srmTenderNotice.setBidOpener(purchaseDateDemand.getBidOpener());
        // 文件上传地址
        srmTenderNotice.setFileSubmissionAddress(purchaseDateDemand.getFileSubmissionAddress());
        srmTenderNotice.setFileObtainStartTime(purchaseDateDemand.getFileObtainStartTime());
        srmTenderNotice.setFileObtainEndTime(purchaseDateDemand.getFileObtainEndTime());
        srmTenderNotice.setBidDocPayStartTime(purchaseDateDemand.getBidDocPayStartTime());
        srmTenderNotice.setBidDocPayEndTime(purchaseDateDemand.getBidDocPayEndTime());
        // 开标地点
        srmTenderNotice.setBidOpeningAddress(req.getBidOpeningAddress());
        // 开标方式
        srmTenderNotice.setTenderWay(req.getTenderWay());

        // 开标人设置
        // 查询项目负责人
        List<SrmProjectMember> leaders = srmProjectMemberService.lambdaQuery().eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER)
                .in(SrmProjectMember::getBusinessId, projectId).list();
        if(CollectionUtils.isNotEmpty(leaders)){
            srmTenderNotice.setBidOpener(leaders.get(0).getUserId());
        }
        // 报价须知
        srmTenderNotice.setQuotationNotice(req.getQuotationNotice());
        // 状态设置
        srmTenderNotice.setStatus(PublicNoticeStatusEnum.UNPUBLISHED);
        // 设置审批状态，SUBMITTED
        srmTenderNotice.setNoticeStatus(ApproveStatusEnum.APPROVING);
        SrmTenderNotice existNotice = lambdaQuery().eq(SrmTenderNotice::getProjectId, projectId)
                .in(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVE_REVOKE, ApproveStatusEnum.APPROVE_REJECT)
                .last(" limit 1")
                .one();
        if(existNotice != null){
            srmTenderNotice.setId(existNotice.getId());
            updateById(srmTenderNotice);
        } else {
            // 设置公告编码
            String tenderNoticeCode = codeGenerator.generateWithDate(CodeGeneratorPrefixEnum.ZBGG.name());
            srmTenderNotice.setTenderNoticeCode(tenderNoticeCode);
            save(srmTenderNotice);
        }
        return srmTenderNotice;
    }

    /**
     * 校验必填字段
     *
     * @param procurementProject
     * @param req
     */
    private void checkTenderNoticeValidate(SrmProcurementProject procurementProject, SrmTenderNoticeAddReq req) {
        ExceptionUtil.checkNonNull(procurementProject, "采购立项信息不存在！");
        SrmTenderNoticeAddReq.PurchaseDateDemand purchaseDateDemand = req.getPurchaseDateDemand();
        ExceptionUtil.checkNonNull(purchaseDateDemand, "采购时间要求不能为空！");
        ExceptionUtil.checkNonNull(purchaseDateDemand.getBidOpenTime(), "启用资格预审后，开标时间不能为空！");
        // 是否启用资格预审
        Integer preQualification = procurementProject.getPreQualification();
        if (Objects.equals(preQualification, 1)) {
            ExceptionUtil.checkNonNull(purchaseDateDemand.getRegisterStartTime(), "启用资格预审后，报名截止时间不能为空！");
            ExceptionUtil.checkNonNull(purchaseDateDemand.getRegisterEndTime(), "启用资格预审后，报名截止时间不能为空！");
            ExceptionUtil.checkNonNull(purchaseDateDemand.getAuditStartTime(), "启用资格预审后，资质预审截止时间不能为空！");
            ExceptionUtil.checkNonNull(purchaseDateDemand.getAuditEndTime(), "启用资格预审后，资质预审截止时间不能为空！");
        }

        // 报价要求
        SrmTenderNoticeAddReq.QuotationDemand quotationDemand = req.getQuotationDemand();
        ExceptionUtil.checkNonNull(quotationDemand, "报价要求不能为空！");
        ExceptionUtil.checkNonNull(quotationDemand.getIncludeTax(),  "报价要求-报价是否含税不能为空！");
        String certificateType = quotationDemand.getCertificateType();
        if(StringUtils.isBlank(certificateType)){
            ExceptionUtil.checkNonNull(null, "报价要求-发票要求不能为空！");
        }

        // 保证金设置校验
        List<SrmTenderNoticeAddReq.BidsSegment> bidsSegments = req.getBidsSegments();
        List<SrmTenderNoticeAddReq.BidsSegment> feeBidsSegmentList = bidsSegments.stream()
                .filter(info -> info.getRequirementType() == BidsSegmentTypeEnum.FEE).toList();
        feeBidsSegmentList.forEach(info -> {
            ExceptionUtil.checkNonNull(info.getSectionId(), "保证金设置-标段id不能为空！");
            ExceptionUtil.checkNonNull(info.getAmountSet(), "保证金设置-是否设置金额不能为空！");
            if(BooleanUtils.isTrue(info.getAmountSet())){
                SrmTenderNoticeAddReq.FeeInfo feeInfo = info.getFeeInfo();
                ExceptionUtil.checkNonNull(feeInfo, "保证金设置-保证金信息不能为空！");
                ExceptionUtil.checkNonNull(feeInfo.getGuaranteeAmount(), "保证金设置-保证金金额不能为空！");
                if(StringUtils.isBlank(feeInfo.getPayAccount())){
                    ExceptionUtil.checkNonNull(null, "保证金设置-汇款银行账户不能为空！");
                }
                if(StringUtils.isBlank(feeInfo.getPayBank())){
                    ExceptionUtil.checkNonNull(null, "保证金设置-汇款银行不能为空！");
                }
            }
        });
        // 邀请时，邀请供应商校验
        if(procurementProject.getInviteMethod() == InviteMethodEnum.INVITE){
            List<SrmTenderNoticeAddReq.BidsSegment> supplierBidsSegmentList = bidsSegments.stream()
                    .filter(info -> info.getRequirementType() == BidsSegmentTypeEnum.INVITE_SUPPLIERS).toList();
            ExceptionUtil.checkNotEmpty(supplierBidsSegmentList, "邀请供应商信息不能为空！");
            supplierBidsSegmentList.forEach(info -> {
                ExceptionUtil.checkNonNull(info.getSectionId(), "邀请供应商-标段id不能为空！");
                List<SrmTenderNoticeAddReq.InviteSuppliers> inviteSuppliers = info.getInviteSuppliers();
                ExceptionUtil.checkNotEmpty(inviteSuppliers, "邀请供应商--供应商信息不能为空！");
                inviteSuppliers.forEach(inviteSupplier -> {
                    ExceptionUtil.checkNonNull(inviteSupplier.getTenantSupplierId(), "邀请供应商-租户供应商ID不能为空！");
                    if(StringUtils.isBlank(inviteSupplier.getSupplierName())){
                        ExceptionUtil.checkNonNull(null, "邀请供应商-供应商名称不能为空！");
                    }
                });

            });
        } else {
            // 公开招标
            List<SrmTenderNoticeAddReq.BidsSegment> segmentList = bidsSegments.stream()
                    .filter(info -> info.getRequirementType() != BidsSegmentTypeEnum.INVITE_SUPPLIERS
                            && info.getRequirementType() != BidsSegmentTypeEnum.FEE).toList();
            ExceptionUtil.checkNotEmpty(segmentList, "资质要求，响应条件不能为空！");
            segmentList.forEach(info -> {
                ExceptionUtil.checkNonNull(info.getSectionId(), "资质要求，响应条件-标段id不能为空！");
                String requirementName = info.getRequirementName();
                if (StringUtils.isBlank(requirementName)){
                    ExceptionUtil.checkNonNull(null, "资质要求，响应条件-要求名称不能为空！");
                }
//                String requirementContent = info.getRequirementContent();
//                if (StringUtils.isBlank(requirementContent)){
//                    ExceptionUtil.checkNonNull(null, "资质要求，响应条件-资质要求内容不能为空！");
//                }
            });
        }

        // 评审规则校验
        String evaluationMethod = req.getEvaluationMethod();
        if(StringUtils.isBlank(evaluationMethod)){
            ExceptionUtil.checkNonNull(null, "评审规则不能为空！");
        }

        // 联系人信息校验
        SrmTenderNoticeAddReq.ContactInfo contactInfo = req.getContactInfo();
        ExceptionUtil.checkNonNull(contactInfo, "联系人信息不能为空！");
        String contactPerson = contactInfo.getContactPerson();
        if(StringUtils.isBlank(contactPerson)){
            ExceptionUtil.checkNonNull(null, "联系人信息-联系人人不能为空！");
        }
        String contactPhone = contactInfo.getContactPhone();
        if(StringUtils.isBlank(contactPhone)){
            ExceptionUtil.checkNonNull(null, "联系人信息-联系电话不能为空！");
        }


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addTenderNoticeReview(SrmTenderNoticeReviewReq req) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        ApproveStatusEnum noticeStatus = req.getNoticeStatus();
        if(noticeStatus != ApproveStatusEnum.APPROVE && noticeStatus != ApproveStatusEnum.APPROVE_REJECT){
            ExceptionUtil.checkNonNull(null, "招标审核状态只能填（不通过/已审批）");
        }
        SrmTenderNotice srmTenderNotice = lambdaQuery().eq(SrmTenderNotice::getId, noticeId)
                .eq(SrmTenderNotice::getProjectId, projectId)
                .ne(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVE)
                .one();
        ExceptionUtil.checkNonNull(srmTenderNotice, "未找到对应的招标公告，或者招标信息已审批！");
        // 设置审批结果
        lambdaUpdate().set(SrmTenderNotice::getNoticeStatus, noticeStatus)
                // 设置招标公告状态，审批通过就发布
                .set(noticeStatus == ApproveStatusEnum.APPROVE, SrmTenderNotice::getStatus, PublicNoticeStatusEnum.PUBLISHED)
                .set(SrmTenderNotice::getApprovedRemark, req.getApprovedRemark())
                .eq(SrmTenderNotice::getId, noticeId)
                .eq(SrmTenderNotice::getProjectId, projectId)
                .update();

        // 修改采购项目状态
        if(noticeStatus == ApproveStatusEnum.APPROVE){
            // 生成开标信息
            SrmTenderOpenAddReq openReq = new SrmTenderOpenAddReq();
            openReq.setNoticeId(noticeId);
            openReq.setProjectId(projectId);
            SrmTenderOpenService openService = SpringContextHolder.getBean(SrmTenderOpenService.class);
            openService.addTenderOpen(openReq);
            log.info("生成开标信息完成！");
            SrmProcurementProject procurementProject = projectService.getById(projectId);
            projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.NOTICE, true);
        }
    }

    @Override
    public void addTenderNoticeReviewPass(TenderNoticeApproveReq req) {
        Long noticeId = Long.valueOf(req.getNoticeId());
        SrmTenderNotice srmTenderNotice = lambdaQuery().eq(SrmTenderNotice::getId, noticeId)
                .ne(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVE)
                .one();
        ExceptionUtil.checkNonNull(srmTenderNotice, "未找到对应的招标公告，或者招标信息已审批！");
        // 设置审批结果
        handlerNoticeStatus(noticeId, srmTenderNotice.getProjectId(), ApproveStatusEnum.APPROVE);

        // 发送邀请人信息
        // 获取项目信息
        SrmProcurementProject project = projectService.getById(srmTenderNotice.getProjectId());
        if (project != null && project.getInviteMethod() == InviteMethodEnum.INVITE) {
            // 获取供应商邀请列表
            List<SrmTenderSupplierInvite> supplierInvites = invoiceService.lambdaQuery()
                    .eq(SrmTenderSupplierInvite::getNoticeId, noticeId)
                    .eq(SrmTenderSupplierInvite::getProjectId, srmTenderNotice.getProjectId())
                    .list();
            // 确定业务编码
            String bizCode = "AYN_INVITE_ISSUE";
            if (project.getSourcingType() == BaseServiceTypeFieldService.BuyWayEnum.JJCG) {
                // 如果是竞价采购，使用特定的业务编码
                bizCode = "AYN_INVITE_ISSUE_COMPETITIVE";
            }

            // 为每个供应商发送邀请信息
            for (SrmTenderSupplierInvite invite : supplierInvites) {
                // 获取供应商详细信息
                SrmTenantSupplierInfo supplierInfo = srmTenantSupplierInfoService.lambdaQuery()
                        .eq(SrmTenantSupplierInfo::getId, invite.getTenantSupplierId())
                        .one();

                if (supplierInfo != null && supplierInfo.getLegalPersonPhone() != null) {
                    MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder()
                            .mobile(supplierInfo.getLegalPersonPhone())
                            .biz(bizCode)
                            .param("company_name", supplierInfo.getSupplierName() != null ?
                                    supplierInfo.getSupplierName() : "供应商")
                            .param("project_name", project.getProjectName())
                            .param("project_code", project.getProjectCode())
                            .param("sourcing_method", project.getSourcingType() != null ?
                                    project.getSourcingType().getDesc() : "")
                            .param("quote_start_time", srmTenderNotice.getQuoteStartTime() != null ?
                                    srmTenderNotice.getQuoteStartTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "")
                            .param("quote_end_time", srmTenderNotice.getQuoteEndTime() != null ?
                                    srmTenderNotice.getQuoteEndTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : "")
                            .param("login_url", appApiUrl != null ? appApiUrl : "https://ayn.canpanscp.com")
                            .build();

                    // 调用发送短信的服务方法
                    asyncSmsService.sendSmsAsync(messageSmsDTO);
                }
            }
        }
    }

    @Override
    public void addTenderNoticeReviewReject(TenderNoticeApproveReq req) {
        Long noticeId = Long.valueOf(req.getNoticeId());
        SrmTenderNotice srmTenderNotice = lambdaQuery().eq(SrmTenderNotice::getId, noticeId)
                .ne(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVE_REJECT)
                .one();
        ExceptionUtil.checkNonNull(srmTenderNotice, "未找到对应的招标公告，或者招标信息已审批！");
        // 设置审批结果
        handlerNoticeStatus(noticeId, srmTenderNotice.getProjectId(), ApproveStatusEnum.APPROVE_REJECT);
    }

    private void handlerNoticeStatus(Long noticeId,  Long projectId, ApproveStatusEnum approved) {
        lambdaUpdate().set(SrmTenderNotice::getNoticeStatus, approved)
                // 设置招标公告状态，审批通过就发布
                .set(approved == ApproveStatusEnum.APPROVE, SrmTenderNotice::getStatus, PublicNoticeStatusEnum.PUBLISHED)
                .set(approved == ApproveStatusEnum.APPROVE_REJECT, SrmTenderNotice::getStatus, PublicNoticeStatusEnum.UNPUBLISHED)
                .eq(SrmTenderNotice::getId, noticeId)
                .update();

        // 修改采购项目状态
        if(approved == ApproveStatusEnum.APPROVE){
            // 修改开标状态为待开标
            srmTenderOpenService.lambdaUpdate().set(SrmTenderOpen::getOpenStatus, OpenStatusEnum.TO_BID_OPEN)
                    .eq(SrmTenderOpen::getNoticeId, noticeId)
                    .eq(SrmTenderOpen::getProjectId, projectId)
                    .update();
            SrmProcurementProject procurementProject = projectService.getById(projectId);
            projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.NOTICE, true);
        }
        srmProcessInstanceService.handlerInstanceStatus(projectId,SrmProcessConfigService.BizTypeEnum.SRM_TENDER_NOTICE_AUDIT,approved);
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SrmTenderNoticeAddResp tenderNoticeDetail(String noticeId) {
        // 审批通过的，浏览次数才加1
        lambdaUpdate()
                .setSql("view_count = view_count + 1") // 使用 SQL 表达式更新
                .eq(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVE)
                .eq(SrmTenderNotice::getId, noticeId)
                .update();
        // 查询招标公告信息
        SrmTenderNotice srmTenderNotice = getById(noticeId);
        ExceptionUtil.checkNonNull(srmTenderNotice, "招标公告信息不存在");
        SrmProcurementProject procurementProject = projectService.getById(srmTenderNotice.getProjectId());
        ExceptionUtil.checkNonNull(procurementProject, "采购项目不存在");

        // 构建响应对象
        SrmTenderNoticeAddResp resp = new SrmTenderNoticeAddResp();

        // 基础信息
        BeanUtils.copyProperties(srmTenderNotice, resp);
        // 设置项目编号
        resp.setProjectCode(procurementProject.getProjectCode());

        // 采购时间要求
        SrmTenderNoticeAddReq.PurchaseDateDemand purchaseDateDemand = new SrmTenderNoticeAddReq.PurchaseDateDemand();
        purchaseDateDemand.setRegisterStartTime(srmTenderNotice.getRegisterStartTime());
        purchaseDateDemand.setRegisterEndTime(srmTenderNotice.getRegisterEndTime());
        purchaseDateDemand.setAuditStartTime(srmTenderNotice.getAuditStartTime());
        purchaseDateDemand.setAuditEndTime(srmTenderNotice.getAuditEndTime());
        purchaseDateDemand.setQuoteStartTime(srmTenderNotice.getQuoteStartTime());
        purchaseDateDemand.setQuoteEndTime(srmTenderNotice.getQuoteEndTime());
        purchaseDateDemand.setBidOpenTime(srmTenderNotice.getBidOpenTime());
        purchaseDateDemand.setBidOpener(srmTenderNotice.getBidOpener());
        purchaseDateDemand.setFileObtainStartTime(srmTenderNotice.getFileObtainStartTime());
        purchaseDateDemand.setFileObtainEndTime(srmTenderNotice.getFileObtainEndTime());
        purchaseDateDemand.setBidDocPayStartTime(srmTenderNotice.getBidDocPayStartTime());
        purchaseDateDemand.setBidDocPayEndTime(srmTenderNotice.getBidDocPayEndTime());
        // 文件提交地址
        purchaseDateDemand.setFileSubmissionAddress(srmTenderNotice.getFileSubmissionAddress());
        resp.setPurchaseDateDemand(purchaseDateDemand);
        resp.setBidOpeningAddress(srmTenderNotice.getBidOpeningAddress());
        resp.setTenderWay(srmTenderNotice.getTenderWay());


        // 报价要求
        SrmTenderNoticeAddReq.QuotationDemand quotationDemand = new SrmTenderNoticeAddReq.QuotationDemand();
        BeanUtils.copyProperties(srmTenderNotice, quotationDemand);
        quotationDemand.setIncludeTax(srmTenderNotice.getIncludeTax());
        quotationDemand.setCertificateType(srmTenderNotice.getCertificateType());
        resp.setQuotationDemand(quotationDemand);

        // 联系方式
        SrmTenderNoticeAddReq.ContactInfo contactInfo = new SrmTenderNoticeAddReq.ContactInfo();
        contactInfo.setContactPerson(srmTenderNotice.getContactPerson());
        contactInfo.setContactPhone(srmTenderNotice.getContactPhone());
        contactInfo.setContactFixedPhone(srmTenderNotice.getContactFixedPhone());
        contactInfo.setContactEmail(srmTenderNotice.getContactEmail());
        resp.setContactInfo(contactInfo);

        // 招标要求（包括资质要求、报名响应条件、保证金设置等）
        List<SrmTenderRequirement> tenderRequirements = requirementService.lambdaQuery()
                .eq(SrmTenderRequirement::getNoticeId, noticeId).list();
        if (CollectionUtils.isNotEmpty(tenderRequirements)) {
            List<SrmTenderNoticeAddReq.BidsSegment> bidsSegments = tenderRequirements.stream()
                    .map(requirement -> {
                        SrmTenderNoticeAddReq.BidsSegment segment = new SrmTenderNoticeAddReq.BidsSegment();
                        segment.setRequirementType(requirement.getRequirementType());
                        segment.setSectionId(requirement.getSectionId());
                        segment.setRequirementName(requirement.getRequirementName());
                        String requirementContent = requirement.getRequirementContent();
                        segment.setRequirementContent(requirementContent);

                        // 如果是保证金类型，则解析JSON内容为FeeInfo对象
                        if (BidsSegmentTypeEnum.FEE == requirement.getRequirementType()) {
                            if(StringUtils.isEmpty(requirementContent)){
                                segment.setAmountSet(false);
                            }else{
                                SrmTenderNoticeAddReq.FeeInfo feeInfo = JSONObject.parseObject(
                                        requirementContent, SrmTenderNoticeAddReq.FeeInfo.class);
                                segment.setFeeInfo(feeInfo);
                                segment.setAmountSet(feeInfo != null && StringUtils.isNotBlank(feeInfo.getPayAccount()));
                            }
                        }
                        // 如果是标书费标书文件信息
                        if(BidsSegmentTypeEnum.BID_DOCUMENT == requirement.getRequirementType()){
                            BidFeeInfoReq bidFeeInfo = JSONObject.parseObject(requirementContent, BidFeeInfoReq.class);
                            segment.setBidFeeInfo(bidFeeInfo);
                            resp.setBidFeeCollection(bidFeeInfo != null && StringUtils.isNotBlank(bidFeeInfo.getBankAccount()));
                        }
                        return segment;
                    }).collect(Collectors.toList());
            resp.setBidsSegments(bidsSegments);
        }

        // 附件信息
        List<AttachmentInfoReq> attachmentInfos = attachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId,noticeId)
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.NOTICE).list()
                .stream()
                .map(info -> {
                    AttachmentInfoReq attachment = new AttachmentInfoReq();
                    BeanUtils.copyProperties(info, attachment);
                    return attachment;
                }).collect(Collectors.toList());
        resp.setAttachmentInfos(attachmentInfos);

        // 供应商邀请信息
        List<SrmTenderSupplierInvite> supplierInvites = invoiceService.lambdaQuery()
                .eq(SrmTenderSupplierInvite::getNoticeId, noticeId).list();
        if (CollectionUtils.isNotEmpty(supplierInvites)) {
            // 获取供应商信息
            List<Long> supplierIds = supplierInvites.stream().map(SrmTenderSupplierInvite::getTenantSupplierId).toList();
            List<SrmPlatformSupplierInfo> srmPlatformSupplierInfos = supplierService.lambdaQuery()
                    .in(SrmPlatformSupplierInfo::getId, supplierIds).list();
            Map<Long, SrmPlatformSupplierInfo> idMapVo = srmPlatformSupplierInfos.stream()
                    .collect(Collectors.toMap(SrmPlatformSupplierInfo::getId, Function.identity(), (x, y) -> x));
            // 将供应商邀请信息分组到对应的标段中
            List<SrmTenderNoticeAddReq.BidsSegment> inviteSegments = supplierInvites.stream()
                    .collect(Collectors.groupingBy(SrmTenderSupplierInvite::getSectionId))
                    .entrySet().stream()
                    .map(entry -> {
                        SrmTenderNoticeAddReq.BidsSegment segment = new SrmTenderNoticeAddReq.BidsSegment();
                        segment.setRequirementType(BidsSegmentTypeEnum.INVITE_SUPPLIERS);
                        segment.setSectionId(entry.getKey());

                        List<SrmTenderNoticeAddReq.InviteSuppliers> inviteSuppliers = entry.getValue().stream()
                                .map(invite -> {
                                    SrmTenderNoticeAddReq.InviteSuppliers supplier = new SrmTenderNoticeAddReq.InviteSuppliers();
                                    supplier.setSectionId(invite.getSectionId());
                                    supplier.setTenantSupplierId(invite.getTenantSupplierId());
                                    supplier.setSupplierName(invite.getSupplierName());
                                    SrmPlatformSupplierInfo srmPlatformSupplierInfo = idMapVo.get(invite.getTenantSupplierId());
                                    if(srmPlatformSupplierInfo != null){
                                        supplier.setContactName(srmPlatformSupplierInfo.getLegalPerson());
                                        supplier.setContactPhone(srmPlatformSupplierInfo.getLegalPersonPhone());
                                    }
                                    return supplier;
                                }).collect(Collectors.toList());

                        segment.setInviteSuppliers(inviteSuppliers);
                        return segment;
                    }).collect(Collectors.toList());

            // 合并或添加邀请供应商信息到bidsSegments
            if (resp.getBidsSegments() != null) {
                resp.getBidsSegments().addAll(inviteSegments);
            } else {
                resp.setBidsSegments(inviteSegments);
            }
        }
        return resp;
    }


    @Override
    public SrmTenderNotice getEffectNotice(Long projectId) {
        SrmTenderNotice tenderNotice = this.lambdaQuery()
                .eq(SrmTenderNotice::getProjectId, projectId)
                .eq(SrmTenderNotice::getStatus, PublicNoticeStatusEnum.PUBLISHED)
                .one();
        ExceptionUtil.checkNonNull(tenderNotice, "项目没有进行中的招标公告");
        tenderNotice.setQuoteRoundVoList(srmTenderOpenService.getSectionQuoteRound(tenderNotice.getId()));
        // 招标要求列表
        List<SrmTenderRequirement> requirementList = srmTenderRequirementService.lambdaQuery()
                .eq(SrmTenderRequirement::getNoticeId, tenderNotice.getId()).list();
        tenderNotice.setRequirementList(requirementList);
        // 判断当前项目下是否有需要保证金的标段
        SrmProcurementProject procurementProject = projectService.getById(projectId);
        SrmProcurementProject srmProcurementProject = projectService.detail(procurementProject.getProjectCode(), false);
        List<SrmProcurementProjectSection> projectSectionList = srmProcurementProject.getProjectSectionList();
        List<Long> sectionIds = projectSectionList.stream().map(SrmProcurementProjectSection::getId).toList();
        Map<Long, Boolean> depositEffInfoBySectionIds = requirementService.isNeedEffInfoBySectionIds(tenderNotice.getId(), sectionIds, BidsSegmentTypeEnum.FEE);
        tenderNotice.setNeedDeposit(depositEffInfoBySectionIds.containsValue(true));
        Map<Long, Boolean> tenderFeeEffInfoBySectionIds = requirementService.isNeedEffInfoBySectionIds(tenderNotice.getId(), sectionIds, BidsSegmentTypeEnum.BID_DOCUMENT);
        tenderNotice.setNeedTenderFee(tenderFeeEffInfoBySectionIds.containsValue(true));
        // 附件信息
        List<SrmProjectAttachment> attachmentList = attachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId, tenderNotice.getId())
                .list();
        tenderNotice.setAttachmentList(attachmentList);
        return tenderNotice;
    }

    /**
     * （作废此方法） 获取当前项目下的有效招标公告
     * @param noticeId
     */
    @Override
    @Deprecated
    public void addTenderNoticeToReview(String noticeId) {
        SrmTenderNotice srmTenderNotice = this.getById(noticeId);
        ExceptionUtil.checkNonNull(srmTenderNotice, "公告不存在");
        lambdaUpdate().set(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVING)
                .eq(SrmTenderNotice::getId, noticeId)
                .update();
        Long projectId = srmTenderNotice.getProjectId();
        // 项目是否存在
        SrmProcurementProject project = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(project, "项目不存在");
        // 业务流程发起
        ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
        startReq.setBizKey(String.valueOf(noticeId));
        startReq.setBizId(projectId);
        startReq.setArgs(List.of(project.getProjectCode(),projectId,noticeId));
        startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_TENDER_NOTICE_AUDIT);
        startReq.setApprovalType(startReq.getApprovalType());
        startReq.setSpecialProcessExecutorList(startReq.getSpecialProcessExecutorList());
        srmProcessInstanceService.startProcessInstance(startReq);
        log.info("业务流程发起完成！");
    }

    @Override
    public boolean match(SrmProcessConfigService.BizTypeEnum bizType) {
        return bizType == SrmProcessConfigService.BizTypeEnum.SRM_TENDER_NOTICE_AUDIT;
    }

    @Override
    public void approveRejectHook(String bizKey) {
        this.lambdaUpdate().set(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVE_REVOKE)
                .eq(SrmTenderNotice::getId, bizKey).update();
    }

    @Override
    public void noticeApprovingHook(String noticeId) {
        this.lambdaUpdate().set(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVING)
                .eq(SrmTenderNotice::getId, noticeId).update();
    }
}