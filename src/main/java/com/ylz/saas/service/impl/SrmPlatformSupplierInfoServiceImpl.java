package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.common.sequence.generator.impl.DefaultCodeGenerator;
import com.ylz.saas.entity.SrmPlatformSupplierInfo;
import com.ylz.saas.entity.SrmTenantSupplierDept;
import com.ylz.saas.entity.SrmTenantSupplierInfo;
import com.ylz.saas.enums.SupplierApprovalStatus;
import com.ylz.saas.enums.SupplierDeptRelationType;
import com.ylz.saas.enums.SupplierSourceEnum;
import com.ylz.saas.mapper.SrmPlatformSupplierInfoMapper;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.service.SrmPlatformSupplierInfoService;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.service.SrmProcessInstanceService;
import com.ylz.saas.service.SrmTenantSupplierBankAccountService;
import com.ylz.saas.service.SrmTenantSupplierCertificateService;
import com.ylz.saas.service.SrmTenantSupplierContactService;
import com.ylz.saas.service.SrmTenantSupplierDeptService;
import com.ylz.saas.service.SrmTenantSupplierInfoService;
import com.ylz.saas.service.SrmTenantSupplierMaterialService;
import com.ylz.saas.vo.SrmPlatformSupplierInfoVo;
import com.ylz.saas.vo.SupplierBankAccountInfoVo;
import com.ylz.saas.vo.SupplierBasicInfoDetailVo;
import com.ylz.saas.vo.SupplierBasicInfoVo;
import com.ylz.saas.vo.SupplierCertificateInfoVo;
import com.ylz.saas.vo.SupplierCompleteInfoVo;
import com.ylz.saas.vo.SupplierContactInfoVo;
import com.ylz.saas.vo.SupplierMaterialInfoVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【srm_platform_supplier_info(供应商基础信息表)】的数据库操作Service实现
 * @createDate 2025-06-10 18:08:09
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmPlatformSupplierInfoServiceImpl extends ServiceImpl<SrmPlatformSupplierInfoMapper, SrmPlatformSupplierInfo>
        implements SrmPlatformSupplierInfoService {

    private final SrmTenantSupplierInfoService srmTenantSupplierInfoService;
    private final SrmTenantSupplierDeptService srmTenantSupplierDeptService;
    private final DefaultCodeGenerator codeGenerator;
    private final SrmTenantSupplierContactService srmTenantSupplierContactService;
    private final SrmTenantSupplierCertificateService srmTenantSupplierCertificateService;
    private final SrmTenantSupplierBankAccountService srmTenantSupplierBankAccountService;
    private final SrmTenantSupplierMaterialService srmTenantSupplierMaterialService;
    private final SrmProcessInstanceService srmProcessInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SrmTenantSupplierInfo saveSupplierBasicInfo(SupplierBasicInfoVo supplierBasicInfoVo) {
        log.info("开始保存供应商基本信息，供应商名称：{}", supplierBasicInfoVo.getSupplierName());
        supplierBasicInfoVo.setApprovalStatus(SupplierApprovalStatus.WAIT_APPROVAL.name());
//        supplierBasicInfoVo.
//        supplierBasicInfoVo.setSupplierSource(SupplierSourceEnum.SRM_SYSTEM.name());
        SrmPlatformSupplierInfo platformSupplier = null;

        // 根据统一信用代码查询是否存在供应商，并进行重复校验
        if (StrUtil.isNotBlank(supplierBasicInfoVo.getSocialCreditCode())) {
            SrmTenantSupplierInfo one = srmTenantSupplierInfoService.lambdaQuery()
                    .eq(SrmTenantSupplierInfo::getSocialCreditCode, supplierBasicInfoVo.getSocialCreditCode())
                    .ne(supplierBasicInfoVo.getId() != null, SrmTenantSupplierInfo::getId, supplierBasicInfoVo.getId())
                    .one();

            if (one != null) {
                ExceptionUtil.check(true, "500", "统一社会信用代码已存在，供应商名称：" + one.getSupplierName());
            }


            platformSupplier = this.getOne(new LambdaQueryWrapper<SrmPlatformSupplierInfo>()
                    .eq(SrmPlatformSupplierInfo::getSocialCreditCode, supplierBasicInfoVo.getSocialCreditCode())
                    .eq(SrmPlatformSupplierInfo::getDelFlag, 0));


        }


        // 如果不存在，则新建供应商信息
        if (platformSupplier == null) {
            platformSupplier = new SrmPlatformSupplierInfo();
//            BeanUtil.copyProperties(supplierBasicInfoVo,platformSupplier);
            platformSupplier.setSupplierCode(codeGenerator.generateWithDate(CodeGeneratorPrefixEnum.PGYS.name()));
            platformSupplier.setApprovalStatus(SupplierApprovalStatus.WAIT_APPROVAL.name());

        } else {
            platformSupplier.setApprovalStatus(platformSupplier.getApprovalStatus());

        }

        // 只保存平台级别的关键信息
        platformSupplier.setLegalPerson(supplierBasicInfoVo.getLegalPerson());
        platformSupplier.setLegalPersonId(supplierBasicInfoVo.getLegalPersonId());
        platformSupplier.setLegalPersonPhone(supplierBasicInfoVo.getLegalPersonPhone());
        platformSupplier.setRegisteredAddress(supplierBasicInfoVo.getRegisteredAddress());
        platformSupplier.setCompanyProfile(supplierBasicInfoVo.getCompanyProfile());
        platformSupplier.setSocialCreditCode(supplierBasicInfoVo.getSocialCreditCode());
        platformSupplier.setEstablishDate(supplierBasicInfoVo.getEstablishDate() != null ?
                new java.sql.Timestamp(supplierBasicInfoVo.getEstablishDate().getTime()).toLocalDateTime() : null);
        platformSupplier.setRegisteredCapital(supplierBasicInfoVo.getRegisteredCapital());
        platformSupplier.setSupplierSource(supplierBasicInfoVo.getSupplierSource());
        platformSupplier.setRemark(supplierBasicInfoVo.getRemark());
        platformSupplier.setSupplierType(supplierBasicInfoVo.getSupplierType());
        platformSupplier.setDelFlag(0);
        // 注意：SrmPlatformSupplierInfo不设置租户ID，为全租户通用数据
        platformSupplier.setTenantId(null);
        platformSupplier.setSupplierName(supplierBasicInfoVo.getSupplierName());
        platformSupplier.setSupplierShortName(supplierBasicInfoVo.getSupplierShortName());
        platformSupplier.setLetterOfAuthorization(supplierBasicInfoVo.getLetterOfAuthorization());
        this.saveOrUpdate(platformSupplier);


//        Long id = platformSupplier.getId();
        Long id = supplierBasicInfoVo.getId();
//        SrmTenantSupplierInfo exist = srmTenantSupplierInfoService.lambdaQuery().eq(SrmTenantSupplierInfo::getPlatformSupplierId, id).one();


        // 在租户供应商关联表中创建关联记录
        SrmTenantSupplierInfo tenantSupplier = new SrmTenantSupplierInfo();
        if (id != null) {
            SrmTenantSupplierInfo exist = srmTenantSupplierInfoService.getById(id);
            tenantSupplier.setSupplierCode(exist.getSupplierCode());
            tenantSupplier.setId(exist.getId());

        } else {
            tenantSupplier.setApprovalStatus(SupplierApprovalStatus.WAIT_APPROVAL.name());
            tenantSupplier.setSupplierCode(codeGenerator.generateWithDate(CodeGeneratorPrefixEnum.GYS.name()));
            tenantSupplier.setDelFlag(0);
        }
        tenantSupplier.setPlatformSupplierId(platformSupplier.getId());
        tenantSupplier.setLetterOfAuthorization(supplierBasicInfoVo.getLetterOfAuthorization());

        // 设置基础信息字段（现在存储在租户表中）
        tenantSupplier.setApprovalStatus(supplierBasicInfoVo.getApprovalStatus());

        tenantSupplier.setSupplierName(supplierBasicInfoVo.getSupplierName());
        tenantSupplier.setRegisteredCapital(supplierBasicInfoVo.getRegisteredCapital());
        tenantSupplier.setSupplierShortName(supplierBasicInfoVo.getSupplierShortName());
        tenantSupplier.setSocialCreditCode(supplierBasicInfoVo.getSocialCreditCode());
        tenantSupplier.setEstablishDate(supplierBasicInfoVo.getEstablishDate() != null ?
                new java.sql.Timestamp(supplierBasicInfoVo.getEstablishDate().getTime()).toLocalDateTime() : null);
        tenantSupplier.setSupplierType(supplierBasicInfoVo.getSupplierType());
        tenantSupplier.setEnterpriseNature(supplierBasicInfoVo.getEnterpriseNature());
        tenantSupplier.setLegalPerson(supplierBasicInfoVo.getLegalPerson());
        tenantSupplier.setLegalPersonId(supplierBasicInfoVo.getLegalPersonId());
        tenantSupplier.setLegalPersonPhone(supplierBasicInfoVo.getLegalPersonPhone());
        tenantSupplier.setRegisteredAddress(supplierBasicInfoVo.getRegisteredAddress());
        tenantSupplier.setPostalCode(supplierBasicInfoVo.getPostalCode());
        tenantSupplier.setFax(supplierBasicInfoVo.getFax());
        tenantSupplier.setAnnualRevenue(supplierBasicInfoVo.getAnnualRevenue());
        tenantSupplier.setCompanyProfile(supplierBasicInfoVo.getCompanyProfile());
        tenantSupplier.setBusinessScope(supplierBasicInfoVo.getBusinessScope());

        // 设置租户特有字段
        tenantSupplier.setSupplierCategory(supplierBasicInfoVo.getSupplierCategory());
        tenantSupplier.setSupplierStatus(supplierBasicInfoVo.getSupplierStatus());
        tenantSupplier.setSupplierSource(supplierBasicInfoVo.getSupplierSource());
        tenantSupplier.setServiceStartDate(supplierBasicInfoVo.getServiceStartDate());
        tenantSupplier.setServiceEndDate(supplierBasicInfoVo.getServiceEndDate());
        tenantSupplier.setRemark(supplierBasicInfoVo.getRemark());
        tenantSupplier.setIsAllDept(supplierBasicInfoVo.getIsAllDept());

        // 租户供应商关联表设置租户ID，由MyBatis-Plus自动填充

        boolean result = srmTenantSupplierInfoService.saveOrUpdate(tenantSupplier);

        if (result) {
            log.info("供应商基本信息保存成功，平台供应商ID：{}，租户供应商ID：{}",
                    platformSupplier.getId(), tenantSupplier.getId());

            // 创建供应商与部门的关联关系（仅当isAllDept为false时）
            if (supplierBasicInfoVo.getIsAllDept() == null || !supplierBasicInfoVo.getIsAllDept()) {
                try {
                    List<Long> deptIds = supplierBasicInfoVo.getDeptIds();
                    if (deptIds != null && !deptIds.isEmpty()) {
                        // 使用传入的部门ID集合创建关联关系
                        boolean deptRelationResult = srmTenantSupplierDeptService.createSupplierDeptRelations(
                                tenantSupplier.getId(), deptIds, SupplierDeptRelationType.CREATE.getCode(), tenantSupplier.getApprovalStatus());
                        if (deptRelationResult) {
                            log.info("供应商部门关联关系批量创建成功，供应商ID：{}，部门ID集合：{}",
                                    tenantSupplier.getId(), deptIds);
                        } else {
                            log.warn("供应商部门关联关系批量创建失败，供应商ID：{}，部门ID集合：{}",
                                    tenantSupplier.getId(), deptIds);
                        }
                    } else {
                        // 如果没有传入部门ID集合，则使用当前用户的部门ID作为兜底
                        Long currentUserDeptId = SecurityUtils.getUser().getDeptId();
                        if (currentUserDeptId != null) {
                            boolean deptRelationResult = srmTenantSupplierDeptService.createSupplierDeptRelation(
                                    tenantSupplier.getId(), currentUserDeptId, SupplierDeptRelationType.CREATE.getCode());
                            if (deptRelationResult) {
                                log.info("供应商部门关联关系创建成功（使用当前用户部门），供应商ID：{}，部门ID：{}",
                                        tenantSupplier.getId(), currentUserDeptId);
                            } else {
                                log.warn("供应商部门关联关系创建失败（使用当前用户部门），供应商ID：{}，部门ID：{}",
                                        tenantSupplier.getId(), currentUserDeptId);
                            }
                        } else {
                            log.warn("当前用户部门ID为空且未传入部门ID集合，无法创建供应商部门关联关系");
                        }
                    }
                } catch (Exception e) {
                    log.error("创建供应商部门关联关系时发生异常：{}", e.getMessage(), e);
                    // 不影响主流程，继续执行
                }
            } else {
                log.info("供应商设置为所有组织可用，跳过部门关联关系创建，供应商ID：{}", tenantSupplier.getId());
            }
        } else {
            log.error("供应商基本信息保存失败");
        }

        return tenantSupplier;
    }

    @Override
    public IPage<SrmPlatformSupplierInfoVo> getPlatformSupplierPage(SrmPlatformSupplierInfo info, Page<SrmPlatformSupplierInfo> pageParams) {
        log.info("开始查询平台供应商信息，查询条件：{}", info);

        // 提取供应商名称用于模糊查询
        String supplierName = info.getSupplierName();
        info.setSupplierName(null);

        // 只查询审核状态为"审核通过"的供应商
        info.setApprovalStatus(SupplierApprovalStatus.APPROVED.name());
//        只查询门户来源的供应商
        info.setSupplierSource(SupplierSourceEnum.SUPPLIER_REGISTER.name());
        // 构建查询条件
        QueryWrapper<SrmPlatformSupplierInfo> wrapper = buildPlatformSupplierQueryWrapper(info, supplierName);

        // 执行分页查询
        Page<SrmPlatformSupplierInfo> platformSupplierInfoPage = this.page(pageParams, wrapper);

        // 获取当前用户的部门ID，用于判断关系
        Long currentUserDeptId = getCurrentUserDeptId();

        // 转换为VO并设置关系标识
        List<SrmPlatformSupplierInfoVo> supplierInfoVoList = buildPlatformSupplierVoList(
                platformSupplierInfoPage.getRecords(), currentUserDeptId);

        // 构建返回结果
        return buildPlatformSupplierResultPage(platformSupplierInfoPage, supplierInfoVoList);
    }

    @Override
    public boolean checkSupplierRelation(Long platformSupplierId, Long deptId) {
        if (platformSupplierId == null || deptId == null) {
            return false;
        }

        try {
            // 通过平台供应商ID查找对应的租户供应商ID
            SrmTenantSupplierInfo tenantSupplier = srmTenantSupplierInfoService.lambdaQuery()
                    .eq(SrmTenantSupplierInfo::getPlatformSupplierId, platformSupplierId)
                    .one();

            if (tenantSupplier == null) {
                return false;
            }

            // 查询该租户供应商是否与指定部门有关联关系
            List<Long> relatedSupplierIds = srmTenantSupplierDeptService.getTenantSupplierIdsByDeptId(deptId);
            return relatedSupplierIds != null && relatedSupplierIds.contains(tenantSupplier.getId());

        } catch (Exception e) {
            log.error("检查供应商关系时发生异常，平台供应商ID：{}，部门ID：{}", platformSupplierId, deptId, e);
            return false;
        }
    }

    /**
     * 构建平台供应商查询条件
     *
     * @param info 查询条件对象
     * @param supplierName 供应商名称（用于模糊查询）
     * @return 查询条件包装器
     */
    private QueryWrapper<SrmPlatformSupplierInfo> buildPlatformSupplierQueryWrapper(SrmPlatformSupplierInfo info, String supplierName) {
        QueryWrapper<SrmPlatformSupplierInfo> wrapper = QueryWrapperHelper.fromBean(info, SrmPlatformSupplierInfo.class)
                .orderByDesc("id");

        // 添加供应商名称模糊查询
        if (StrUtil.isNotBlank(supplierName)) {
            wrapper.like("supplier_name", supplierName);
        }

        return wrapper;
    }

    /**
     * 构建平台供应商VO列表
     *
     * @param platformSuppliers 平台供应商实体列表
     * @param currentUserDeptId 当前用户部门ID
     * @return 平台供应商VO列表
     */
    private List<SrmPlatformSupplierInfoVo> buildPlatformSupplierVoList(List<SrmPlatformSupplierInfo> platformSuppliers, Long currentUserDeptId) {
        return platformSuppliers.stream()
                .map(item -> {
                    SrmPlatformSupplierInfoVo vo = BeanUtil.toBean(item, SrmPlatformSupplierInfoVo.class);
                    // 设置关系标识：检查该平台供应商是否与当前用户部门已建立关系
                    boolean hasRelation = checkSupplierRelation(item.getId(), currentUserDeptId);
                    vo.setHasRelation(hasRelation);
                    return vo;
                })
                .toList();
    }

    /**
     * 构建平台供应商分页结果
     *
     * @param platformSupplierInfoPage 原始分页结果
     * @param supplierInfoVoList VO列表
     * @return 最终分页结果
     */
    private IPage<SrmPlatformSupplierInfoVo> buildPlatformSupplierResultPage(Page<SrmPlatformSupplierInfo> platformSupplierInfoPage,
                                                                             List<SrmPlatformSupplierInfoVo> supplierInfoVoList) {
        Page<SrmPlatformSupplierInfoVo> resultPage = new Page<>();
        resultPage.setRecords(supplierInfoVoList);
        resultPage.setTotal(platformSupplierInfoPage.getTotal());
        resultPage.setSize(platformSupplierInfoPage.getSize());
        resultPage.setCurrent(platformSupplierInfoPage.getCurrent());
        return resultPage;
    }

    /**
     * 获取当前用户的部门ID
     *
     * @return 部门ID，获取失败时返回null
     */
    private Long getCurrentUserDeptId() {
        try {
            return SecurityUtils.getUser().getDeptId();
        } catch (Exception e) {
            log.error("获取当前用户部门信息失败", e);
            return null;
        }
    }

    /**
     * 获取指定部门关联的供应商ID列表
     *
     * @param deptId 部门ID
     * @return 供应商ID列表
     */
    private List<Long> getRelatedSupplierIds(Long deptId) {
        if (deptId == null) {
            return List.of();
        }

        try {
            List<Long> supplierIds = srmTenantSupplierDeptService.getTenantSupplierIdsByDeptId(deptId);
            return supplierIds != null ? supplierIds : List.of();
        } catch (Exception e) {
            log.error("查询部门关联供应商失败，部门ID：{}", deptId, e);
            return List.of();
        }
    }

    @Override
    public SupplierBasicInfoDetailVo getSupplierBySupplierDeptId(Long supplierDeptId) {
        SrmTenantSupplierDept byId = srmTenantSupplierDeptService.getById(supplierDeptId);
        if (byId == null) {
            return null;
        }
        Long tenantSupplierId = byId.getTenantSupplierId();
        SrmPlatformSupplierInfo supplierInfo = this.getById(tenantSupplierId);
        SupplierBasicInfoDetailVo supplierBasicInfoDetailVo = BeanUtil.copyProperties(supplierInfo, SupplierBasicInfoDetailVo.class);

        supplierBasicInfoDetailVo.setDeptApprovalStatus(byId.getApprovalStatus());
        return supplierBasicInfoDetailVo;
    }

    @Override
    public SupplierBasicInfoDetailVo getSupplierBasicInfoDetail(String code) {
        log.info("开始查询供应商基础信息详情，租户供应商CODE：{}", code);

        // 查询租户供应商信息
        SrmTenantSupplierInfo tenantSupplierInfo = srmTenantSupplierInfoService.lambdaQuery().eq(SrmTenantSupplierInfo::getSupplierCode, code).one();
        if (tenantSupplierInfo == null) {
            log.error("租户供应商信息不存在，CODE：{}", code);
            throw new RuntimeException("供应商信息不存在");
        }

        // 查询平台供应商信息
        SrmPlatformSupplierInfo platformSupplierInfo = this.getById(tenantSupplierInfo.getPlatformSupplierId());
        if (platformSupplierInfo == null) {
            log.error("平台供应商信息不存在，ID：{}", tenantSupplierInfo.getPlatformSupplierId());
            throw new RuntimeException("平台供应商信息不存在");
        }

        // 构建返回对象
        SupplierBasicInfoDetailVo detailVo = new SupplierBasicInfoDetailVo();
        BeanUtil.copyProperties(tenantSupplierInfo, detailVo);
//        // 设置平台供应商信息
//        detailVo.setPlatformSupplierId(platformSupplierInfo.getId());
//        detailVo.setSupplierCode(platformSupplierInfo.getSupplierCode());
//        detailVo.setSupplierName(platformSupplierInfo.getSupplierName());
//        detailVo.setSupplierShortName(platformSupplierInfo.getSupplierShortName());
//        detailVo.setSupplierType(platformSupplierInfo.getSupplierType());
//        detailVo.setEnterpriseNature(platformSupplierInfo.getEnterpriseNature());
//        detailVo.setSupplierSource(platformSupplierInfo.getSupplierSource());
//        detailVo.setApprovalStatus(platformSupplierInfo.getApprovalStatus());
//        detailVo.setSocialCreditCode(platformSupplierInfo.getSocialCreditCode());
//        detailVo.setEstablishDate(platformSupplierInfo.getEstablishDate() != null ?
//                Date.from(platformSupplierInfo.getEstablishDate().atZone(java.time.ZoneId.systemDefault()).toInstant()) : null);
//        detailVo.setRegisteredCapital(platformSupplierInfo.getRegisteredCapital());
//        detailVo.setLegalPerson(platformSupplierInfo.getLegalPerson());
//        detailVo.setLegalPersonId(platformSupplierInfo.getLegalPersonId());
//        detailVo.setLegalPersonPhone(platformSupplierInfo.getLegalPersonPhone());
//        detailVo.setRegisteredAddress(platformSupplierInfo.getRegisteredAddress());
//        detailVo.setPostalCode(platformSupplierInfo.getPostalCode());
//        detailVo.setFax(platformSupplierInfo.getFax());
//        detailVo.setAnnualRevenue(platformSupplierInfo.getAnnualRevenue());
//        detailVo.setCompanyProfile(platformSupplierInfo.getCompanyProfile());
//        detailVo.setBusinessScope(platformSupplierInfo.getBusinessScope());
//        detailVo.setRemark(platformSupplierInfo.getRemark());
//
//        // 设置租户供应商信息
//        detailVo.setTenantSupplierId(tenantSupplierInfo.getId());
//        detailVo.setSupplierCategory(tenantSupplierInfo.getSupplierCategory());
//        detailVo.setSupplierStatus(tenantSupplierInfo.getSupplierStatus());
//        detailVo.setServiceStartDate(tenantSupplierInfo.getServiceStartDate());
//        detailVo.setServiceEndDate(tenantSupplierInfo.getServiceEndDate());
////        detailVo.setTenantRemark(tenantSupplierInfo.getRemark());
//        detailVo.setIsAllDept(tenantSupplierInfo.getIsAllDept());

        // 查询关联部门信息（仅当isAllDept为false时）
        if (tenantSupplierInfo.getIsAllDept() == null || !tenantSupplierInfo.getIsAllDept()) {
            List<SrmTenantSupplierDept> deptRelations = srmTenantSupplierDeptService.getDeptsByTenantSupplierId(tenantSupplierInfo.getId());
            if (deptRelations != null && !deptRelations.isEmpty()) {
                List<Long> deptIds = deptRelations.stream()
                        .map(SrmTenantSupplierDept::getDeptId)
                        .collect(Collectors.toList());
                detailVo.setDeptIds(deptIds);

                List<SupplierBasicInfoDetailVo.DeptRelationInfo> deptRelationInfos = deptRelations.stream()
                        .map(dept -> {
                            SupplierBasicInfoDetailVo.DeptRelationInfo info = new SupplierBasicInfoDetailVo.DeptRelationInfo();
                            info.setRelationId(dept.getId());
                            info.setDeptId(dept.getDeptId());
                            info.setDeptName(dept.getDeptName());
                            info.setRelationType(dept.getRelationType());
                            info.setApprovalStatus(dept.getApprovalStatus());
                            info.setRemark(dept.getRemark());
                            return info;
                        })
                        .collect(Collectors.toList());
                detailVo.setDeptRelations(deptRelationInfos);
            }
        }

        log.info("供应商基础信息详情查询完成，供应商名称：{}", detailVo.getSupplierName());
        return detailVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SrmTenantSupplierInfo saveSupplierCompleteInfo(SupplierCompleteInfoVo supplierCompleteInfoVo) {
        return saveSupplierCompleteInfo(supplierCompleteInfoVo, false);
    }

    /**
     * 提交模式下保存供应商完整信息
     */
    @Transactional(rollbackFor = Exception.class)
    public SrmTenantSupplierInfo saveSupplierCompleteInfoForSubmit(SupplierCompleteInfoVo supplierCompleteInfoVo) {
        return saveSupplierCompleteInfo(supplierCompleteInfoVo, true);
    }

    /**
     * 保存供应商完整信息
     *
     * @param supplierCompleteInfoVo 供应商完整信息
     * @param isSubmitMode 是否为提交模式
     * @return 租户供应商信息
     */
    @Transactional(rollbackFor = Exception.class)
    public SrmTenantSupplierInfo saveSupplierCompleteInfo(SupplierCompleteInfoVo supplierCompleteInfoVo, boolean isSubmitMode) {
        log.info("开始保存供应商完整信息，供应商名称：{}，提交模式：{}", supplierCompleteInfoVo.getSupplierName(), isSubmitMode);

        try {
            // 1. 保存基本信息并获取供应商ID
            SrmTenantSupplierInfo tenantSupplierInfo = saveBasicInfoAndGetId(supplierCompleteInfoVo);

            // 2. 并行处理删除和保存操作
            SupplierSaveContext context = new SupplierSaveContext(tenantSupplierInfo.getId(), supplierCompleteInfoVo, isSubmitMode);
            executeSupplierOperations(context);

            log.info("供应商完整信息保存成功，租户供应商ID：{}，提交模式：{}", tenantSupplierInfo.getId(), isSubmitMode);
            return tenantSupplierInfo;

        } catch (SupplierSaveException e) {
            log.error("保存供应商完整信息失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("保存供应商完整信息时发生未知异常：{}", e.getMessage(), e);
            throw new SupplierSaveException("保存供应商完整信息失败", e);
        }
    }

    /**
     * 供应商保存上下文
     */
    private static class SupplierSaveContext {
        private final Long tenantSupplierId;
        private final SupplierCompleteInfoVo supplierInfo;
        private final boolean isSubmitMode;

        public SupplierSaveContext(Long tenantSupplierId, SupplierCompleteInfoVo supplierInfo) {
            this(tenantSupplierId, supplierInfo, false);
        }

        public SupplierSaveContext(Long tenantSupplierId, SupplierCompleteInfoVo supplierInfo, boolean isSubmitMode) {
            this.tenantSupplierId = tenantSupplierId;
            this.supplierInfo = supplierInfo;
            this.isSubmitMode = isSubmitMode;
        }

        public Long getTenantSupplierId() {
            return tenantSupplierId;
        }

        public SupplierCompleteInfoVo getSupplierInfo() {
            return supplierInfo;
        }

        public boolean isSubmitMode() {
            return isSubmitMode;
        }
    }

    /**
     * 自定义异常类
     */
    private static class SupplierSaveException extends RuntimeException {
        public SupplierSaveException(String message) {
            super(message);
        }

        public SupplierSaveException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 保存基本信息并返回租户供应商ID
     */
    private SrmTenantSupplierInfo saveBasicInfoAndGetId(SupplierCompleteInfoVo supplierCompleteInfoVo) {
        try {
            SupplierBasicInfoVo basicInfoVo = new SupplierBasicInfoVo();
            BeanUtil.copyProperties(supplierCompleteInfoVo, basicInfoVo);

            SrmTenantSupplierInfo tenantSupplierInfo = saveSupplierBasicInfo(basicInfoVo);
            if (tenantSupplierInfo == null) {
                throw new SupplierSaveException("供应商基本信息保存失败");
            }

            // 回设ID信息
            if (supplierCompleteInfoVo.getId() == null) {
                supplierCompleteInfoVo.setId(tenantSupplierInfo.getId());
                supplierCompleteInfoVo.setPlatformSupplierId(tenantSupplierInfo.getPlatformSupplierId());
            }

            return tenantSupplierInfo;
        } catch (Exception e) {
            throw new SupplierSaveException("保存供应商基本信息失败：" + e.getMessage(), e);
        }
    }

    /**
     * 执行供应商相关操作（删除和保存）
     */
    private void executeSupplierOperations(SupplierSaveContext context) {
        // 1. 先执行删除操作
        batchDeleteOperations(context.getSupplierInfo());

        // 2. 再执行保存操作（顺序执行，保持上下文）
        sequentialSaveOperations(context);
    }

    /**
     * 批量删除操作（顺序执行，保持上下文）
     */
    private void batchDeleteOperations(SupplierCompleteInfoVo supplierInfo) {
        log.debug("开始执行批量删除操作");

        try {
            // 顺序执行删除操作，保持上下文信息

            // 1. 删除联系人
            if (hasData(supplierInfo.getDeleteContactIds())) {
                log.debug("开始删除联系人，数量：{}", supplierInfo.getDeleteContactIds().size());
                for (Long contactId : supplierInfo.getDeleteContactIds()) {
                    srmTenantSupplierContactService.deleteSupplierContact(contactId);
                }
                log.debug("联系人删除完成");
            }

            // 2. 删除资质
            if (hasData(supplierInfo.getDeleteCertificateIds())) {
                log.debug("开始删除资质，数量：{}", supplierInfo.getDeleteCertificateIds().size());
                for (Long certificateId : supplierInfo.getDeleteCertificateIds()) {
                    srmTenantSupplierCertificateService.deleteSupplierCertificate(certificateId);
                }
                log.debug("资质删除完成");
            }

            // 3. 删除银行账户
            if (hasData(supplierInfo.getDeleteBankAccountIds())) {
                log.debug("开始删除银行账户，数量：{}", supplierInfo.getDeleteBankAccountIds().size());
                for (Long bankAccountId : supplierInfo.getDeleteBankAccountIds()) {
                    srmTenantSupplierBankAccountService.deleteSupplierBankAccount(bankAccountId);
                }
                log.debug("银行账户删除完成");
            }

            // 4. 删除物料关联
            if (hasData(supplierInfo.getDeleteMaterialIds())) {
                log.debug("开始删除物料关联，数量：{}", supplierInfo.getDeleteMaterialIds().size());
                for (Long materialId : supplierInfo.getDeleteMaterialIds()) {
                    srmTenantSupplierMaterialService.deleteSupplierMaterial(materialId);
                }
                log.debug("物料关联删除完成");
            }

            log.debug("所有删除操作执行完成");

        } catch (Exception e) {
            log.error("删除操作执行失败：{}", e.getMessage());
            throw new SupplierSaveException("批量删除操作失败：" + e.getMessage(), e);
        }
    }


    /**
     * 顺序保存操作（保持上下文信息，优化性能）
     */
    private void sequentialSaveOperations(SupplierSaveContext context) {
        Long tenantSupplierId = context.getTenantSupplierId();
        SupplierCompleteInfoVo supplierInfo = context.getSupplierInfo();
        boolean isSubmitMode = context.isSubmitMode();

        log.debug("开始顺序执行保存操作，租户供应商ID：{}，提交模式：{}", tenantSupplierId, isSubmitMode);

        // 统计要保存的数据量
        int totalOperations = 0;
        if (hasData(supplierInfo.getContactList())) totalOperations++;
        if (hasData(supplierInfo.getCertificateList())) totalOperations++;
        if (hasData(supplierInfo.getBankAccountList())) totalOperations++;
        if (hasData(supplierInfo.getMaterialList())) totalOperations++;

        if (totalOperations == 0) {
            log.debug("没有需要保存的子表数据");
            return;
        }

        log.info("开始保存供应商子表数据，总操作数：{}，租户供应商ID：{}", totalOperations, tenantSupplierId);

        try {
            int completedOperations = 0;

            // 1. 保存联系人信息
            if (hasData(supplierInfo.getContactList())) {
                log.debug("保存联系人信息，数量：{}", supplierInfo.getContactList().size());
                batchSaveContacts(tenantSupplierId, supplierInfo.getContactList(), isSubmitMode);
                completedOperations++;
                log.debug("联系人信息保存完成 ({}/{})", completedOperations, totalOperations);
            }

            // 2. 保存资质信息
            if (hasData(supplierInfo.getCertificateList())) {
                log.debug("保存资质信息，数量：{}", supplierInfo.getCertificateList().size());
                batchSaveCertificates(tenantSupplierId, supplierInfo.getCertificateList());
                completedOperations++;
                log.debug("资质信息保存完成 ({}/{})", completedOperations, totalOperations);
            }

            // 3. 保存银行账户信息
            if (hasData(supplierInfo.getBankAccountList())) {
                log.debug("保存银行账户信息，数量：{}", supplierInfo.getBankAccountList().size());
                batchSaveBankAccounts(tenantSupplierId, supplierInfo.getBankAccountList());
                completedOperations++;
                log.debug("银行账户信息保存完成 ({}/{})", completedOperations, totalOperations);
            }

            // 4. 保存物料信息
            if (hasData(supplierInfo.getMaterialList())) {
                log.debug("保存物料信息，数量：{}", supplierInfo.getMaterialList().size());
                batchSaveMaterials(tenantSupplierId, supplierInfo.getMaterialList());
                completedOperations++;
                log.debug("物料信息保存完成 ({}/{})", completedOperations, totalOperations);
            }

            log.info("所有保存操作执行完成，租户供应商ID：{}，完成操作数：{}", tenantSupplierId, completedOperations);

        } catch (Exception e) {
            log.error("保存操作执行失败，租户供应商ID：{}，错误：{}", tenantSupplierId, e.getMessage());
            throw new SupplierSaveException("批量保存操作失败：" + e.getMessage(), e);
        }
    }


    /**
     * 检查列表是否有数据
     */
    private boolean hasData(List<?> list) {
        return list != null && !list.isEmpty();
    }

    /**
     * 批量保存联系人信息
     */
    private void batchSaveContacts(Long tenantSupplierId, List<SupplierContactInfoVo> contactList) {
        batchSaveContacts(tenantSupplierId, contactList, false);
    }

    /**
     * 批量保存联系人信息
     *
     * @param tenantSupplierId 租户供应商ID
     * @param contactList 联系人列表
     * @param isSubmitMode 是否为提交模式
     */
    private void batchSaveContacts(Long tenantSupplierId, List<SupplierContactInfoVo> contactList, boolean isSubmitMode) {
        contactList.forEach(contact -> {
            contact.setSupplierId(tenantSupplierId);
            if (!srmTenantSupplierContactService.saveOrUpdateSupplierContactInfo(contact, isSubmitMode)) {
                throw new SupplierSaveException(
                        String.format("联系人信息保存失败，联系人姓名：%s", contact.getContactName()));
            }
        });
        log.debug("批量保存联系人完成，保存数量：{}，提交模式：{}", contactList.size(), isSubmitMode);
    }

    /**
     * 批量保存资质信息
     */
    private void batchSaveCertificates(Long tenantSupplierId, List<SupplierCertificateInfoVo> certificateList) {
        certificateList.forEach(certificate -> {
            certificate.setSupplierId(tenantSupplierId);
            if (!srmTenantSupplierCertificateService.saveOrUpdateSupplierCertificateInfo(certificate)) {
                throw new SupplierSaveException(
                        String.format("资质信息保存失败，证书名称：%s", certificate.getCertificateName()));
            }
        });
        log.debug("批量保存资质完成，保存数量：{}", certificateList.size());
    }


    /**
     * 批量保存银行账户信息
     */
    private void batchSaveBankAccounts(Long tenantSupplierId, List<SupplierBankAccountInfoVo> bankAccountList) {
        bankAccountList.forEach(bankAccount -> {
            bankAccount.setSupplierId(tenantSupplierId);
            if (!srmTenantSupplierBankAccountService.saveOrUpdateSupplierBankAccountInfo(bankAccount)) {
                throw new SupplierSaveException(
                        String.format("银行账户信息保存失败，开户行：%s", bankAccount.getBankName()));
            }
        });
        log.debug("批量保存银行账户完成，保存数量：{}", bankAccountList.size());
    }

    /**
     * 批量保存主营物料信息
     */
    private void batchSaveMaterials(Long tenantSupplierId, List<SupplierMaterialInfoVo> materialList) {
        materialList.forEach(material -> {
            material.setSupplierId(tenantSupplierId);
            if (!srmTenantSupplierMaterialService.saveOrUpdateSupplierMaterialInfo(material)) {
                throw new SupplierSaveException(
                        String.format("主营物料信息保存失败，物料ID：%s", material.getMaterialId()));
            }
        });
        log.debug("批量保存主营物料完成，保存数量：{}", materialList.size());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitSupplierInfo(SupplierCompleteInfoVo supplierCompleteInfoVo) {
        log.info("开始提交供应商管理信息，供应商名称：{}", supplierCompleteInfoVo.getSupplierName());

        try {
            // 设置供应商来源为SRM系统
            supplierCompleteInfoVo.setSupplierSource(SupplierSourceEnum.SRM_SYSTEM.name());

            // 调用通用提交方法
            return submitSupplierCommon(supplierCompleteInfoVo, "SRM系统供应商");

        } catch (Exception e) {
            log.error("提交供应商管理信息失败：{}", e.getMessage(), e);
            throw new RuntimeException("提交供应商管理信息失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitRegisterSupplier(SupplierCompleteInfoVo supplierCompleteInfoVo) {
        log.info("开始提交注册供应商信息，供应商名称：{}", supplierCompleteInfoVo.getSupplierName());

        try {
            // 设置供应商来源为供应商注册
            supplierCompleteInfoVo.setSupplierSource(SupplierSourceEnum.SUPPLIER_REGISTER.name());

            // 调用通用提交方法
//            return submitSupplierCommon(supplierCompleteInfoVo, "注册供应商");

            SrmTenantSupplierInfo saveResult = saveSupplierCompleteInfoForSubmit(supplierCompleteInfoVo);
            Long platformSupplierId = saveResult.getPlatformSupplierId();
            Long tenantSupplierId = saveResult.getId();
//         3. 更新平台供应商审核状态
            boolean platformUpdateResult = this.lambdaUpdate()
                    .eq(SrmPlatformSupplierInfo::getId, platformSupplierId)
                    .set(SrmPlatformSupplierInfo::getApprovalStatus, SupplierApprovalStatus.APPROVED.name())
                    .update();

            if (!platformUpdateResult) {
                log.error("更新平台供应商审核状态失败，平台供应商ID：{}", platformSupplierId);
                return false;
            }

//         4. 更新租户供应商审核状态
            boolean tenantUpdateResult = srmTenantSupplierInfoService.lambdaUpdate()
                    .eq(SrmTenantSupplierInfo::getId, tenantSupplierId)
                    .set(SrmTenantSupplierInfo::getApprovalStatus, SupplierApprovalStatus.APPROVED.name())
                    .update();

            if (!tenantUpdateResult) {
                log.error("更新租户供应商审核状态失败，租户供应商ID：{}", tenantSupplierId);
                return false;
            }


            return true;
        } catch (Exception e) {
            log.error("提交注册供应商信息失败：{}", e.getMessage(), e);
            throw new RuntimeException("提交注册供应商信息失败：" + e.getMessage());
        }
    }

    /**
     * 通用供应商提交方法
     * 避免代码重复，统一处理提交逻辑
     */
    private boolean submitSupplierCommon(SupplierCompleteInfoVo supplierCompleteInfoVo, String supplierType) {
        log.info("开始处理{}提交，供应商名称：{}", supplierType, supplierCompleteInfoVo.getSupplierName());

        // 1. 保存供应商完整信息（提交模式）
        SrmTenantSupplierInfo saveResult = saveSupplierCompleteInfoForSubmit(supplierCompleteInfoVo);
        if (saveResult == null) {
            log.error("{}信息保存失败", supplierType);
            return false;
        }

        // 2. 获取平台供应商ID和租户供应商ID
        Long platformSupplierId = supplierCompleteInfoVo.getPlatformSupplierId();
        Long tenantSupplierId = supplierCompleteInfoVo.getId();

        if (platformSupplierId == null || tenantSupplierId == null) {
            log.error("供应商ID为空，平台供应商ID：{}，租户供应商ID：{}", platformSupplierId, tenantSupplierId);
            return false;
        }
        Long deptId = SecurityUtils.getUser().getDeptId();
        SrmTenantSupplierDept one = srmTenantSupplierDeptService.lambdaQuery()
                .eq(SrmTenantSupplierDept::getDeptId, deptId)
                .eq(SrmTenantSupplierDept::getTenantSupplierId, saveResult.getId())
                .one();
        ProcessInstanceStartReq req = new ProcessInstanceStartReq();
        req.setBizKey(saveResult.getSupplierCode());
        req.setBizId(saveResult.getId());
        req.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_SUPPLIER_AUDIT);
        List<Object> args = Lists.newArrayList();
        args.add(saveResult.getId());
        args.add(saveResult.getSupplierCode());
        args.add(saveResult.getSupplierCode());
        if (one != null) {
            args.add(one.getId());
        } else {
            args.add(null);
        }
        req.setArgs(args);
        req.setApprovalType(supplierCompleteInfoVo.getApprovalType());
        req.setSpecialProcessExecutorList(supplierCompleteInfoVo.getSpecialProcessExecutorList());
//        try {
//            srmProcessInstanceService.startProcessInstance(req);
//        } catch (Exception e) {
//            log.error("提交审批失败：{}", e.getMessage());
//        }
//         3. 更新平台供应商审核状态
        boolean platformUpdateResult = this.lambdaUpdate()
                .eq(SrmPlatformSupplierInfo::getId, platformSupplierId)
                .set(SrmPlatformSupplierInfo::getApprovalStatus, SupplierApprovalStatus.APPROVING.name())
                .update();

        if (!platformUpdateResult) {
            log.error("更新平台供应商审核状态失败，平台供应商ID：{}", platformSupplierId);
            return false;
        }

//         4. 更新租户供应商审核状态
        boolean tenantUpdateResult = srmTenantSupplierInfoService.lambdaUpdate()
                .eq(SrmTenantSupplierInfo::getId, tenantSupplierId)
                .set(SrmTenantSupplierInfo::getApprovalStatus, SupplierApprovalStatus.APPROVING.name())
                .update();

        if (!tenantUpdateResult) {
            log.error("更新租户供应商审核状态失败，租户供应商ID：{}", tenantSupplierId);
            return false;
        }
        srmProcessInstanceService.startProcessInstance(req);
//         5. 调用预留审核方法
//        processSupplierApproval(tenantSupplierId, SupplierApprovalStatus.APPROVING.name(),
//                supplierType + "提交审核");

        log.info("{}提交成功，平台供应商ID：{}，租户供应商ID：{}", supplierType, platformSupplierId, tenantSupplierId);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean inviteAndUpdateSupplier(SupplierCompleteInfoVo supplierCompleteInfoVo) {
        log.info("开始邀请供应商并修改信息，供应商名称：{}，租户供应商ID：{}，关联部门数量：{}",
                supplierCompleteInfoVo.getSupplierName(),
                supplierCompleteInfoVo.getId(),
                supplierCompleteInfoVo.getDeptIds() != null ? supplierCompleteInfoVo.getDeptIds().size() : 0);

        try {
            // 1. 验证供应商是否存在且状态正确
            validateSupplierForInvite(supplierCompleteInfoVo.getId());

            // 2. 保存/更新供应商完整信息（提交模式）
            SrmTenantSupplierInfo saveResult = saveSupplierCompleteInfoForSubmit(supplierCompleteInfoVo);
            if (saveResult == null) {
                throw new SupplierSaveException("供应商信息保存失败");
            }

            // 3. 建立部门关联关系
            boolean inviteResult = createInviteRelations(supplierCompleteInfoVo);
            if (!inviteResult) {
                throw new SupplierSaveException("建立邀请关联关系失败");
            }
            Long deptId = SecurityUtils.getUser().getDeptId();
            SrmTenantSupplierDept one = srmTenantSupplierDeptService.lambdaQuery()
                    .eq(SrmTenantSupplierDept::getTenantSupplierId, saveResult.getId())
                    .eq(SrmTenantSupplierDept::getDeptId, deptId)
                    .one();
            ProcessInstanceStartReq req = new ProcessInstanceStartReq();
            req.setBizKey(saveResult.getSupplierCode());
            req.setBizId(saveResult.getId());
            List<Object> args = Lists.newArrayList();
            args.add(saveResult.getId());
            args.add(saveResult.getSupplierCode());
            args.add(saveResult.getSupplierCode());

            if (one != null) {
                args.add(one.getId());

            } else {
                args.add(null);

            }
            req.setArgs(args);
            req.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_SUPPLIER_AUDIT);
            req.setApprovalType(supplierCompleteInfoVo.getApprovalType());
            req.setSpecialProcessExecutorList(supplierCompleteInfoVo.getSpecialProcessExecutorList());
            srmProcessInstanceService.startProcessInstance(req);
            // 4. 调用预留邀请审核方法
//            processSupplierInviteApproval(
//                    supplierCompleteInfoVo.getId(),
//                    supplierCompleteInfoVo.getDeptIds(),
//                    SupplierApprovalStatus.INVITE_APPROVING.name(),
//                    "邀请供应商并修改信息"
//            );

            log.info("邀请供应商并修改信息成功，租户供应商ID：{}", supplierCompleteInfoVo.getId());
            return true;

        } catch (SupplierSaveException e) {
            log.error("邀请供应商并修改信息失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("邀请供应商并修改信息时发生未知异常：{}", e.getMessage(), e);
            throw new SupplierSaveException("邀请供应商并修改信息失败", e);
        }
    }

    @Override
    public SupplierBasicInfoDetailVo getOwnSupplier() {
        SaasUser user = SecurityUtils.getUser();
        String username = user.getUsername();

        SupplierBasicInfoDetailVo supplierBasicInfoDetailVo = this.baseMapper.getOwnSupplier(username);
        if (supplierBasicInfoDetailVo == null) {
            ExceptionUtil.check(true, "500", "当前用户没有关联的供应商");

        }

        return supplierBasicInfoDetailVo;
    }

    /**
     * 验证供应商是否可以被邀请
     */
    private void validateSupplierForInvite(Long tenantSupplierId) {
        if (tenantSupplierId == null) {
            throw new SupplierSaveException("租户供应商ID不能为空");
        }

        SrmTenantSupplierInfo supplierInfo = srmTenantSupplierInfoService.getById(tenantSupplierId);
        if (supplierInfo == null) {
            throw new SupplierSaveException("供应商不存在，ID：" + tenantSupplierId);
        }

        // 验证供应商状态（只有审核通过的供应商才能被邀请）
        if (!SupplierApprovalStatus.APPROVED.name().equals(supplierInfo.getApprovalStatus())) {
            throw new SupplierSaveException(
                    String.format("只能邀请审核通过的供应商，当前状态：%s", supplierInfo.getApprovalStatus()));
        }

        log.debug("供应商验证通过，可以进行邀请操作，供应商ID：{}，供应商名称：{}",
                tenantSupplierId, supplierInfo.getSupplierName());
    }

    /**
     * 创建邀请关联关系
     */
    private boolean createInviteRelations(SupplierCompleteInfoVo supplierCompleteInfoVo) {
        List<Long> deptIds = supplierCompleteInfoVo.getDeptIds();
        Long tenantSupplierId = supplierCompleteInfoVo.getId();

        if (deptIds == null || deptIds.isEmpty()) {
            log.debug("没有指定部门关联，跳过创建邀请关系");
            return true;
        }

        log.info("开始创建邀请关联关系，供应商ID：{}，部门数量：{}", tenantSupplierId, deptIds.size());

        try {
            // 使用邀请关联类型和邀请审核中状态创建关联关系
            boolean result = srmTenantSupplierDeptService.createSupplierDeptRelations(
                    tenantSupplierId,
                    deptIds,
                    SupplierDeptRelationType.INVITE.getCode(),
                    SupplierApprovalStatus.INVITE_APPROVING.name()
            );

            if (result) {
                log.info("邀请关联关系创建成功，供应商ID：{}，关联部门数量：{}", tenantSupplierId, deptIds.size());
            } else {
                log.error("邀请关联关系创建失败，供应商ID：{}", tenantSupplierId);
            }

            return result;

        } catch (Exception e) {
            log.error("创建邀请关联关系时发生异常，供应商ID：{}，错误：{}", tenantSupplierId, e.getMessage());
            throw new SupplierSaveException("创建邀请关联关系失败：" + e.getMessage(), e);
        }
    }
}