package com.ylz.saas.service.impl;


import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.codegen.base_service_type.entity.BaseServiceTypeEntity;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.service.SysDeptService;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.codegen.base_service_type.service.BaseServiceTypeService;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import com.ylz.saas.codegen.srm_procurement_plan.service.SrmProcurementPlanService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.core.exception.GlobalResultCode;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmProcurementProjectItem;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.req.HandleTransferReq;
import com.ylz.saas.req.InitiateTransferReq;
import com.ylz.saas.entity.SrmProjectMember;
import com.ylz.saas.entity.SrmProjectTransferLog;
import com.ylz.saas.enums.ProjectMemberRoleEnum;
import com.ylz.saas.enums.ProjectMemberTypeEnum;
import com.ylz.saas.enums.ProjectTransferStatusEnum;
import com.ylz.saas.mapper.SrmProjectTransferLogMapper;
import com.ylz.saas.service.SrmProcurementProjectItemService;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmProjectMemberService;
import com.ylz.saas.service.SrmProjectTransferLogService;
import com.ylz.saas.vo.PendingTransferProjectVO;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SrmProjectTransferLogServiceImpl extends ServiceImpl<SrmProjectTransferLogMapper, SrmProjectTransferLog> implements SrmProjectTransferLogService {

    @Resource
    private  SrmProjectMemberService srmProjectMemberService;

    @Resource
    private SrmProcurementProjectService srmProcurementProjectService;
    @Resource
    private  SysUserService sysUserService;
    @Resource
    private  SysDeptService sysDeptService;
    @Resource
    private  BaseServiceTypeService baseServiceTypeService;

    @Resource
    private  SrmProcurementProjectItemService srmProcurementProjectItemService;

    @Resource
    private final SrmProcurementPlanService srmProcurementPlanService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String initiateTransfer(InitiateTransferReq req) {
        // --- 获取当前操作人信息 ---
        Long senderUserId = SecurityUtils.getUser().getId();
        String senderUserName = SecurityUtils.getUser().getName();

        // 防范自己转交给自己
        ExceptionUtil.check(senderUserId.equals(req.getReceiverUserId()), GlobalResultCode.INVALID_PARAMS, "不能将项目转交给自己");

        // --- 业务校验 ---
        // 检查当前用户是否是所有待转交项目的负责人
        List<SrmProjectMember> leaders = srmProjectMemberService.list(Wrappers.<SrmProjectMember>lambdaQuery()
                .in(SrmProjectMember::getBusinessId, req.getProjectIds())
                .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER));

        // 将查询到的负责人记录按项目ID分组
        List<Long> leaderProjectIds = leaders.stream()
                .filter(member -> member.getUserId().equals(senderUserId))
                .map(SrmProjectMember::getBusinessId)
                .collect(Collectors.toList());

        // 如果当前用户是负责人的项目数量与请求转交的项目数量不符，说明权限不足
        ExceptionUtil.check(leaderProjectIds.size() != req.getProjectIds().size(), GlobalResultCode.INVALID_PARAMS, "您不是所有选中项目的负责人，无法发起转交");

        //  状态校验：检查项目是否已处于“待转交”状态
        long pendingCount = this.count(Wrappers.<SrmProjectTransferLog>lambdaQuery()
                .in(SrmProjectTransferLog::getProjectId, req.getProjectIds())
                .eq(SrmProjectTransferLog::getStatus, ProjectTransferStatusEnum.PENDING));
        ExceptionUtil.check(pendingCount > 0, GlobalResultCode.INVALID_STATE, "选中的项目中有项目已处于转交中，请勿重复操作");
        
        // 检查项目本身的状态，比如不能是已完成或已作废的项目
        List<SrmProcurementProject> projects = srmProcurementProjectService.listByIds(req.getProjectIds());
        boolean canTransfer = projects.stream().allMatch(p -> p.getStatus() != SrmProcurementProjectService.DocumentStatus.CANCEL );//&& p.getProgressStatus() != ProjectProgressStatusEnum.COMPLETED
        ExceptionUtil.check(!canTransfer, GlobalResultCode.INVALID_STATE, "选中的项目中有项目已完成或作废，无法转交");


        // ---  生成批次号并创建转交记录 ---
        final String batchId = UUID.randomUUID().toString().replace("-", "");

        Map<Long, SrmProcurementProject> projectMap = projects.stream()
                .collect(Collectors.toMap(SrmProcurementProject::getId, p -> p));

        List<SrmProjectTransferLog> logList = req.getProjectIds().stream().map(projectId -> {
            SrmProjectTransferLog log = new SrmProjectTransferLog();
            log.setTransferBatchId(batchId);
            log.setProjectId(projectId);
            SrmProcurementProject project = projectMap.get(projectId);
            if (project != null) {
                log.setProjectCode(project.getProjectCode());
            }
            log.setSenderUserId(senderUserId);
            log.setSenderUserName(senderUserName);
            log.setReceiverUserId(req.getReceiverUserId());
            log.setReceiverUserName(sysUserService.getById(req.getReceiverUserId()).getName());
            log.setStatus(ProjectTransferStatusEnum.PENDING);
            return log;
        }).collect(Collectors.toList());

        // 批量保存到数据库
        this.saveBatch(logList);

        // --- 发送通知 ---
        // 调用消息服务，给接收人发送通知

        log.info("用户[{}]成功发起项目转交，批次号：{}，共{}个项目，接收人ID：{}", senderUserName, batchId, logList.size(), req.getReceiverUserId());
        return("用户"+senderUserName+"成功发起项目转交，批次号："+batchId+"，共"+logList.size()+"个项目，接收人ID："+req.getReceiverUserId());
    }


    @Override
    public Page<PendingTransferProjectVO> getPendingListByBatch(Page<PendingTransferProjectVO> page, String batchId) {
        Long currentUserId = SecurityUtils.getUser().getId();

        // --- 安全校验 & 分页查询转交记录 ---
        Page<SrmProjectTransferLog> logPage = this.page(new Page<>(page.getCurrent(), page.getSize()),
                Wrappers.<SrmProjectTransferLog>lambdaQuery()
                        .eq(SrmProjectTransferLog::getTransferBatchId, batchId)
                        .eq(SrmProjectTransferLog::getStatus, ProjectTransferStatusEnum.PENDING)
                        .eq(SrmProjectTransferLog::getReceiverUserId, currentUserId)
        );

        if (logPage.getRecords().isEmpty()) {
            // 根据批次号查不到任何待办，可能是批次号错误或已处理
            return new Page<>(page.getCurrent(), page.getSize(), 0);
        }

        List<SrmProjectTransferLog> logs = logPage.getRecords();
        List<Long> projectIds = logs.stream().map(SrmProjectTransferLog::getProjectId).toList();

        //  一次性获取所有关联的基础数据 ---
        // 获取项目主信息
        List<SrmProcurementProject> projects = srmProcurementProjectService.listByIds(projectIds);
        Map<Long, SrmProcurementProject> projectMap = projects.stream()
                .collect(Collectors.toMap(SrmProcurementProject::getId, p -> p));

        // 获取所有项目负责人信息
        Map<Long, SrmProjectMember> leaderMap = srmProjectMemberService.list(Wrappers.<SrmProjectMember>lambdaQuery()
                        .in(SrmProjectMember::getBusinessId, projectIds)
                        .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                        .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER))
                .stream().collect(Collectors.toMap(SrmProjectMember::getBusinessId, m -> m, (o1, o2) -> o1));

        // 获取所有涉及到的用户名称
        List<Long> userIds = leaderMap.values().stream().map(SrmProjectMember::getUserId).toList();
        Map<Long, String> userNameMap = CollectionUtils.isEmpty(userIds) ? Collections.emptyMap() :
                sysUserService.listByIds(userIds).stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getName));

        // 获取所有涉及到的部门名称
        List<Long> deptIds = new ArrayList<>();

        // 添加 buyerDeptId
        projects.stream()
                .map(SrmProcurementProject::getBuyerDeptId)
                .filter(java.util.Objects::nonNull)
                .forEach(deptIds::add);
        // 添加 purchaseDeptId
        projects.stream()
                .filter(p -> StringUtils.hasText(p.getPurchaseDeptId()))
                .flatMap(p -> Arrays.stream(p.getPurchaseDeptId().split(",")).map(Long::parseLong))
                .forEach(deptIds::add);

        // 最后去重并查询
        Map<Long, String> deptNameMap = CollectionUtils.isEmpty(deptIds) ? Collections.emptyMap() :
                sysDeptService.listByIds(deptIds.stream().distinct().toList()).stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getName));
        //  获取所有涉及到的业务类型名称
        List<Long> serviceTypeIds = projects.stream().map(SrmProcurementProject::getServiceTypeId).distinct().toList();
        Map<Long, String> serviceTypeNameMap = CollectionUtils.isEmpty(serviceTypeIds) ? Collections.emptyMap() :
                baseServiceTypeService.listByIds(serviceTypeIds).stream().collect(Collectors.toMap(BaseServiceTypeEntity::getId, BaseServiceTypeEntity::getTypeName));

        // 获取所有关联的采购计划编号
        // 根据项目ID列表，查询所有相关的项目物料明细记录
        List<SrmProcurementProjectItem> relationItems = CollectionUtils.isEmpty(projectIds) ? Collections.emptyList() :
                srmProcurementProjectItemService.list(Wrappers.<SrmProcurementProjectItem>lambdaQuery()
                        .in(SrmProcurementProjectItem::getProjectId, projectIds)
                        .isNotNull(SrmProcurementProjectItem::getPlanId));
        // 从物料明细中提取出所有不重复的 planId
        List<Long> planIds = relationItems.stream().map(SrmProcurementProjectItem::getPlanId).distinct().toList();
        // 使用 planId 列表，一次性查询出所有采购计划实体，并转为Map<planId, planCode>
        Map<Long, String> planIdToCodeMap = CollectionUtils.isEmpty(planIds) ? Collections.emptyMap() :
                srmProcurementPlanService.listByIds(planIds).stream()
                        .collect(Collectors.toMap(SrmProcurementPlanEntity::getId, SrmProcurementPlanEntity::getPlanCode));
        // 将物料明细按 projectId 分组，方便后续查找
        Map<Long, List<SrmProcurementProjectItem>> itemsByProjectIdMap = relationItems.stream()
                .collect(Collectors.groupingBy(SrmProcurementProjectItem::getProjectId));
        // 组装最终的VO ---
        Page<PendingTransferProjectVO> resultPage = new Page<>(logPage.getCurrent(), logPage.getSize(), logPage.getTotal());

        List<PendingTransferProjectVO> voList = logs.stream().map(log -> {
            PendingTransferProjectVO vo = new PendingTransferProjectVO();
            SrmProcurementProject project = projectMap.get(log.getProjectId());

            if (project != null) {
                //  拷贝 SrmProcurementProject 的基础属性
                BeanUtils.copyProperties(project, vo);
                vo.setProjectId(project.getId());

                //  手动组装名称字段
                vo.setServiceTypeName(serviceTypeNameMap.get(project.getServiceTypeId()));
                vo.setBuyerDeptName(deptNameMap.get(project.getBuyerDeptId()));

                SrmProjectMember leader = leaderMap.get(project.getId());
                if(leader != null) {
                    vo.setProjectLeaderName(userNameMap.get(leader.getUserId()));
                }

                if(StringUtils.hasText(project.getPurchaseDeptId())){
                    String purchaseDeptNames = Arrays.stream(project.getPurchaseDeptId().split(","))
                            .map(idStr -> deptNameMap.get(Long.parseLong(idStr)))
                            .filter(java.util.Objects::nonNull)
                            .collect(Collectors.joining(","));
                    vo.setPurchaseDeptName(purchaseDeptNames);
                }
                List<SrmProcurementProjectItem> currentProjectItems = itemsByProjectIdMap.get(project.getId());
                if (!CollectionUtils.isEmpty(currentProjectItems)) {
                    List<String> planCodes = currentProjectItems.stream()
                            .map(SrmProcurementProjectItem::getPlanId)
                            .map(planIdToCodeMap::get)
                            .filter(java.util.Objects::nonNull)
                            .distinct()
                            .toList();
                    vo.setRelationPlanCodeList(planCodes);
                } else {
                    vo.setRelationPlanCodeList(Collections.emptyList()); // 确保字段不为null
                }
            }

            // 补充转交信息
            vo.setTransferBatchId(log.getTransferBatchId());
            vo.setSenderUserName(log.getSenderUserName());
            vo.setTransferTime(log.getCreateTime());

            return vo;
        }).toList();

        resultPage.setRecords(voList);
        return resultPage;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleTransfer(HandleTransferReq req) {
        Long currentUserId = SecurityUtils.getUser().getId();
        final int ACTION_ACCEPT = 1;

        // --- 查询并校验待处理的转交记录 ---
        List<SrmProjectTransferLog> logsToHandle = this.list(Wrappers.<SrmProjectTransferLog>lambdaQuery()
                .eq(SrmProjectTransferLog::getTransferBatchId, req.getTransferBatchId())
                .eq(SrmProjectTransferLog::getStatus, ProjectTransferStatusEnum.PENDING));

        ExceptionUtil.check(CollectionUtils.isEmpty(logsToHandle), GlobalResultCode.INVALID_PARAMS, "未找到待处理的转交记录，请勿重复操作");
        ExceptionUtil.check(!logsToHandle.get(0).getReceiverUserId().equals(currentUserId), GlobalResultCode.INVALID_PARAMS, "您不是该批次转交的指定接收人");

        // 将 action 字符串转换为整数
        int actionCode = Integer.parseInt(req.getAction());

        // --- 根据动作进行分支处理 ---
        if (actionCode == ACTION_ACCEPT) {
            List<SrmProcurementProject> projectsToUpdate = srmProcurementProjectService.listByIds(
                    logsToHandle.stream().map(SrmProjectTransferLog::getProjectId).toList()
            );

            for (SrmProjectTransferLog log : logsToHandle) {
                //  变更项目负责人
                boolean isSuccess = srmProjectMemberService.lambdaUpdate()
                        .eq(SrmProjectMember::getBusinessId, log.getProjectId())
                        .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                        .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER)
                        .eq(SrmProjectMember::getUserId, log.getSenderUserId())
                        .set(SrmProjectMember::getUserId, log.getReceiverUserId())
                        .update(); // IService.update() 返回 boolean

                // 校验逻辑：检查操作是否成功。
                // 如果 isSuccess 为 false，说明影响行数为0，即没有找到匹配的负责人记录。
                ExceptionUtil.check(!isSuccess, GlobalResultCode.INVALID_STATE, "项目[" + log.getProjectCode() + "]原负责人信息不存在或不匹配，转交失败");
                //  转移数据权限
                projectsToUpdate.stream()
                        .filter(p -> p.getId().equals(log.getProjectId()))
                        .findFirst()
                        .ifPresent(project ->{
                            // 创建一个可变的 newUserIds 集合
                            Set<Long> newMemberIds = new HashSet<>();
                            newMemberIds.add(log.getReceiverUserId());

                            // 2创建一个可变的 existingUserIds 集合
                            Set<Long> oldMemberIds = new HashSet<>();
                            oldMemberIds.add(log.getSenderUserId());

                            // 将可变的集合传入方法
                            srmProcurementProjectService.insertProjectUserIdentityDataPermission(
                                    project,
                                    newMemberIds,
                                    oldMemberIds
                                    );
                        });

            }

            //  更新转交记录状态为 ACCEPTED
            List<Long> logIds = logsToHandle.stream().map(SrmProjectTransferLog::getId).toList();
            this.lambdaUpdate()
                    .in(SrmProjectTransferLog::getId, logIds)
                    .set(SrmProjectTransferLog::getStatus, ProjectTransferStatusEnum.ACCEPTED)
                    .set(SrmProjectTransferLog::getHandleTime, LocalDateTime.now())
                    .update();

            // 站内消息：转交成功

        } else {
            // --- “拒绝”逻辑 ---
            List<Long> logIds = logsToHandle.stream().map(SrmProjectTransferLog::getId).toList();
            this.lambdaUpdate()
                    .in(SrmProjectTransferLog::getId, logIds)
                    .set(SrmProjectTransferLog::getStatus, ProjectTransferStatusEnum.REJECTED)
                    .set(SrmProjectTransferLog::getHandleTime, LocalDateTime.now())
                    .update();

            // 站内消息：转交失败
        }
    }


}