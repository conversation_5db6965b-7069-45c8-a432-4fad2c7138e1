package com.ylz.saas.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.data.tenant.TenantContextHolder;
import com.ylz.saas.common.flowable.utils.SaasFlowUtils;
import com.ylz.saas.entity.SrmProcessConfig;
import com.ylz.saas.entity.SrmProcessInstance;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.YesNoEnum;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.req.ProcessInstanceStopReq;
import com.ylz.saas.resp.ProcessInstanceStartResp;
import com.ylz.saas.resp.ProcessInstanceGroupResp;
import com.ylz.saas.service.ApproveRejectHookService;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.service.SrmProcessInstanceService;
import com.ylz.saas.mapper.SrmProcessInstanceMapper;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【srm_process_instance(供应商响应表)】的数据库操作Service实现
 * @createDate 2025-06-12 16:20:53
 */
@Service
@RequiredArgsConstructor
public class SrmProcessInstanceServiceImpl extends ServiceImpl<SrmProcessInstanceMapper, SrmProcessInstance>
        implements SrmProcessInstanceService {

    private final SrmProcessConfigService srmProcessConfigService;

    @Lazy
    @Autowired(required = false)
    private List<ApproveRejectHookService> approveRejectHookServiceList;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessInstanceStartResp startProcessInstance(ProcessInstanceStartReq req) {
        SrmProcessConfig processConfig = srmProcessConfigService.lambdaQuery().eq(SrmProcessConfig::getBizType, req.getBizType()).one();
        ExceptionUtil.checkNonNull(processConfig, "流程配置不存在！");

        JSONObject variables = JSON.parseObject(String.format(processConfig.getVariables(), req.getArgs().toArray()));
        ProcessInstance processInstance = SaasFlowUtils.start(processConfig.getFlowKey(), req.getBizKey(), String.valueOf(TenantContextHolder.getTenantId()), variables);

        SrmProcessInstance srmProcessInstance = new SrmProcessInstance();
        srmProcessInstance.setType(processConfig.getBizType());
        srmProcessInstance.setFlowKey(processConfig.getFlowKey());
        srmProcessInstance.setBizKey(req.getBizKey());
        srmProcessInstance.setBizId(req.getBizId());
        srmProcessInstance.setInstanceId(processInstance.getProcessInstanceId());
        srmProcessInstance.setApprovalStatus(ApproveStatusEnum.APPROVING);
        this.save(srmProcessInstance);

        ProcessInstanceStartResp startResp = new ProcessInstanceStartResp();
        startResp.setProcessInstanceId(processInstance.getProcessInstanceId());
        return startResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean stopProcessInstance(ProcessInstanceStopReq req) {
        final SrmProcessInstance instance = getOne(Wrappers.lambdaQuery(SrmProcessInstance.class)
                .eq(SrmProcessInstance::getType, req.getBizType())
                .eq(SrmProcessInstance::getBizKey, req.getBizKey()));
        ExceptionUtil.checkNonNull(instance, "要终止的业务流程不存在！");
        for (ApproveRejectHookService approveRejectHookService : approveRejectHookServiceList) {
            if (approveRejectHookService.match(req.getBizType())) {
                approveRejectHookService.approveRejectHook(req.getBizKey());
            }
        }
        this.update(Wrappers.lambdaUpdate(SrmProcessInstance.class)
                .eq(SrmProcessInstance::getId, instance.getId())
                .set(SrmProcessInstance::getApprovalStatus, ApproveStatusEnum.APPROVE_REVOKE)
                .set(SrmProcessInstance::getDelFlag, 1)
        );
//        this.removeById(instance.getId());
        SaasFlowUtils.stop(instance.getInstanceId(), req.getReason());
        return true;
    }


    /**
     * 通过bizId集合和type集合查询流程实例，返回分组封装对象
     * @param bizIds 业务ID集合
     * @param types 业务类型集合
     * @return 分组封装的流程实例对象
     */
    @Override
    public ProcessInstanceGroupResp getInstancesGroupByBizIdAndType(Set<Long> bizIds, Set<SrmProcessConfigService.BizTypeEnum> types) {
        List<SrmProcessInstance> instances = this.baseMapper.getInstancesByBizIdAndType(bizIds, types);
        return ProcessInstanceGroupResp.fromInstances(instances);
    }

    @Override
    public void handlerInstanceStatus(Long bizId, SrmProcessConfigService.BizTypeEnum bizTypeEnum, ApproveStatusEnum status) {
        SrmProcessInstance one = this.lambdaQuery().eq(SrmProcessInstance::getBizId, bizId)
                .eq(SrmProcessInstance::getType, bizTypeEnum)
                .orderByDesc(SrmProcessInstance::getCreateTime)
                .last("limit 1")
                .one();
        if (one == null) {
            return;
        }
        one.setApprovalStatus(status);
        this.updateById(one);

    }
}




