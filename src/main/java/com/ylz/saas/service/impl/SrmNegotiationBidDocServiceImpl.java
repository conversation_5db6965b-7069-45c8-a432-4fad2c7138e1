package com.ylz.saas.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmTenderEvaluationStandard;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.entity.SrmTenderRequirement;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.enums.BidsSegmentTypeEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.req.AttachmentInfoReq;
import com.ylz.saas.req.BidFeeInfoReq;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.req.SrmNegotiationAddReq;
import com.ylz.saas.service.ApproveRejectHookService;
import com.ylz.saas.service.SrmNegotiationBidDocService;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.service.SrmProcessInstanceService;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmProjectAttachmentService;
import com.ylz.saas.service.SrmTenderEvaluationStandardService;
import com.ylz.saas.service.SrmTenderNoticeService;
import com.ylz.saas.service.SrmTenderRequirementService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_negotiation_bid_doc(竞谈文件)】的数据库操作Service实现
* @createDate 2025-07-10 14:31:42
*/
@Slf4j
@Service
public class SrmNegotiationBidDocServiceImpl implements SrmNegotiationBidDocService, ApproveRejectHookService {

    @Resource
    private SrmProcurementProjectService projectService;

    @Resource
    private SrmTenderNoticeService noticeService;

    @Resource
    private SrmProjectAttachmentService attachmentService;

    @Resource
    private SrmTenderEvaluationStandardService standardService;

    @Resource
    private SrmTenderRequirementService requirementService;

    @Resource
    private SrmProcessInstanceService srmProcessInstanceService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addNegotiationDoc(SrmNegotiationAddReq req) {
        // 校验项目是否存在
        Long projectId = req.getProjectId();
        SrmProcurementProject project = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(project, "项目不存在");
        // 校验发标公告是否存在
        Long noticeId = req.getNoticeId();
        SrmTenderNotice notice = noticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(notice, "招标公告不存在");
        List<SrmNegotiationAddReq.BidsSegment> bidsSegments = req.getBidsSegments();
        if (req.getBidFeeCollection()) {
            ExceptionUtil.checkNotEmpty(bidsSegments, "标书费收取时，账号信息不能为空");
            bidsSegments.forEach(bidsSegment -> {
                ExceptionUtil.checkNonNull(bidsSegment.getSectionId(), "标书费&标书文件设置--标段ID不能为空");
                ExceptionUtil.checkNonNull(bidsSegment.getRequirementType(), "标书费&标书文件设置--要求类型不能为空");
                ExceptionUtil.checkNonNull(bidsSegment.getRequirementName(), "标书费&标书文件设置--要求名称不能为空");
                ExceptionUtil.checkNonNull(bidsSegment.getBidFeeInfo(), "标书费&标书文件设置--要求内容不能为空");
            });
        }

        if (req.getFileObtainStartTime().isAfter(req.getFileObtainEndTime())) {
            throw new IllegalArgumentException("文件获取开始时间不能晚于结束时间");
        }
        if (req.getBidDocPayStartTime().isAfter(req.getBidDocPayEndTime())) {
            throw new IllegalArgumentException("标书费缴纳开始时间不能晚于结束时间");
        }
        if (req.getQuoteStartTime().isAfter(req.getQuoteEndTime())) {
            throw new IllegalArgumentException("报价开始时间不能晚于结束时间");
        }
        negotiationCoreHandler(req, false);
        // 业务流程发起
        ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
        String projectAndNoticeId = projectId + "#" + noticeId;
        startReq.setBizKey(String.valueOf(noticeId));
        startReq.setBizId(projectId);
        startReq.setArgs(List.of(project.getProjectCode(),projectId,projectAndNoticeId));
        startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_TENDER_DOC_AUDIT);
        srmProcessInstanceService.startProcessInstance(startReq);
    }

    /**
     * 竞谈文件信息处理
     *
     * @param req
     * @param isChange
     */
    @Override
    public void negotiationCoreHandler(SrmNegotiationAddReq req, boolean isChange) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        List<SrmNegotiationAddReq.BidsSegment> bidsSegments = req.getBidsSegments();
        log.info("处理竞谈文件信息，是否来自变更逻辑：{}，req:{}",isChange, JSONObject.toJSONString(req));
        // 保存竞谈文件主表信息
        noticeService.lambdaUpdate().set(SrmTenderNotice::getFileSubmissionAddress, req.getFileSubmissionAddress())
                .set(SrmTenderNotice::getBidFeeCollection, req.getBidFeeCollection())
                .set(SrmTenderNotice::getBiddingNotice, req.getBiddingNotice())
                .set(SrmTenderNotice::getEvaluationMethod, req.getEvaluationMethod())
                .set(SrmTenderNotice::getFileObtainStartTime, req.getFileObtainStartTime())
                .set(SrmTenderNotice::getFileObtainEndTime, req.getFileObtainEndTime())
                .set(SrmTenderNotice::getBidDocPayStartTime, req.getBidDocPayStartTime())
                .set(SrmTenderNotice::getBidDocPayEndTime, req.getBidDocPayEndTime())
                .set(SrmTenderNotice::getQuoteStartTime, req.getQuoteStartTime())
                .set(SrmTenderNotice::getQuoteEndTime, req.getQuoteEndTime())
                .set(SrmTenderNotice::getDocStatus, isChange ? ApproveStatusEnum.APPROVE : ApproveStatusEnum.APPROVING)
                .eq(SrmTenderNotice::getId, noticeId)
                .update();

        // 处理标段的附件信息
        if (CollectionUtils.isNotEmpty(bidsSegments)) {
            bidsSegments.forEach(bidsSegment -> {
                // 先删除标书文件信息
                Long sectionId = bidsSegment.getSectionId();
                requirementService.lambdaUpdate().eq(SrmTenderRequirement::getSectionId, sectionId)
                        .eq(SrmTenderRequirement::getNoticeId, noticeId)
                        .eq(SrmTenderRequirement::getRequirementType, BidsSegmentTypeEnum.BID_DOCUMENT)
                        .remove();
                // 标书文件信息设置
                SrmTenderRequirement srmTenderRequirement = new SrmTenderRequirement();
                BeanUtils.copyProperties(bidsSegment, srmTenderRequirement);
                srmTenderRequirement.setRequirementType(BidsSegmentTypeEnum.BID_DOCUMENT);
                srmTenderRequirement.setNoticeId(noticeId);
                // 标书费信息设置
                BidFeeInfoReq bidFeeInfo = bidsSegment.getBidFeeInfo();
                srmTenderRequirement.setRequirementContent(bidFeeInfo != null ? JSONObject.toJSONString(bidFeeInfo) : "{}");
                requirementService.save(srmTenderRequirement);
                // 附件信息设置
                if (bidsSegment.getAttachmentInfos() != null) {
                    // 先删除信息
                    attachmentService.lambdaUpdate().eq(SrmProjectAttachment::getBusinessId, noticeId)
                            .eq(SrmProjectAttachment::getBusinessGroup, sectionId)
                            .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.BID_DOC_ATTACHMENT)
                            .remove();
                    List<SrmProjectAttachment> attachmentList = bidsSegment.getAttachmentInfos().stream().map(item -> {
                        SrmProjectAttachment attachment = new SrmProjectAttachment();
                        BeanUtils.copyProperties(item, attachment);
                        attachment.setBusinessId(noticeId);
                        attachment.setBusinessGroup(String.valueOf(sectionId));
                        attachment.setBusinessType(AttachmentTypeEnum.BID_DOC_ATTACHMENT);
                        return attachment;
                    }).toList();
                    // 这里需要调用附件保存服务将附件信息保存到数据库
                    attachmentService.saveBatch(attachmentList);
                }
            });
        }
        // 评分细则入库逻辑
        List<SrmNegotiationAddReq.EvaluationStandardInfo> evaluationStandardInfos = req.getEvaluationStandardInfos();
        if (CollectionUtils.isNotEmpty(evaluationStandardInfos)) {
            // 先删除旧数据
            standardService.lambdaUpdate().eq(SrmTenderEvaluationStandard::getProjectId, projectId)
                    .eq(SrmTenderEvaluationStandard::getNoticeId, noticeId)
                    .remove();
            List<SrmTenderEvaluationStandard> standardList = evaluationStandardInfos.stream().map(evaluationStandardInfo -> {
                SrmTenderEvaluationStandard standard = new SrmTenderEvaluationStandard();
                BeanUtils.copyProperties(evaluationStandardInfo, standard);
                standard.setType(evaluationStandardInfo.getType());
                standard.setProjectId(projectId);
                standard.setNoticeId(noticeId);
                return standard;
            }).toList();
            standardService.saveBatch(standardList);
        }
    }

    @Override
    public SrmNegotiationAddReq negotiationDocDetail(Long noticeId) {
        // 获取公告信息
        SrmTenderNotice notice = noticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(notice, "招标公告不存在");

        // 设置基本信息
        SrmNegotiationAddReq resp = new SrmNegotiationAddReq();
        resp.setProjectId(notice.getProjectId());
        resp.setNoticeId(noticeId);
        resp.setFileSubmissionAddress(notice.getFileSubmissionAddress());
        resp.setBidFeeCollection(notice.getBidFeeCollection());
        resp.setBiddingNotice(notice.getBiddingNotice());
        resp.setEvaluationMethod(notice.getEvaluationMethod());
        resp.setFileObtainStartTime(notice.getFileObtainStartTime());
        resp.setFileObtainEndTime(notice.getFileObtainEndTime());
        resp.setBidDocPayStartTime(notice.getBidDocPayStartTime());
        resp.setBidDocPayEndTime(notice.getBidDocPayEndTime());
        resp.setQuoteStartTime(notice.getQuoteStartTime());
        resp.setQuoteEndTime(notice.getQuoteEndTime());
        resp.setDocStatus(notice.getDocStatus());
        resp.setTenderWay(notice.getTenderWay());
        
        // 获取标段文件信息
        List<SrmTenderRequirement> requirements = requirementService.lambdaQuery()
                .eq(SrmTenderRequirement::getNoticeId, noticeId)
                .eq(SrmTenderRequirement::getRequirementType, BidsSegmentTypeEnum.BID_DOCUMENT)
                .list();
        
        if (CollectionUtils.isNotEmpty(requirements)) {
            List<SrmNegotiationAddReq.BidsSegment> bidsSegments = requirements.stream().map(req -> {
                SrmNegotiationAddReq.BidsSegment segment = new SrmNegotiationAddReq.BidsSegment();
                BeanUtils.copyProperties(req, segment);
                // 处理标书费信息
                if (StringUtils.isNotBlank(req.getRequirementContent())) {
                    segment.setBidFeeInfo(JSONObject.parseObject(req.getRequirementContent(), BidFeeInfoReq.class));
                }
                // 处理附件信息
                List<SrmProjectAttachment> attachments = attachmentService.lambdaQuery()
                        .eq(SrmProjectAttachment::getBusinessId, noticeId)
                        .eq(SrmProjectAttachment::getBusinessGroup, req.getSectionId().toString())
                        .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.BID_DOC_ATTACHMENT)
                        .list();
                if (CollectionUtils.isNotEmpty(attachments)) {
                    segment.setAttachmentInfos(attachments.stream().map(att -> {
                        AttachmentInfoReq info = new AttachmentInfoReq();
                        BeanUtils.copyProperties(att, info);
                        return info;
                    }).toList());
                }
                return segment;
            }).toList();
            resp.setBidsSegments(bidsSegments);
        }
        
        // 获取评标标准信息
        List<SrmTenderEvaluationStandard> standards = standardService.lambdaQuery()
                .eq(SrmTenderEvaluationStandard::getNoticeId, noticeId)
                .list();
        
        if (CollectionUtils.isNotEmpty(standards)) {
            List<SrmNegotiationAddReq.EvaluationStandardInfo> standardInfos = standards.stream().map(std -> {
                SrmNegotiationAddReq.EvaluationStandardInfo info = new SrmNegotiationAddReq.EvaluationStandardInfo();
                BeanUtils.copyProperties(std, info);
                return info;
            }).toList();
            resp.setEvaluationStandardInfos(standardInfos);
        }
        return resp;
    }

    @Override
    public SrmNegotiationAddReq negotiationDocDetailForApprove(String projectIdAndNoticeId) {
        String[] split = projectIdAndNoticeId.split("#");
        Long projectId = Long.parseLong(split[0]);
        Long noticeId = Long.parseLong(split[1]);
        return negotiationDocDetail(noticeId);
    }

    @Override
    public void negotiationDocApprovePass(String projectIdAndNoticeId) {
        String[] split = projectIdAndNoticeId.split("#");
        Long projectId = Long.parseLong(split[0]);
        Long noticeId = Long.parseLong(split[1]);
        noticeService.lambdaUpdate().set(SrmTenderNotice::getDocStatus, ApproveStatusEnum.APPROVE)
                .eq(SrmTenderNotice::getProjectId, projectId)
                .eq(SrmTenderNotice::getId, noticeId).update();
        // 修改项目状态
        SrmProcurementProject project = projectService.getById(projectId);
        projectService.updateProgressStatus(project.getProjectCode(), ProjectProgressStatusEnum.TENDER_DOC, true);
        // 修改流程状态
        srmProcessInstanceService.handlerInstanceStatus(projectId,SrmProcessConfigService.BizTypeEnum.SRM_TENDER_DOC_AUDIT,ApproveStatusEnum.APPROVE);
    }

    @Override
    public void negotiationDocApproveReject(String projectIdAndNoticeId) {
        String[] split = projectIdAndNoticeId.split("#");
        Long projectId = Long.parseLong(split[0]);
        Long noticeId = Long.parseLong(split[1]);
        noticeService.lambdaUpdate().set(SrmTenderNotice::getDocStatus, ApproveStatusEnum.APPROVE_REJECT)
                .eq(SrmTenderNotice::getProjectId, projectId)
                .eq(SrmTenderNotice::getId, noticeId).update();
        // 修改流程状态
        srmProcessInstanceService.handlerInstanceStatus(projectId,SrmProcessConfigService.BizTypeEnum.SRM_TENDER_DOC_AUDIT,ApproveStatusEnum.APPROVE_REJECT);
    }

    @Override
    public boolean match(SrmProcessConfigService.BizTypeEnum bizType) {
        // 匹配流程类型  标书/竞谈文件信息审核
        return bizType == SrmProcessConfigService.BizTypeEnum.SRM_TENDER_DOC_AUDIT;
    }

    @Override
    public void approveRejectHook(String bizKey) {
        noticeService.lambdaUpdate().set(SrmTenderNotice::getDocStatus, ApproveStatusEnum.APPROVE_REVOKE)
                .eq(SrmTenderNotice::getId, bizKey).update();
    }

    @Override
    public void docApprovingHook(String id) {
        noticeService.lambdaUpdate().set(SrmTenderNotice::getDocStatus, ApproveStatusEnum.APPROVING)
                .eq(SrmTenderNotice::getId, id).update();
    }
}




