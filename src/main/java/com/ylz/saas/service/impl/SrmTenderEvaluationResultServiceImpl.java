package com.ylz.saas.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmProcurementProjectSection;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmProjectMember;
import com.ylz.saas.entity.SrmTenderBidderQuoteItem;
import com.ylz.saas.entity.SrmTenderEvaluationResult;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.entity.SrmTenderSupplierResponse;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.enums.ProjectMemberRoleEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.enums.PublicityStatusEnum;
import com.ylz.saas.enums.YesNoEnum;
import com.ylz.saas.mapper.SrmTenderEvaluationResultMapper;
import com.ylz.saas.req.AttachmentInfoReq;
import com.ylz.saas.req.AuditPublicityNoticeReq;
import com.ylz.saas.req.InviteSupplierReq;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.req.SrmTenderBidPublicNoticeReq;
import com.ylz.saas.req.SrmTenderBidPublicityReq;
import com.ylz.saas.req.SrmTenderBidReq;
import com.ylz.saas.req.SrmTenderEditBatchAwardNoticeReq;
import com.ylz.saas.resp.BidSectionInfoResp;
import com.ylz.saas.resp.SrmTenderEvaluationResultDetailResp;
import com.ylz.saas.service.ApproveRejectHookService;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.service.SrmProcessInstanceService;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmProjectAttachmentService;
import com.ylz.saas.service.SrmTenderAwardNoticeService;
import com.ylz.saas.service.SrmTenderBidderQuoteItemService;
import com.ylz.saas.service.SrmTenderEvaluationResultService;
import com.ylz.saas.service.SrmTenderNoticeService;
import com.ylz.saas.service.SrmTenderSupplierResponseService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【srm_tender_evaluation_result(评标结果表)】的数据库操作Service实现
 * @createDate 2025-06-16 15:54:36
 */
@Slf4j
@Service
public class SrmTenderEvaluationResultServiceImpl extends ServiceImpl<SrmTenderEvaluationResultMapper, SrmTenderEvaluationResult>
        implements SrmTenderEvaluationResultService, ApproveRejectHookService {

    private final ThreadLocal<SrmProcessConfigService.BizTypeEnum> RESULT_TYPE_THREAD_LOCAL = new ThreadLocal<>();

    private static final List<SrmProcessConfigService.BizTypeEnum> RESULT_TYPE_ENUMS = List.of(
            SrmProcessConfigService.BizTypeEnum.SRM_TENDER_BID_AUDIT,
            SrmProcessConfigService.BizTypeEnum.SRM_PUBLICITY_AUDITING,
            SrmProcessConfigService.BizTypeEnum.SRM_BID_NOTICE_AUDITING
    );
    @Resource
    private SrmTenderNoticeService tenderNoticeService;

    @Resource
    private SrmProjectAttachmentService projectAttachmentService;

    @Resource
    private SrmProcurementProjectService projectService;

    @Resource
    private SrmTenderBidderQuoteItemService quoteItemService;

    @Resource
    private SrmProcessInstanceService srmProcessInstanceService;

    @Resource
    private SrmTenderAwardNoticeService awardNoticeService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private SrmTenderSupplierResponseService srmTenderSupplierResponseService;

    public void clearThreadLocal() {
        RESULT_TYPE_THREAD_LOCAL.remove();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void reviewBid(SrmTenderBidReq req) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        SrmTenderNotice tenderNotice = tenderNoticeService.lambdaQuery().eq(SrmTenderNotice::getId, noticeId)
                .eq(SrmTenderNotice::getProjectId, projectId).one();
        ExceptionUtil.checkNonNull(tenderNotice, "未找到对应的招标公告");
        // 项目是否存在校验
        SrmProcurementProject project = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(project, "项目不存在");

        List<SrmTenderEvaluationResult> existResultList = lambdaQuery().eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId).list();
        Map<String, SrmTenderEvaluationResult> keyMapVo = existResultList.stream().collect(Collectors.toMap(
                info -> ""+ info.getSectionId() + info.getTenantSupplierId()
                , Function.identity(), (x1, x2) -> x1));

        List<SrmTenderBidReq.BidProjectItem> projectItemList = req.getProjectItemList();

        // 是否竞谈的定标，校验：只能有一个供应商中标
        Boolean isCompetitive = req.getIsCompetitive();
        if(isCompetitive != null && isCompetitive){
            handlerCompetitive(projectItemList, noticeId, projectId);
        }
        Map<String, List<SrmTenderBidReq.BidProjectItem>> webKeyMapList = projectItemList.stream().collect(Collectors.groupingBy(
                item -> "" + item.getSectionId() + item.getTenantSupplierId()));
        List<SrmTenderEvaluationResult> evaluationResultList = new ArrayList<>();
        webKeyMapList.forEach((key, values) -> {
            SrmTenderBidReq.BidProjectItem item = values.get(0);
            SrmTenderEvaluationResult evaluationResult = new SrmTenderEvaluationResult();
            BeanUtils.copyProperties(item, evaluationResult);
            SrmTenderEvaluationResult existResult = keyMapVo.get("" +item.getSectionId() + item.getTenantSupplierId());
            if(existResult != null){
                evaluationResult.setId(existResult.getId());
            }
            evaluationResult.setProjectId(projectId);
            evaluationResult.setNoticeId(noticeId);
            evaluationResult.setSectionId(item.getSectionId());
            // 评标备注
            evaluationResult.setAwardRemark(req.getAwardRemark());
            // 已提交
            evaluationResult.setAwardReportStatus(ApproveStatusEnum.TO_APPROVE);
            // 定标报告
            evaluationResult.setAwardTemplateId(req.getAwardTemplateId());
            evaluationResult.setAwardReportContent(req.getAwardReportContent());
            evaluationResult.setAwardReportTime(LocalDateTime.now());
            // 未公告
            evaluationResult.setNoticeStatus(PublicNoticeStatusEnum.UNPUBLISHED);
            // 未公示
            evaluationResult.setPublicityStatus(PublicityStatusEnum.UNPUBLISHED);
            // 中标总金额
            BigDecimal awardedAmountTotal = values.stream().map(SrmTenderBidReq.BidProjectItem::getBidAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            evaluationResult.setAwardedAmountTotal(awardedAmountTotal);
            saveOrUpdate(evaluationResult);
            evaluationResultList.add(evaluationResult);
            // 设置中标供应商，设置价格，设置数量，设置resultId
            String materialCode = item.getMaterialCode();
            if(StringUtils.isNotBlank(materialCode)){
                quoteItemService.lambdaUpdate().set(SrmTenderBidderQuoteItem::getAwarded, true)
                        .set(SrmTenderBidderQuoteItem::getAwardedQuantity, item.getAwardedQuantity())
                        .set(SrmTenderBidderQuoteItem::getResultId, evaluationResult.getId())
                        .eq(SrmTenderBidderQuoteItem::getNoticeId, noticeId)
                        .eq(SrmTenderBidderQuoteItem::getSectionId, item.getSectionId())
                        .eq(SrmTenderBidderQuoteItem::getTenantSupplierId, item.getTenantSupplierId())
                        .eq(SrmTenderBidderQuoteItem::getMaterialCode, materialCode)
                        .update();
            } else {
                quoteItemService.lambdaUpdate().set(SrmTenderBidderQuoteItem::getAwarded, true)
                        .setSql(" awarded_quantity = required_quantity")
                        .set(SrmTenderBidderQuoteItem::getResultId, evaluationResult.getId())
                        .eq(SrmTenderBidderQuoteItem::getNoticeId, noticeId)
                        .eq(SrmTenderBidderQuoteItem::getSectionId, item.getSectionId())
                        .eq(SrmTenderBidderQuoteItem::getTenantSupplierId, item.getTenantSupplierId())
                        .update();
            }

        });
        // 附件保存
        List<AttachmentInfoReq> attachmentInfos = req.getAttachmentInfos();
        if (CollectionUtils.isNotEmpty(attachmentInfos)) {
            evaluationResultList.forEach(evaluationResult -> {
                // 先删除旧附件
                projectAttachmentService.lambdaUpdate().eq(SrmProjectAttachment::getBusinessId, evaluationResult.getId())
                        .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.BID_ATTACHMENT)
                        .remove();
                List<SrmProjectAttachment> attachments = attachmentInfos.stream()
                        .map(info -> {
                            SrmProjectAttachment attachment = new SrmProjectAttachment();
                            BeanUtils.copyProperties(info, attachment);
                            attachment.setBusinessType(AttachmentTypeEnum.BID_ATTACHMENT); // 根据业务类型设置
                            attachment.setBusinessId(evaluationResult.getId()); // 评标结果ID作为业务ID
                            return attachment;
                        })
                        .collect(Collectors.toList());
                projectAttachmentService.saveBatch(attachments);
            });
        }
        // 业务流程发起
        ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
        Long sectionId = projectItemList.get(0).getSectionId();
        String projectAndNoticeId = projectId + "#" + noticeId + "#" + sectionId;
        String tenantSupplierIdsStr = req.getProjectItemList().stream().map(SrmTenderBidReq.BidProjectItem::getTenantSupplierId)
                .distinct().map(String::valueOf).collect(Collectors.joining(","));
//        Long bizId = Long.valueOf(projectId  + "" + noticeId + sectionId);
        startReq.setBizKey(projectAndNoticeId);
        startReq.setBizId(projectId);
        String projectCode = project.getProjectCode();
        if (BaseServiceTypeFieldService.BuyWayEnum.QZXJ == project.getSourcingType()) {
            projectCode = projectCode + "&isSilage=true";
        } else {
            projectCode = projectCode + "&isSilage=false";
        }
        startReq.setArgs(List.of(projectCode,projectId,sectionId,projectAndNoticeId,tenantSupplierIdsStr));
        startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_TENDER_BID_AUDIT);
        srmProcessInstanceService.startProcessInstance(startReq);
        log.info("定标业务流程发起完成！");
        // 中标通知书保存
        List<SrmTenderEditBatchAwardNoticeReq> awardNoticeReqList = req.getAwardNoticeReqList();
        Map<Long, SrmTenderEvaluationResult> sectionIdAndSupplierIdMapVo = evaluationResultList.stream().collect(Collectors.toMap(
                info -> info.getSectionId() + info.getTenantSupplierId(), Function.identity(), (x1, x2) -> x1));
        if(CollectionUtils.isNotEmpty(awardNoticeReqList)){
            for (SrmTenderEditBatchAwardNoticeReq awardNoticeReq : awardNoticeReqList) {
                SrmTenderEvaluationResult evaluationResult = sectionIdAndSupplierIdMapVo.get(awardNoticeReq.getSectionId() + awardNoticeReq.getTenantSupplierId());
                awardNoticeReq.setEvaluationResultId(evaluationResult.getId());
            }
            awardNoticeService.batchAwardNotice(awardNoticeReqList);
        }
    }

    private void handlerCompetitive(List<SrmTenderBidReq.BidProjectItem> projectItemList, Long noticeId, Long projectId) {
        List<SrmTenderBidReq.BidProjectItem> winningSuppliers = projectItemList.stream()
                .filter(supplier -> Boolean.TRUE.equals(supplier.getIsBid()))
                .toList();

        if (winningSuppliers.size() > 1) {
            throw new IllegalArgumentException("定标审核只能有一个供应商中标，当前有 " + winningSuppliers.size() + " 个供应商中标");
        }

        if (winningSuppliers.isEmpty()) {
            throw new IllegalArgumentException("必须至少有一个供应商中标");
        }

        // 更新所有供应商的中标状态
        projectItemList.forEach(bidSupplier -> {
            srmTenderSupplierResponseService.lambdaUpdate()
                    .set(SrmTenderSupplierResponse::getIsWin, bidSupplier.getIsBid() ? 1 : 0)
                    .eq(SrmTenderSupplierResponse::getNoticeId, noticeId)
                    .eq(SrmTenderSupplierResponse::getProjectId, projectId)
                    .eq(SrmTenderSupplierResponse::getSectionId, bidSupplier.getSectionId())
                    .eq(SrmTenderSupplierResponse::getTenantSupplierId, bidSupplier.getTenantSupplierId())
                    .update();
        });
    }


    @Override
    public void evaluationResultExamineApproving(String projectId, String noticeId, String sectionId, String tenantSupplierIdsStr) {
        List<Long> tenantSupplierIds = Arrays.stream(tenantSupplierIdsStr.split(",")).map(Long::valueOf).toList();
        this.lambdaUpdate().eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getSectionId, sectionId)
                .in(SrmTenderEvaluationResult::getTenantSupplierId, tenantSupplierIds)
                .set(SrmTenderEvaluationResult::getAwardReportStatus, ApproveStatusEnum.APPROVING)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void evaluationResultExaminePass(String projectId, String noticeId, String sectionId) {
        handlerResultStatus(projectId, noticeId, sectionId, ApproveStatusEnum.APPROVE);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void evaluationResultExamineReject(String projectId, String noticeId, String sectionId) {
        handlerResultStatus(projectId, noticeId, sectionId, ApproveStatusEnum.APPROVE_REJECT);
    }

    private void handlerResultStatus(String projectId, String noticeId, String sectionId, ApproveStatusEnum awardReportStatus) {
        List<SrmTenderEvaluationResult> evaluationResultList = lambdaQuery().eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getSectionId, sectionId)
                .list();
        ExceptionUtil.checkNotEmpty(evaluationResultList, "未找到对应的评标结果");
        // 设置审批状态
        lambdaUpdate().set(SrmTenderEvaluationResult::getAwardReportStatus, awardReportStatus)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getSectionId, sectionId)
                .update();
        // 查询该公告下所有标段的定标状态
        SrmProcurementProject procurementProject = projectService.getById(projectId);
        SrmProcurementProject srmProcurementProject = projectService.detail(procurementProject.getProjectCode(), false);
        List<SrmProcurementProjectSection> projectSectionList = srmProcurementProject.getProjectSectionList();
        List<SrmTenderEvaluationResult> allSectionResults = lambdaQuery()
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .list();
        Map<Long, SrmTenderEvaluationResult> sectionIdMapVo = allSectionResults.stream().collect(
                Collectors.toMap(SrmTenderEvaluationResult::getSectionId, Function.identity()
                , (x1, x2) -> x1));
        if(CollectionUtils.isNotEmpty(projectSectionList)){
            projectSectionList.forEach(item -> item.setAwardReportStatus(sectionIdMapVo.get(item.getId()) != null
                            ? sectionIdMapVo.get(item.getId()).getAwardReportStatus() : ApproveStatusEnum.APPROVING));
            // 多标段
            if(awardReportStatus == ApproveStatusEnum.APPROVE){
                // 检查是否所有标段都已定标完成
                boolean allSectionsApproved = projectSectionList.stream()
                        .allMatch(result -> result.getAwardReportStatus() == ApproveStatusEnum.APPROVE);

                if (allSectionsApproved) {
                    // 修改项目状态
                    log.info("修改项目状态开始");
                    projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.AWARDED, true);
                }
            } else {
                // 删除报价明细表中标信息
                quoteItemService.lambdaUpdate().set(SrmTenderBidderQuoteItem::getAwarded, false)
                        .set(SrmTenderBidderQuoteItem::getProcurementProjectPaymentId, null)
                        .set(SrmTenderBidderQuoteItem::getAwardedQuantity, null)
                        .eq(SrmTenderBidderQuoteItem::getProjectId, projectId)
                        .eq(SrmTenderBidderQuoteItem::getNoticeId, noticeId)
                        .eq(SrmTenderBidderQuoteItem::getSectionId, sectionId)
                        .update();
            }
        } else {
            // 单标段
            if(awardReportStatus == ApproveStatusEnum.APPROVE){
                // 通过
                log.info("修改项目状态开始");
                projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.AWARDED, true);
            } else {
                // 删除报价明细表中标信息
                quoteItemService.lambdaUpdate().set(SrmTenderBidderQuoteItem::getAwarded, false)
                        .set(SrmTenderBidderQuoteItem::getProcurementProjectPaymentId, null)
                        .set(SrmTenderBidderQuoteItem::getAwardedQuantity, null)
                        .eq(SrmTenderBidderQuoteItem::getProjectId, projectId)
                        .eq(SrmTenderBidderQuoteItem::getNoticeId, noticeId)
                        .eq(SrmTenderBidderQuoteItem::getSectionId, sectionId)
                        .update();
            }
        }
        // 修改流程状态（产品确认，取最新的一次审批作为项目上审批状态）
        srmProcessInstanceService.handlerInstanceStatus(Long.parseLong(projectId),SrmProcessConfigService.BizTypeEnum.SRM_TENDER_BID_AUDIT,awardReportStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bidPublicity(SrmTenderBidPublicityReq req) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        List<InviteSupplierReq> inviteSupplierList = req.getInviteSupplierList();
        // 项目是否存在
        SrmProcurementProject project = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(project, "项目不存在");
        List<Long> sectionIdList = inviteSupplierList.stream().map(InviteSupplierReq::getSectionId).toList();
        List<SrmTenderEvaluationResult> evaluationResultList = lambdaQuery().eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .in(CollectionUtils.isNotEmpty(sectionIdList), SrmTenderEvaluationResult::getSectionId, sectionIdList)
                .list();
        ExceptionUtil.checkNotEmpty(evaluationResultList, "未找到对应的评标结果");
        // 必须全部定标通过后才能进行公示操作
        evaluationResultList.stream().filter(evaluationResult -> evaluationResult.getAwardReportStatus() != ApproveStatusEnum.APPROVE)
                .forEachOrdered(evaluationResult -> ExceptionUtil.checkNonNull(null, "请先通过定标！"));
        LocalDateTime publicityStartTime = req.getPublicityStartTime();
        LocalDateTime publicityEndTime = req.getPublicityEndTime();
        // 设置公示信息
        Boolean isPublicity = req.getIsPublicity();
        if(isPublicity){
            if (publicityStartTime == null || publicityEndTime == null) {
                ExceptionUtil.checkNonNull(null, "公示起止时间不能为空！");
            }
            if (publicityStartTime.isAfter(publicityEndTime)) {
                ExceptionUtil.checkNonNull(null, "公示开始时间不能大于结束时间！");
            }
        }


        evaluationResultList.forEach(evaluationResult -> {
            lambdaUpdate()
//                    .set(SrmTenderEvaluationResult::getPublicityStatus, req.getIsPublicity() ? PublicityStatusEnum.PUBLISHED : PublicityStatusEnum.UNPUBLISHED)
                    .set(isPublicity,SrmTenderEvaluationResult::getPublicityTitle, req.getPublicityTitle())
                    .set(isPublicity,SrmTenderEvaluationResult::getPublicityTemplateId, req.getPublicityTemplateId())
                    .set(isPublicity,SrmTenderEvaluationResult::getPublicityContent, req.getPublicityContent())
                    .set(isPublicity,SrmTenderEvaluationResult::getPublicityStartTime, publicityStartTime)
                    .set(isPublicity,SrmTenderEvaluationResult::getPublicityEndTime, publicityEndTime)
                    .set(SrmTenderEvaluationResult::getPublicityAuditStatus, isPublicity ? ApproveStatusEnum.APPROVING : ApproveStatusEnum.APPROVE)
                    .eq(SrmTenderEvaluationResult::getSectionId, evaluationResult.getSectionId())
                    .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                    .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                    .update();
        });

        // 附件信息
        List<AttachmentInfoReq> attachmentInfos = req.getAttachmentInfos();
        if (CollectionUtils.isNotEmpty(attachmentInfos)) {
            evaluationResultList.forEach(evaluationResult -> {
                // 删除附件信息
                projectAttachmentService.lambdaUpdate().eq(SrmProjectAttachment::getBusinessId, evaluationResult.getId())
                        .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.PUBLICITY_ATTACHMENT)
                        .remove();
                List<SrmProjectAttachment> attachments = attachmentInfos.stream().map(info -> {
                    SrmProjectAttachment attachment = new SrmProjectAttachment();
                    BeanUtils.copyProperties(info, attachment);
                    attachment.setBusinessType(AttachmentTypeEnum.PUBLICITY_ATTACHMENT);
                    attachment.setBusinessId(evaluationResult.getId());
                    return attachment;
                }).toList();
                projectAttachmentService.saveBatch(attachments);
            });
        }
        // 是否发起流程
        if(isPublicity){
            // 业务流程发起
            ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
            String projectAndNoticeId = projectId + "#" + noticeId;
//        Long bizId = Long.valueOf(projectId+""+noticeId);
            startReq.setBizKey(projectAndNoticeId);
            startReq.setBizId(projectId);
            startReq.setArgs(List.of(project.getProjectCode(),projectId,projectAndNoticeId));
            startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_PUBLICITY_AUDITING);
            srmProcessInstanceService.startProcessInstance(startReq);
            log.info("中标公示业务流程发起完成！");
        } else {
            // 修改项目状态，修改流程状态
            log.info("中标公示，修改项目状态开始");
            projectService.updateProgressStatus(project.getProjectCode(), ProjectProgressStatusEnum.BID_WON_PUBLICITY, true);
            srmProcessInstanceService.handlerInstanceStatus(projectId, SrmProcessConfigService.BizTypeEnum.SRM_PUBLICITY_AUDITING, ApproveStatusEnum.APPROVE);
        }

    }

    @Override
    public void bidPublicNotice(SrmTenderBidPublicNoticeReq req) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        List<InviteSupplierReq> inviteSupplierList = req.getInviteSupplierList();
        // 项目是否存在
        SrmProcurementProject project = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(project, "项目不存在");
        List<Long> sectionIdList = inviteSupplierList.stream().map(InviteSupplierReq::getSectionId).toList();
        List<SrmTenderEvaluationResult> evaluationResultList = lambdaQuery().eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .in(CollectionUtils.isNotEmpty(sectionIdList), SrmTenderEvaluationResult::getSectionId, sectionIdList)
                .list();
        ExceptionUtil.checkNotEmpty(evaluationResultList, "未找到对应的评标结果");
        // 必须全部定标通过后才能进行公示操作
        evaluationResultList.stream().filter(evaluationResult -> evaluationResult.getAwardReportStatus() != ApproveStatusEnum.APPROVE)
                .forEachOrdered(evaluationResult -> ExceptionUtil.checkNonNull(null, "请先通过定标！"));
        // 必须通过中标公示后，才能进行公告操作
        evaluationResultList.stream().filter(evaluationResult -> evaluationResult.getPublicityAuditStatus() != ApproveStatusEnum.APPROVE)
                .forEachOrdered(evaluationResult -> ExceptionUtil.checkNonNull(null, "请先通过中标公示审批！"));
        // 设置公示信息
        Boolean isPublishNotice = req.getIsPublishNotice();
        evaluationResultList.forEach(evaluationResult -> {
            lambdaUpdate()
//                    审核后修改
//                    .set(SrmTenderEvaluationResult::getNoticeStatus, req.getIsPublishNotice() ? PublicNoticeStatusEnum.PUBLISHED : PublicNoticeStatusEnum.UNPUBLISHED)
                    .set(isPublishNotice,SrmTenderEvaluationResult::getNoticeTitle, req.getNoticeTitle())
                    .set(isPublishNotice,SrmTenderEvaluationResult::getNoticeTemplateId, req.getNoticeTemplateId())
                    .set(isPublishNotice,SrmTenderEvaluationResult::getNoticeContent, req.getNoticeContent())
                    .set(SrmTenderEvaluationResult::getNoticeAuditStatus, isPublishNotice ? ApproveStatusEnum.APPROVING : ApproveStatusEnum.APPROVE)
                    .eq(SrmTenderEvaluationResult::getSectionId, evaluationResult.getSectionId())
                    .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                    .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                    .update();
        });

        // 附件信息
        List<AttachmentInfoReq> attachmentInfos = req.getAttachmentInfos();
        if (CollectionUtils.isNotEmpty(attachmentInfos)) {
            evaluationResultList.forEach(evaluationResult -> {
                // 删除附件信息
                projectAttachmentService.lambdaUpdate().eq(SrmProjectAttachment::getBusinessId, evaluationResult.getId())
                        .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.NOTICE_ATTACHMENT)
                        .remove();
                List<SrmProjectAttachment> attachments = attachmentInfos.stream().map(info -> {
                    SrmProjectAttachment attachment = new SrmProjectAttachment();
                    BeanUtils.copyProperties(info, attachment);
                    attachment.setBusinessType(AttachmentTypeEnum.NOTICE_ATTACHMENT);
                    attachment.setBusinessId(evaluationResult.getId());
                    return attachment;
                }).toList();
                projectAttachmentService.saveBatch(attachments);
            });
        }
        // 是否发起流程
        if(isPublishNotice){
            // 业务流程发起
            ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
            String projectAndNoticeId = projectId + "#" + noticeId;
//        Long bizId = Long.valueOf(projectId+""+noticeId);
            startReq.setBizKey(projectAndNoticeId);
            startReq.setBizId(projectId);
            startReq.setArgs(List.of(project.getProjectCode(),projectId,projectAndNoticeId));
            startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_BID_NOTICE_AUDITING);
            srmProcessInstanceService.startProcessInstance(startReq);
            log.info("中标公告业务流程发起完成！");
        } else {
            // 修改项目状态
            log.info("中标公告，修改项目状态开始");
            projectService.updateProgressStatus(project.getProjectCode(), ProjectProgressStatusEnum.BID_WON_NOTICE, true);
            srmProcessInstanceService.handlerInstanceStatus(projectId, SrmProcessConfigService.BizTypeEnum.SRM_BID_NOTICE_AUDITING, ApproveStatusEnum.APPROVE);
        }

    }

    @Override
    public SrmTenderEvaluationResultDetailResp getEvaluationResultDetail(Long noticeId, Long projectId) {
        ExceptionUtil.checkNonNull(noticeId, "招标公告ID不能为空");
        ExceptionUtil.checkNonNull(projectId, "采购立项ID不能为空");

        // 通过noticeId和projectId查询评标结果
        SrmTenderEvaluationResult evaluationResult = this.lambdaQuery()
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .last(" limit 1")
                .one();
        if (evaluationResult == null) {
            log.info("未找到对应的评标结果信息evaluationResult");
            return new SrmTenderEvaluationResultDetailResp();
        }

        SrmTenderEvaluationResultDetailResp result = baseMapper.getEvaluationResultDetailByNoticeAndProject(noticeId, projectId);
        if (result == null) {
            log.info("未找到对应的评标结果信息result");
            return new SrmTenderEvaluationResultDetailResp();
        }

        // 查询定标公示附件
        List<SrmProjectAttachment> awardAttachments = projectAttachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId, evaluationResult.getId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.BID_ATTACHMENT)
                .eq(SrmProjectAttachment::getDelFlag, YesNoEnum.NO.getCode())
                .orderByDesc(SrmProjectAttachment::getCreateTime)
                .list();
        result.setAwardAttachments(awardAttachments);

        // 查询中标公示附件
        List<SrmProjectAttachment> publicityAttachments = projectAttachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId, evaluationResult.getId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.PUBLICITY_ATTACHMENT)
                .eq(SrmProjectAttachment::getDelFlag, YesNoEnum.NO.getCode())
                .orderByDesc(SrmProjectAttachment::getCreateTime)
                .list();
        result.setPublicityAttachments(publicityAttachments);

        // 查询中标公告附件
        List<SrmProjectAttachment> noticeAttachments = projectAttachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId, evaluationResult.getId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.NOTICE_ATTACHMENT)
                .eq(SrmProjectAttachment::getDelFlag, YesNoEnum.NO.getCode())
                .orderByDesc(SrmProjectAttachment::getCreateTime)
                .list();
        result.setNoticeAttachments(noticeAttachments);
        List<SrmTenderEvaluationResultDetailResp> itemList = baseMapper.getEvaluationResultDetailByNoticeAndProjectList(noticeId, projectId, null);
        Map<Long, SrmTenderEvaluationResultDetailResp> resultIdMapVo = itemList.stream().collect(
                Collectors.toMap(info->info.getProjectId()+info.getNoticeId()+info.getSectionId()+info.getTenantSupplierId(), Function.identity(), (x, y) -> x));
        List<Long> resultIds = itemList.stream().map(SrmTenderEvaluationResultDetailResp::getId).toList();
        List<SrmTenderBidderQuoteItem> bidderQuoteItems = quoteItemService.lambdaQuery().in(SrmTenderBidderQuoteItem::getResultId, resultIds).list();
        // 查询定标附件信息
        List<SrmProjectAttachment> attachmentList = projectAttachmentService.lambdaQuery()
                .in(SrmProjectAttachment::getBusinessId, resultIds)
                .in(SrmProjectAttachment::getBusinessType,
                        AttachmentTypeEnum.BID_ATTACHMENT, AttachmentTypeEnum.PUBLICITY_ATTACHMENT, AttachmentTypeEnum.NOTICE_ATTACHMENT)
                .list();
        Map<String, List<SrmProjectAttachment>> strMapList = attachmentList.stream().collect(Collectors.groupingBy(
                info->info.getBusinessId()+""+info.getBusinessType()));
        // 设置值进去
        result.setProjectItemList(bidderQuoteItems.stream()
            .map(item -> {
                SrmTenderEvaluationResultDetailResp resultDetailResp = resultIdMapVo.get(item.getProjectId()+item.getNoticeId()+item.getSectionId()+item.getTenantSupplierId());
                SrmTenderBidReq.BidProjectItem bidItem = new SrmTenderBidReq.BidProjectItem();
                BeanUtils.copyProperties(resultDetailResp, bidItem);
                bidItem.setProjectPaymentId(item.getProcurementProjectPaymentId());
                bidItem.setMaterialCode(item.getMaterialCode());
                bidItem.setMaterialName(item.getMaterialName());
                bidItem.setSpecModel(item.getSpecModel());
                bidItem.setUnit(item.getUnit());
                bidItem.setRequiredQuantity(item.getRequiredQuantity());
                bidItem.setBidAmount(item.getAwardedQuantity() != null ? item.getAwardedQuantity().multiply(item.getQuotePrice()) : BigDecimal.ZERO);
                bidItem.setAwardedQuantity(item.getAwardedQuantity());
                // 设置定标附件信息
                List<SrmProjectAttachment> awardAttachmentList = strMapList.get(item.getResultId() + "" + AttachmentTypeEnum.BID_ATTACHMENT);
                if(CollectionUtils.isNotEmpty(awardAttachmentList)){
                    bidItem.setAwardAttachments(awardAttachmentList);
                }
                // 设置中标公示附件信息
                List<SrmProjectAttachment> publicityAttachmentList = strMapList.get(item.getResultId() + "" + AttachmentTypeEnum.PUBLICITY_ATTACHMENT);
                if(CollectionUtils.isNotEmpty(publicityAttachmentList)){
                    bidItem.setPublicityAttachments(publicityAttachmentList);
                }
                // 设置中标公告附件信息
                List<SrmProjectAttachment> noticeAttachmentList = strMapList.get(item.getResultId() + "" + AttachmentTypeEnum.NOTICE_ATTACHMENT);
                if(CollectionUtils.isNotEmpty(noticeAttachmentList)){
                    bidItem.setNoticeAttachments(noticeAttachmentList);
                }
                return bidItem;
            })
            .collect(Collectors.toList()));
        return result;
    }
    @Override
    public void auditPublicity(AuditPublicityNoticeReq req) {
        ApproveStatusEnum status;
        PublicityStatusEnum pubStatus ;
        if (req.getResult() == 1) {
            status = ApproveStatusEnum.APPROVE;
            pubStatus = PublicityStatusEnum.PUBLISHED;
            // 修改采购项目进度
            SrmTenderEvaluationResult evaluationResult = getById(req.getId());
            SrmProcurementProject procurementProject = projectService.getById(evaluationResult.getProjectId());
            projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.BID_WON_PUBLICITY, true);
        } else if (req.getResult() == 2) {
            status = ApproveStatusEnum.APPROVE_REJECT;
            pubStatus = PublicityStatusEnum.UNPUBLISHED;

        } else {
            status = ApproveStatusEnum.APPROVE_REVOKE;
            pubStatus = PublicityStatusEnum.UNPUBLISHED;

        }

        this.lambdaUpdate()
                .eq(SrmTenderEvaluationResult::getId, req.getId())
                .set(SrmTenderEvaluationResult::getPublicityAuditStatus, status)
                .set(SrmTenderEvaluationResult::getPublicityStatus, pubStatus)
                .update();

    }

    @Override
    public void auditPublicityPass(String projectId, String noticeId) {
        ApproveStatusEnum status = ApproveStatusEnum.APPROVE;
        PublicityStatusEnum pubStatus = PublicityStatusEnum.PUBLISHED;
        // 修改采购项目进度
        SrmProcurementProject procurementProject = projectService.getById(projectId);
        projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.BID_WON_PUBLICITY, true);

        this.lambdaUpdate()
                .set(SrmTenderEvaluationResult::getPublicityAuditStatus, status)
                .set(SrmTenderEvaluationResult::getPublicityStatus, pubStatus)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .update();
        srmProcessInstanceService.handlerInstanceStatus(Long.parseLong(projectId),SrmProcessConfigService.BizTypeEnum.SRM_PUBLICITY_AUDITING,status);

    }

    @Override
    public void auditPublicityReject(String projectId, String noticeId) {
        ApproveStatusEnum status = ApproveStatusEnum.APPROVE_REJECT;
        PublicityStatusEnum pubStatus = PublicityStatusEnum.UNPUBLISHED;
        this.lambdaUpdate()
                .set(SrmTenderEvaluationResult::getPublicityAuditStatus, status)
                .set(SrmTenderEvaluationResult::getPublicityStatus, pubStatus)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .update();
        srmProcessInstanceService.handlerInstanceStatus(Long.parseLong(projectId),SrmProcessConfigService.BizTypeEnum.SRM_PUBLICITY_AUDITING,status);

    }

    @Override
    public void auditNotice(AuditPublicityNoticeReq req) {
        ApproveStatusEnum status;
        PublicNoticeStatusEnum pubStatus ;
        if (req.getResult() == 1) {
            status = ApproveStatusEnum.APPROVE;
            pubStatus = PublicNoticeStatusEnum.PUBLISHED;
            // 修改采购项目进度
            SrmTenderEvaluationResult evaluationResult = getById(req.getId());
            SrmProcurementProject procurementProject = projectService.getById(evaluationResult.getProjectId());
            projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.BID_WON_NOTICE, true);
        } else if (req.getResult() == 2) {
            status = ApproveStatusEnum.APPROVE_REJECT;
            pubStatus = PublicNoticeStatusEnum.UNPUBLISHED;

        } else {
            status = ApproveStatusEnum.APPROVE_REVOKE;
            pubStatus = PublicNoticeStatusEnum.UNPUBLISHED;

        }


        this.lambdaUpdate()
                .eq(SrmTenderEvaluationResult::getId, req.getId())
                .set(SrmTenderEvaluationResult::getNoticeAuditStatus, status)
                .set(SrmTenderEvaluationResult::getNoticeStatus,pubStatus)
                .update();
    }

    @Override
    public void auditNoticePass(String projectId, String noticeId) {
        ApproveStatusEnum status = ApproveStatusEnum.APPROVE;
        PublicityStatusEnum pubStatus = PublicityStatusEnum.PUBLISHED;
        // 修改采购项目进度
        SrmProcurementProject procurementProject = projectService.getById(projectId);
        projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.BID_WON_NOTICE, true);

        this.lambdaUpdate()
                .set(SrmTenderEvaluationResult::getNoticeAuditStatus, status)
                .set(SrmTenderEvaluationResult::getNoticeStatus, pubStatus)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .update();
        srmProcessInstanceService.handlerInstanceStatus(Long.parseLong(projectId),SrmProcessConfigService.BizTypeEnum.SRM_BID_NOTICE_AUDITING,status);
    }

    @Override
    public void auditNoticeReject(String projectId, String noticeId) {
        ApproveStatusEnum status = ApproveStatusEnum.APPROVE_REJECT;
        PublicityStatusEnum pubStatus = PublicityStatusEnum.UNPUBLISHED;
        this.lambdaUpdate()
                .set(SrmTenderEvaluationResult::getNoticeAuditStatus, status)
                .set(SrmTenderEvaluationResult::getNoticeStatus, pubStatus)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .update();
        srmProcessInstanceService.handlerInstanceStatus(Long.parseLong(projectId),SrmProcessConfigService.BizTypeEnum.SRM_BID_NOTICE_AUDITING,status);

    }

    @Override
    public List<SrmTenderEvaluationResult> getDetailById(String projectIdAndNoticeId) {
        String[] split = projectIdAndNoticeId.split("#");
        Long projectId = Long.valueOf(split[0]);
        Long noticeId = Long.valueOf(split[1]);


        // 校验发标公告信息是否存在
        SrmTenderNotice notice = tenderNoticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(notice, "发标公告不存在");
        // 校验项目是否存在s
        SrmProcurementProject project = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(project, "项目不存在");
        // 通过projectId和noticeId查询
        Long sectionId = null;
        if(split.length > 2){
            sectionId = Long.valueOf(split[2]);
        }
        List<SrmTenderEvaluationResult> evaluationResultList = lambdaQuery().eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .eq(sectionId != null ,SrmTenderEvaluationResult::getSectionId, sectionId)
                .list();
        ExceptionUtil.checkNotEmpty(evaluationResultList, "评标结果不存在");
        evaluationResultList.forEach(info->info.setTenderNoticeTitle(notice.getNoticeTitle()));
        return evaluationResultList;
    }

    @Override
    public List<SrmTenderEvaluationResult> getDetailById(String projectIdAndNoticeId, String tenantSupplierIdsStr) {
        String[] split = projectIdAndNoticeId.split("#");
        Long projectId = Long.valueOf(split[0]);
        Long noticeId = Long.valueOf(split[1]);

        List<Long> tenantSupplierIds = Arrays.stream(tenantSupplierIdsStr.split(",")).map(Long::valueOf).toList();


        // 校验发标公告信息是否存在
        SrmTenderNotice notice = tenderNoticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(notice, "发标公告不存在");
        // 校验项目是否存在s
        SrmProcurementProject project = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(project, "项目不存在");
        // 通过projectId和noticeId查询
        Long sectionId = null;
        if(split.length > 2){
            sectionId = Long.valueOf(split[2]);
        }
        List<SrmTenderEvaluationResult> evaluationResultList = lambdaQuery().eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .eq(sectionId != null, SrmTenderEvaluationResult::getSectionId, sectionId)
                .in(SrmTenderEvaluationResult::getTenantSupplierId, tenantSupplierIds)
                .list();
        ExceptionUtil.checkNotEmpty(evaluationResultList, "评标结果不存在");
        evaluationResultList.forEach(info->info.setTenderNoticeTitle(notice.getNoticeTitle()));
        return evaluationResultList;
    }

    @Override
    public void updatePublicityAuditStatus(String evaluationResultId) {
        Long id = Long.valueOf(evaluationResultId);
        SrmTenderEvaluationResult evaluationResult = this.getById(id);
        ExceptionUtil.checkNonNull(evaluationResult, "评标结果不存在");

        // 更新公示审批状态为审批中
        this.lambdaUpdate()
                .eq(SrmTenderEvaluationResult::getId, id)
                .set(SrmTenderEvaluationResult::getPublicityAuditStatus, ApproveStatusEnum.APPROVING)
                .update();
    }

    @Override
    public void approvePublicityAudit(String evaluationResultId, Integer result) {
        Long id = Long.valueOf(evaluationResultId);

        // 创建审批请求对象
        AuditPublicityNoticeReq req = new AuditPublicityNoticeReq();
        req.setId(id);
        req.setResult(result);

        // 调用公示审批方法
        this.auditPublicity(req);
    }

    @Override
    public boolean match(SrmProcessConfigService.BizTypeEnum bizType) {
        RESULT_TYPE_THREAD_LOCAL.set(bizType);
        return RESULT_TYPE_ENUMS.contains(bizType);
    }

    @Override
    public void approveRejectHook(String bizKey) {
        try {
            if(!bizKey.contains("#")){
                ExceptionUtil.checkNonNull(null, "参数格式应为：projectId#noticeId#sectionId，例如：123#23#1234");
            }
            String[] split = bizKey.split("#");
            String projectId = split[0];
            String noticeId = split[1];
            SrmProcessConfigService.BizTypeEnum bizTypeEnum = RESULT_TYPE_THREAD_LOCAL.get();
            log.info("approveRejectHook bizTypeEnum：{}，split：{}", bizTypeEnum, JSONObject.toJSONString(split));
            switch(bizTypeEnum) {
                // 定标撤销
                case SRM_TENDER_BID_AUDIT:
                    log.info("approveRejectHook 定标撤销");
                    this.lambdaUpdate().set(SrmTenderEvaluationResult::getAwardReportStatus, ApproveStatusEnum.APPROVE_REVOKE)
                            .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                            .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                            .eq(SrmTenderEvaluationResult::getSectionId, split.length > 2 ? split[2] : null)
                            .update();
                    // 修改报价明细中标信息，未中标
                    quoteItemService.lambdaUpdate().set(SrmTenderBidderQuoteItem::getAwarded, false)
                            .eq(SrmTenderBidderQuoteItem::getProjectId, projectId)
                            .eq(SrmTenderBidderQuoteItem::getNoticeId, noticeId)
                            .eq(SrmTenderBidderQuoteItem::getSectionId, split.length > 2 ? split[2] : null)
                            .update();
                    break;
                // 中标公示撤销
                case SRM_PUBLICITY_AUDITING:
                    log.info("approveRejectHook 中标公示撤销");
                    this.lambdaUpdate().set(SrmTenderEvaluationResult::getPublicityAuditStatus, ApproveStatusEnum.APPROVE_REVOKE)
                            .set(SrmTenderEvaluationResult::getPublicityStatus, PublicityStatusEnum.UNPUBLISHED)
                            .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                            .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                            .update();
                    break;
                // 中标公告撤销
                case SRM_BID_NOTICE_AUDITING:
                    log.info("approveRejectHook 中标公告撤销");
                    this.lambdaUpdate().set(SrmTenderEvaluationResult::getNoticeAuditStatus, ApproveStatusEnum.APPROVE_REVOKE)
                            .set(SrmTenderEvaluationResult::getNoticeStatus, PublicNoticeStatusEnum.UNPUBLISHED)
                            .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                            .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                            .update();
                    break;
                default:
                    ExceptionUtil.checkNonNull(null, "业务类型错误！");
                    break;
            }
        } finally {
            this.clearThreadLocal();
        }
    }

    @Override
    public List<BidSectionInfoResp> bidSectionList(Long projectId, Long noticeId) {
        SrmProcurementProject procurementProject = projectService.getById(projectId);
        SrmProcurementProject srmProcurementProject = projectService.detail(procurementProject.getProjectCode(), false);
        List<SrmProcurementProjectSection> projectSectionList = srmProcurementProject.getProjectSectionList();
        // 查询定标结果
        List<SrmTenderEvaluationResult> evaluationResultList = this.lambdaQuery()
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .list();
        Map<Long, SrmTenderEvaluationResult> sectionIdMapVo = evaluationResultList.stream().collect(Collectors.toMap(
                SrmTenderEvaluationResult::getSectionId, Function.identity(), (v1, v2) -> {
                    if (v1.getAwardReportStatus() == ApproveStatusEnum.APPROVING || v1.getAwardReportStatus() == ApproveStatusEnum.APPROVE) {
                        return v1;
                    } else {
                        return v2;
                    }
                }));
        return projectSectionList.stream().map(section -> {
            BidSectionInfoResp resp = new BidSectionInfoResp();
            resp.setProjectCode(procurementProject.getProjectCode());
            resp.setProjectName(procurementProject.getProjectName());
            resp.setSectionId(section.getId());
            resp.setSectionName(section.getSectionName());
            SrmTenderEvaluationResult evaluationResult = sectionIdMapVo.get(section.getId());
            if(evaluationResult != null){
                resp.setAwardReportStatus(evaluationResult.getAwardReportStatus());
                resp.setAwardReportTime(evaluationResult.getAwardReportTime());
                resp.setAwardReportContent(evaluationResult.getAwardReportContent());
            }
            // 项目负责人
            List<SrmProjectMember> projectMemberList = srmProcurementProject.getProjectMemberList();
            if(CollectionUtils.isNotEmpty(projectMemberList)){
                List<SrmProjectMember> projectMembers = projectMemberList.stream().filter(
                        member -> member.getRole() == ProjectMemberRoleEnum.PROJECT_LEADER).toList();
                List<Long> userIds = projectMembers.stream().map(SrmProjectMember::getUserId).toList();
                List<SysUser> sysUserList = sysUserService.listByIds(userIds);
                Map<Long, String> idMapName = sysUserList.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getName));
                projectMemberList.stream().filter(
                                member -> member.getRole() == ProjectMemberRoleEnum.PROJECT_LEADER)
                        .findFirst().ifPresent(member -> {
                            resp.setUserId(member.getUserId());
                            resp.setUserName(idMapName.get(member.getUserId()));
                        });
            }
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public void awardApprovingHook(String projectId, String noticeId, String sectionId) {
        log.info("awardApprovingHook 中标结果审核中");
        this.lambdaUpdate().set(SrmTenderEvaluationResult::getAwardReportStatus, ApproveStatusEnum.APPROVING)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .eq(SrmTenderEvaluationResult::getSectionId, sectionId)
                .update();
    }

    @Override
    public void noticePublicApprovingHook(String projectId, String noticeId) {
        log.info("noticePublicApprovingHook 中标公告审核中");
        this.lambdaUpdate().set(SrmTenderEvaluationResult::getNoticeAuditStatus, ApproveStatusEnum.APPROVING)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .update();
    }

    @Override
    public void publicityApprovingHook(String projectId, String noticeId) {
        log.info("publicityApprovingHook 中标公示审核中");
        this.lambdaUpdate().set(SrmTenderEvaluationResult::getPublicityAuditStatus, ApproveStatusEnum.APPROVING)
                .eq(SrmTenderEvaluationResult::getProjectId, projectId)
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .update();
    }
}




