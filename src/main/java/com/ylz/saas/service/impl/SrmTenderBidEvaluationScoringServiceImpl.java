package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmTenderBidEvaluationScoring;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.enums.ReviewEnum;
import com.ylz.saas.mapper.SrmTenderBidEvaluationMapper;
import com.ylz.saas.mapper.SrmTenderBidEvaluationScoringMapper;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.*;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmTenderBidEvaluationScoringService;

import com.ylz.saas.service.SrmTenderBidEvaluationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.ylz.saas.enums.ApproveStatusEnum.TO_APPROVE;

/**
 * 针对表【srm_tender_bid_evaluation_scoring(评标专家打分)】的数据库操作Service实现
 * <AUTHOR>
 * @createDate 2025-07-09
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmTenderBidEvaluationScoringServiceImpl extends ServiceImpl<SrmTenderBidEvaluationScoringMapper, SrmTenderBidEvaluationScoring>
        implements SrmTenderBidEvaluationScoringService {

    private final SrmProcurementProjectService srmProcurementProjectService;
    private final SrmTenderBidEvaluationService srmTenderBidEvaluationService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSubmitScoring(SrmTenderBidEvaluationScoringBatchReq req) {
        List<SrmTenderBidEvaluationScoringReq> scoringList = req.getScoringList();
        SrmTenderBidEvaluationScoring.SubmitStatus status = req.getStatus();
        if (scoringList == null || scoringList.isEmpty()) {
            return true;
        }

        // 批量查询已存在的记录，提高性能
        Map<String, SrmTenderBidEvaluationScoring> existingRecordsMap = batchFindExistingRecords(scoringList,req.getCurrentRound());

        // 分离新增和修改的数据
        List<SrmTenderBidEvaluationScoring> insertList = new ArrayList<>();
        List<SrmTenderBidEvaluationScoring> updateList = new ArrayList<>();

        Long projectId = null;
        for (SrmTenderBidEvaluationScoringReq scoringReq : scoringList) {
            projectId = scoringReq.getProjectId();
            SrmTenderBidEvaluationScoring entity = new SrmTenderBidEvaluationScoring();
            BeanUtil.copyProperties(scoringReq, entity);
            entity.setStatus(status);
            entity.setCurrentRound(req.getCurrentRound());
            // 设置evaluationId：如果请求中没有提供，则根据sectionId查询当前轮次的评标委员会ID
            if (entity.getEvaluationId() == null && entity.getSectionId() != null) {
                SrmTenderBidEvaluationResp evaluation = srmTenderBidEvaluationService.getBySectionId(entity.getSectionId());
                if (evaluation != null) {
                    entity.setEvaluationId(evaluation.getId());
                }
            }

            // 根据业务唯一性判断是新增还是修改
            String businessKey = buildBusinessKey(scoringReq);
            SrmTenderBidEvaluationScoring existingRecord = existingRecordsMap.get(businessKey);

            if (existingRecord != null) {
                // 修改操作：已存在相同业务键的记录
                entity.setId(existingRecord.getId());
                updateList.add(entity);
            } else {
                // 新增操作：不存在相同业务键的记录
                entity.setId(null); // 确保ID为空，让数据库自动生成
                entity.setDelFlag(0);
                insertList.add(entity);
            }
        }

        boolean result = true;

        // 批量新增
        if (!insertList.isEmpty()) {
            result = saveBatch(insertList);
        }

        // 批量修改
        if (!updateList.isEmpty() && result) {
            result = updateBatchById(updateList);
        }

        SrmProcurementProject project = srmProcurementProjectService.getById(projectId);
        srmProcurementProjectService.updateProgressStatus(project.getProjectCode(), ProjectProgressStatusEnum.EVALUATING, false);

        return result;
    }

    /**
     * 批量查找已存在的记录，提高性能
     * 业务唯一性：项目ID + 标段ID + 专家ID + 评分详情ID + 供应商ID
     */
    private Map<String, SrmTenderBidEvaluationScoring> batchFindExistingRecords(List<SrmTenderBidEvaluationScoringReq> scoringList,Integer currentRound) {
        if (scoringList.isEmpty()) {
            return new HashMap<>();
        }

        // 提取所有相关的项目ID、标段ID等，用于批量查询
        Set<Long> projectIds = scoringList.stream().map(SrmTenderBidEvaluationScoringReq::getProjectId).collect(Collectors.toSet());
        Set<Long> sectionIds = scoringList.stream().map(SrmTenderBidEvaluationScoringReq::getSectionId).collect(Collectors.toSet());
        Set<Long> userIds = scoringList.stream().map(SrmTenderBidEvaluationScoringReq::getUserId).collect(Collectors.toSet());
        Set<Long> scoringDetailIds = scoringList.stream().map(SrmTenderBidEvaluationScoringReq::getScoringDetailId).collect(Collectors.toSet());
        Set<Long> supplierIds = scoringList.stream().map(SrmTenderBidEvaluationScoringReq::getTenantSupplierId).collect(Collectors.toSet());
        Set<Long> evaluationIds = scoringList.stream()
                .map(SrmTenderBidEvaluationScoringReq::getEvaluationId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量查询可能存在的记录
        LambdaQueryWrapper<SrmTenderBidEvaluationScoring> queryWrapper = new LambdaQueryWrapper<SrmTenderBidEvaluationScoring>()
                .in(SrmTenderBidEvaluationScoring::getProjectId, projectIds)
                .in(SrmTenderBidEvaluationScoring::getSectionId, sectionIds)
                .in(SrmTenderBidEvaluationScoring::getUserId, userIds)
                .in(SrmTenderBidEvaluationScoring::getScoringDetailId, scoringDetailIds)
                .in(SrmTenderBidEvaluationScoring::getTenantSupplierId, supplierIds)
                .eq(SrmTenderBidEvaluationScoring::getCurrentRound,currentRound)
                .eq(SrmTenderBidEvaluationScoring::getDelFlag, 0);

        if (!evaluationIds.isEmpty()) {
            queryWrapper.in(SrmTenderBidEvaluationScoring::getEvaluationId, evaluationIds);
        }

        List<SrmTenderBidEvaluationScoring> existingRecords = this.list(queryWrapper);

        // 构建业务键到记录的映射
        return existingRecords.stream()
                .collect(Collectors.toMap(
                        this::buildBusinessKey,
                        record -> record,
                        (existing, replacement) -> existing // 如果有重复键，保留现有的
                ));
    }

    /**
     * 构建业务唯一键
     */
    private String buildBusinessKey(SrmTenderBidEvaluationScoringReq req) {
        return String.format("%d_%d_%d_%d_%d_%d",
                req.getProjectId(), req.getSectionId(), req.getUserId(),
                req.getScoringDetailId(), req.getTenantSupplierId(),
                req.getEvaluationId() != null ? req.getEvaluationId() : 0L);
    }

    /**
     * 构建业务唯一键（从实体对象）
     */
    private String buildBusinessKey(SrmTenderBidEvaluationScoring entity) {
        return String.format("%d_%d_%d_%d_%d_%d",
                entity.getProjectId(), entity.getSectionId(), entity.getUserId(),
                entity.getScoringDetailId(), entity.getTenantSupplierId(),
                entity.getEvaluationId() != null ? entity.getEvaluationId() : 0L);
    }



//    @Override
//    public List<SrmTenderBidEvaluationScoringQueryResp> getExpertScoringByNode(SrmTenderBidEvaluationScoringQueryReq req) {
//        return baseMapper.getExpertScoringByNode(req);
//    }

    @Override
    public boolean revokeScoringResult(SrmTenderBidEvaluationScoringQueryReq req) {
        ExceptionUtil.check(req.getUserId() == null, "500", "撤回时需要传入专家信息");
        ExceptionUtil.check(StrUtil.isBlank(req.getNodeName()), "500", "撤回时需要传入节点名称");

        // 根据节点名称撤回评分结果，需要联查srm_tender_evaluation_standard表
        return baseMapper.revokeScoringResultByNode(req) > 0;
    }

    @Override
    public SrmTenderNodeEvaluationSummaryResp getNodeEvaluationSummary(SrmTenderBidEvaluationScoringQueryReq req) {

        // 查询节点下所有专家对各个供应商的评审/评分详情
        List<SrmTenderBidEvaluationScoringQueryResp> details = baseMapper.getNodeEvaluationDetails(req);

        if (details.isEmpty()) {
            return new SrmTenderNodeEvaluationSummaryResp();
        }

        // 构建响应对象
        return buildNodeEvaluationSummary(details, req);
    }

    /**
     * 构建节点评审汇总响应对象
     */
    private SrmTenderNodeEvaluationSummaryResp buildNodeEvaluationSummary(
            List<SrmTenderBidEvaluationScoringQueryResp> details,
            SrmTenderBidEvaluationScoringQueryReq req) {

        SrmTenderNodeEvaluationSummaryResp response = new SrmTenderNodeEvaluationSummaryResp();

        if (details.isEmpty()) {
            return response;
        }

        SrmTenderBidEvaluationScoringQueryResp first = details.get(0);
        response.setNodeName(first.getNodeName());
        response.setNodeType(first.getType());

        // 计算节点权重和总分（仅评分项有效）
        if (ReviewEnum.SCORE.getCode().equals(first.getType())) {
//            BigDecimal totalWeight = details.stream()
//                    .map(SrmTenderBidEvaluationScoringQueryResp::getWeight)
//                    .filter(Objects::nonNull)
//                    .map(BigDecimal::valueOf)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            Double weight = details.get(0).getWeight();
            ExceptionUtil.check(weight == null, "500", "节点权重不能为空");
            BigDecimal totalWeight = BigDecimal.valueOf(weight);
            response.setNodeWeight(totalWeight);

            BigDecimal totalScore = details.stream()
                    .map(SrmTenderBidEvaluationScoringQueryResp::getMaxScore)
                    .filter(Objects::nonNull)
                    .map(BigDecimal::valueOf)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            response.setNodeTotalScore(totalScore);
        }

        // 构建专家列表
        response.setExperts(buildExpertList(details));

        // 构建供应商评审结果列表
        response.setSupplierResults(buildSupplierResults(details, first.getType()));

        // 构建结论数据
        response.setConclusionDataList(buildConclusionData(details, first.getType()));

        return response;
    }

    /**
     * 构建专家列表
     */
    private List<SrmTenderNodeEvaluationSummaryResp.ExpertInfo> buildExpertList(
            List<SrmTenderBidEvaluationScoringQueryResp> details) {

        return details.stream()
                .collect(Collectors.groupingBy(
                        SrmTenderBidEvaluationScoringQueryResp::getUserId,
                        LinkedHashMap::new,
                        Collectors.toList()
                ))
                .entrySet().stream()
                .map(entry -> {
                    SrmTenderBidEvaluationScoringQueryResp first = entry.getValue().get(0);
                    SrmTenderNodeEvaluationSummaryResp.ExpertInfo expert =
                            new SrmTenderNodeEvaluationSummaryResp.ExpertInfo();
                    expert.setUserId(first.getUserId());
                    expert.setExpertName(first.getExpertName());
                    expert.setExpertCode(first.getExpertCode());
                    expert.setExpertCategory(first.getExpertCategory());
                    expert.setIsLeader("EVALUATION_LEADER".equals(first.getRole()));
                    return expert;
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建供应商评审结果列表
     */
    private List<SrmTenderNodeEvaluationSummaryResp.SupplierEvaluationResult> buildSupplierResults(
            List<SrmTenderBidEvaluationScoringQueryResp> details, String nodeType) {

        // 按供应商分组
        Map<Long, List<SrmTenderBidEvaluationScoringQueryResp>> supplierGroups = details.stream()
                .collect(Collectors.groupingBy(
                        SrmTenderBidEvaluationScoringQueryResp::getTenantSupplierId,
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        return supplierGroups.entrySet().stream()
                .map(entry -> {
                    List<SrmTenderBidEvaluationScoringQueryResp> supplierDetails = entry.getValue();
                    SrmTenderBidEvaluationScoringQueryResp first = supplierDetails.get(0);

                    SrmTenderNodeEvaluationSummaryResp.SupplierEvaluationResult result =
                            new SrmTenderNodeEvaluationSummaryResp.SupplierEvaluationResult();
                    result.setTenantSupplierId(first.getTenantSupplierId());
                    result.setSupplierName(first.getSupplierName());

                    // 构建专家评审详情
                    result.setExpertDetails(buildExpertEvaluationDetails(supplierDetails, nodeType));

                    // 计算结论和得分
                    if (ReviewEnum.REVIEW.getCode().equals(nodeType)) {
                        result.setConclusion(calculateReviewConclusion(supplierDetails));
                    } else if (ReviewEnum.SCORE.getCode().equals(nodeType)) {
                        calculateScoringResults(result, supplierDetails);
                    }

                    return result;
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建专家评审详情
     */
    private List<SrmTenderNodeEvaluationSummaryResp.ExpertEvaluationDetail> buildExpertEvaluationDetails(
            List<SrmTenderBidEvaluationScoringQueryResp> supplierDetails, String nodeType) {

        // 按专家分组
        Map<Long, List<SrmTenderBidEvaluationScoringQueryResp>> expertGroups = supplierDetails.stream()
                .collect(Collectors.groupingBy(
                        SrmTenderBidEvaluationScoringQueryResp::getUserId,
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        return expertGroups.entrySet().stream()
                .map(entry -> {
                    List<SrmTenderBidEvaluationScoringQueryResp> expertDetails = entry.getValue();
                    SrmTenderBidEvaluationScoringQueryResp first = expertDetails.get(0);

                    SrmTenderNodeEvaluationSummaryResp.ExpertEvaluationDetail detail =
                            new SrmTenderNodeEvaluationSummaryResp.ExpertEvaluationDetail();
                    detail.setUserId(first.getUserId());
                    detail.setExpertName(first.getExpertName());

                    // 检查是否有组长汇总数据
                    boolean hasLeaderSummary = expertDetails.stream()
                            .anyMatch(d -> d.getScoringType() != null &&
                                    "LEADER_SUMMARY".equals(d.getScoringType().name()));
                    detail.setIsLeaderSummary(hasLeaderSummary);

                    if (ReviewEnum.REVIEW.getCode().equals(nodeType)) {
                        // 评审项逻辑：优先使用组长汇总，否则全部通过才算通过
                        calculateReviewResult(detail, expertDetails, hasLeaderSummary);
                    } else if (ReviewEnum.SCORE.getCode().equals(nodeType)) {
                        // 评分项逻辑：计算总分
                        calculateScoringResult(detail, expertDetails);
                    }

                    return detail;
                })
                .collect(Collectors.toList());
    }

    /**
     * 计算评审结果
     */
    private void calculateReviewResult(SrmTenderNodeEvaluationSummaryResp.ExpertEvaluationDetail detail,
                                     List<SrmTenderBidEvaluationScoringQueryResp> expertDetails,
                                     boolean hasLeaderSummary) {
        if (hasLeaderSummary) {
            // 优先使用组长汇总结果
            SrmTenderBidEvaluationScoringQueryResp leaderSummary = expertDetails.stream()
                    .filter(d -> d.getScoringType() != null &&
                            "LEADER_SUMMARY".equals(d.getScoringType().name()))
                    .findFirst()
                    .orElse(null);

            if (leaderSummary != null) {
                detail.setResult(leaderSummary.getIsConform() != null && leaderSummary.getIsConform() == 1 ? "通过" : "不通过");
                detail.setConclusion(leaderSummary.getConclusion());
                detail.setStatus(leaderSummary.getStatus() != null ? leaderSummary.getStatus().getDesc() : "");
            }
        } else {
            // 全部评审通过则为通过，有一个不通过则为不通过
            boolean allPassed = expertDetails.stream()
                    .filter(d -> d.getIsConform() != null)
                    .allMatch(d -> d.getIsConform() == 1);

            boolean hasAnyResult = expertDetails.stream()
                    .anyMatch(d -> d.getIsConform() != null);

            if (hasAnyResult) {
                detail.setResult(allPassed ? "通过" : "不通过");
            } else {
                detail.setResult("未评审");
            }

            // 合并结论
            String conclusion = expertDetails.stream()
                    .map(SrmTenderBidEvaluationScoringQueryResp::getConclusion)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining("; "));
            detail.setConclusion(conclusion);
        }
    }

    /**
     * 计算评分结果
     */
    private void calculateScoringResult(SrmTenderNodeEvaluationSummaryResp.ExpertEvaluationDetail detail,
                                      List<SrmTenderBidEvaluationScoringQueryResp> expertDetails) {
        // 计算该专家对该供应商在此节点下的总分
        BigDecimal totalScore = expertDetails.stream()
                .filter(d -> d.getScore() != null)
                .map(SrmTenderBidEvaluationScoringQueryResp::getScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        detail.setTotalScore(totalScore);
        detail.setResult(totalScore.toString());

        // 合并结论
        String conclusion = expertDetails.stream()
                .map(SrmTenderBidEvaluationScoringQueryResp::getConclusion)
                .filter(Objects::nonNull)
                .collect(Collectors.joining("; "));
        detail.setConclusion(conclusion);

        // 设置状态
        SrmTenderBidEvaluationScoringQueryResp first = expertDetails.get(0);
        detail.setStatus(first.getStatus() != null ? first.getStatus().getDesc() : "");
    }

    /**
     * 计算评审结论（供应商级别）
     */
    private String calculateReviewConclusion(List<SrmTenderBidEvaluationScoringQueryResp> supplierDetails) {
        // 优先查看有没有组长汇总
        boolean hasLeaderSummary = supplierDetails.stream()
                .anyMatch(d -> d.getScoringType() != null &&
                        "LEADER_SUMMARY".equals(d.getScoringType().name()));

        if (hasLeaderSummary) {
            // 使用组长汇总结果
            boolean leaderPassed = supplierDetails.stream()
                    .filter(d -> d.getScoringType() != null &&
                            "LEADER_SUMMARY".equals(d.getScoringType().name()))
                    .anyMatch(d -> d.getIsConform() != null && d.getIsConform() == 1);
            return leaderPassed ? "通过" : "不通过";
        } else {
            // 按专家分组，每个专家的评审项全部通过才算该专家通过
            Map<Long, List<SrmTenderBidEvaluationScoringQueryResp>> expertGroups = supplierDetails.stream()
                    .collect(Collectors.groupingBy(SrmTenderBidEvaluationScoringQueryResp::getUserId));

            boolean allExpertsPassed = expertGroups.values().stream()
                    .allMatch(expertDetails -> {
                        boolean hasAnyResult = expertDetails.stream()
                                .anyMatch(d -> d.getIsConform() != null);
                        if (!hasAnyResult) {
                            return false; // 未评审算不通过
                        }
                        return expertDetails.stream()
                                .filter(d -> d.getIsConform() != null)
                                .allMatch(d -> d.getIsConform() == 1);
                    });

            return allExpertsPassed ? "通过" : "不通过";
        }
    }

    /**
     * 计算评分结果（供应商级别）
     */
    private void calculateScoringResults(SrmTenderNodeEvaluationSummaryResp.SupplierEvaluationResult result,
                                        List<SrmTenderBidEvaluationScoringQueryResp> supplierDetails) {
        // 按专家分组计算每个专家的总分
        Map<Long, List<SrmTenderBidEvaluationScoringQueryResp>> expertGroups = supplierDetails.stream()
                .collect(Collectors.groupingBy(SrmTenderBidEvaluationScoringQueryResp::getUserId));

        List<BigDecimal> expertTotalScores = new ArrayList<>();
        for (List<SrmTenderBidEvaluationScoringQueryResp> expertDetails : expertGroups.values()) {
            BigDecimal expertTotal = expertDetails.stream()
                    .filter(d -> d.getScore() != null)
                    .map(SrmTenderBidEvaluationScoringQueryResp::getScore)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            expertTotalScores.add(expertTotal);
        }

        if (!expertTotalScores.isEmpty()) {
            // 计算平均分
            BigDecimal totalSum = expertTotalScores.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal averageScore = totalSum.divide(BigDecimal.valueOf(expertTotalScores.size()), 2, RoundingMode.HALF_UP);
            result.setAverageScore(averageScore);
            result.setConclusion(averageScore.toString());

            // 计算实际得分（平均分 * 权重）
            BigDecimal nodeWeight = supplierDetails.stream()
                    .map(SrmTenderBidEvaluationScoringQueryResp::getWeight)
                    .filter(Objects::nonNull)
                    .map(weight -> BigDecimal.valueOf(weight))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (nodeWeight.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal actualScore = result.getAverageScore().multiply(nodeWeight);
                result.setActualScore(actualScore);
            }
        } else {
            result.setAverageScore(BigDecimal.ZERO);
            result.setActualScore(BigDecimal.ZERO);
            result.setConclusion("未评分");
        }
    }

    /**
     * 构建结论数据
     */
    private List<SrmTenderNodeEvaluationSummaryResp.ConclusionData> buildConclusionData(
            List<SrmTenderBidEvaluationScoringQueryResp> details, String nodeType) {

        List<SrmTenderNodeEvaluationSummaryResp.ConclusionData> conclusionDataList = new ArrayList<>();

        if (ReviewEnum.SCORE.getCode().equals(nodeType)) {
            // 评分项返回三行：结论（平均数）、权重、实际得分

            // 按供应商分组
            Map<Long, List<SrmTenderBidEvaluationScoringQueryResp>> supplierGroups = details.stream()
                    .collect(Collectors.groupingBy(
                            SrmTenderBidEvaluationScoringQueryResp::getTenantSupplierId,
                            LinkedHashMap::new,
                            Collectors.toList()
                    ));

            // 1. 结论行（平均数）
            SrmTenderNodeEvaluationSummaryResp.ConclusionData conclusionRow =
                    new SrmTenderNodeEvaluationSummaryResp.ConclusionData();
            conclusionRow.setDataType("结论");
            Map<Long, String> conclusionMap = new HashMap<>();

            // 2. 权重行
            SrmTenderNodeEvaluationSummaryResp.ConclusionData weightRow =
                    new SrmTenderNodeEvaluationSummaryResp.ConclusionData();
            weightRow.setDataType("权重");
            Map<Long, String> weightMap = new HashMap<>();

            // 3. 实际得分行
            SrmTenderNodeEvaluationSummaryResp.ConclusionData actualScoreRow =
                    new SrmTenderNodeEvaluationSummaryResp.ConclusionData();
            actualScoreRow.setDataType("实际得分");
            Map<Long, String> actualScoreMap = new HashMap<>();

            // 计算节点权重
            Double weight = details.get(0).getWeight();
            ExceptionUtil.check(weight == null, "500","节点权重不能为空");
            BigDecimal nodeWeight = BigDecimal.valueOf(weight);
//            BigDecimal nodeWeight = details.stream()
//                    .map(SrmTenderBidEvaluationScoringQueryResp::getWeight)
//                    .filter(Objects::nonNull)
//                    .map(BigDecimal::valueOf)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            for (Map.Entry<Long, List<SrmTenderBidEvaluationScoringQueryResp>> entry : supplierGroups.entrySet()) {
                Long supplierId = entry.getKey();
                List<SrmTenderBidEvaluationScoringQueryResp> supplierDetails = entry.getValue();

                // 检查是否有组长汇总数据
                boolean hasLeaderSummary = supplierDetails.stream()
                        .anyMatch(d -> d.getScoringType() != null &&
                                "LEADER_SUMMARY".equals(d.getScoringType().name()));

                if (hasLeaderSummary) {
                    // 使用组长汇总结果
                    String leaderConclusion = supplierDetails.stream()
                            .filter(d -> d.getScoringType() != null &&
                                    "LEADER_SUMMARY".equals(d.getScoringType().name()))
                            .map(SrmTenderBidEvaluationScoringQueryResp::getConclusion)
                            .filter(Objects::nonNull)
                            .findFirst()
                            .orElse("未汇总");
                    conclusionMap.put(supplierId, leaderConclusion);
                } else {
                    // 使用专家评分的平均数
                    Map<Long, List<SrmTenderBidEvaluationScoringQueryResp>> expertGroups = supplierDetails.stream()
                            .collect(Collectors.groupingBy(SrmTenderBidEvaluationScoringQueryResp::getUserId));

                    List<BigDecimal> expertTotalScores = new ArrayList<>();
                    for (List<SrmTenderBidEvaluationScoringQueryResp> expertDetails : expertGroups.values()) {
                        BigDecimal expertTotal = expertDetails.stream()
                                .filter(d -> d.getScore() != null)
                                .map(SrmTenderBidEvaluationScoringQueryResp::getScore)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        expertTotalScores.add(expertTotal);
                    }

                    if (!expertTotalScores.isEmpty()) {
                        BigDecimal totalSum = expertTotalScores.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal average = totalSum.divide(BigDecimal.valueOf(expertTotalScores.size()), 2, RoundingMode.HALF_UP);
                        conclusionMap.put(supplierId, average.toString());

                        // 计算实际得分（平均分 * 权重）
                        if (nodeWeight.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal actualScore = average.multiply(nodeWeight).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                            actualScoreMap.put(supplierId, actualScore.toString());
                        } else {
                            actualScoreMap.put(supplierId, "0.00");
                        }
                    } else {
                        conclusionMap.put(supplierId, "未评分");
                        actualScoreMap.put(supplierId, "0.00");
                    }
                }

                // 权重对所有供应商都是一样的
                weightMap.put(supplierId, nodeWeight.toString());
            }

            conclusionRow.setSupplierDataMap(conclusionMap);
            weightRow.setSupplierDataMap(weightMap);
            actualScoreRow.setSupplierDataMap(actualScoreMap);

            conclusionDataList.add(conclusionRow);
            conclusionDataList.add(weightRow);
            conclusionDataList.add(actualScoreRow);

        } else if (ReviewEnum.REVIEW.getCode().equals(nodeType)) {
            // 评审项返回结论数据
            SrmTenderNodeEvaluationSummaryResp.ConclusionData conclusionRow =
                    new SrmTenderNodeEvaluationSummaryResp.ConclusionData();
            conclusionRow.setDataType("结论");
            Map<Long, String> conclusionMap = new HashMap<>();

            // 按供应商分组
            Map<Long, List<SrmTenderBidEvaluationScoringQueryResp>> supplierGroups = details.stream()
                    .collect(Collectors.groupingBy(
                            SrmTenderBidEvaluationScoringQueryResp::getTenantSupplierId,
                            LinkedHashMap::new,
                            Collectors.toList()
                    ));

            for (Map.Entry<Long, List<SrmTenderBidEvaluationScoringQueryResp>> entry : supplierGroups.entrySet()) {
                Long supplierId = entry.getKey();
                List<SrmTenderBidEvaluationScoringQueryResp> supplierDetails = entry.getValue();

                // 检查是否有组长汇总数据
                boolean hasLeaderSummary = supplierDetails.stream()
                        .anyMatch(d -> d.getScoringType() != null &&
                                "LEADER_SUMMARY".equals(d.getScoringType().name()));

                if (hasLeaderSummary) {
                    // 使用组长汇总结果
                    boolean leaderPassed = supplierDetails.stream()
                            .filter(d -> d.getScoringType() != null &&
                                    "LEADER_SUMMARY".equals(d.getScoringType().name()))
                            .anyMatch(d -> d.getIsConform() != null && d.getIsConform() == 1);
                    conclusionMap.put(supplierId, leaderPassed ? "通过" : "不通过");
                } else {
                    // 使用专家评审的总体结果
                    String conclusion = calculateReviewConclusion(supplierDetails);
                    conclusionMap.put(supplierId, conclusion);
                }
            }

            conclusionRow.setSupplierDataMap(conclusionMap);
            conclusionDataList.add(conclusionRow);
        }

        return conclusionDataList;
    }

    @Override
    public SrmTenderBidEvaluationScoringTableResp getExpertScoringTableByNode(SrmTenderBidEvaluationScoringQueryReq req) {
        // 验证必传参数
        ExceptionUtil.check(req.getUserId() == null, "500", "专家用户ID不能为空");

        // 获取原始数据
        List<SrmTenderBidEvaluationScoringQueryResp> rawData = baseMapper.getExpertScoringByNode(req);

        if (rawData == null || rawData.isEmpty()) {
            return new SrmTenderBidEvaluationScoringTableResp();
        }

        SrmTenderBidEvaluationScoringTableResp tableResp = new SrmTenderBidEvaluationScoringTableResp();

        // 获取节点类型（评审项或评分项）
        SrmTenderBidEvaluationScoringQueryResp node0 = rawData.get(0);
        String nodeType = node0.getType();

        // 计算节点总分和权重
        BigDecimal nodeTotalScore = BigDecimal.valueOf(node0.getTotalScore() != null ? node0.getTotalScore() : 0);
        Double nodeWeight = node0.getWeight();

//        Integer nodeTotalScore = rawData.stream()
//                .collect(Collectors.groupingBy(SrmTenderBidEvaluationScoringQueryResp::getStandardId))
//                .values()
//                .stream()
//                .mapToInt(group -> {
//                    SrmTenderBidEvaluationScoringQueryResp first = group.get(0);
//                    return first.getTotalScore() != null ? first.getTotalScore() : 0;
//                })
//                .sum();
//
//        Double nodeWeight = rawData.stream()
//                .collect(Collectors.groupingBy(SrmTenderBidEvaluationScoringQueryResp::getStandardId))
//                .values()
//                .stream()
//                .mapToDouble(group -> {
//                    SrmTenderBidEvaluationScoringQueryResp first = group.get(0);
//                    return first.getWeight() != null ? first.getWeight() : 0.0;
//                })
//                .sum();

        tableResp.setNodeTotalScore(nodeTotalScore);
        tableResp.setNodeWeight(nodeWeight);

        // 提取供应商信息（去重）
        List<SrmTenderBidEvaluationScoringTableResp.SupplierInfo> suppliers = rawData.stream()
                .collect(Collectors.groupingBy(
                        SrmTenderBidEvaluationScoringQueryResp::getTenantSupplierId,
                        LinkedHashMap::new,
                        Collectors.toList()
                ))
                .values()
                .stream()
                .map(group -> {
                    SrmTenderBidEvaluationScoringQueryResp first = group.get(0);
                    SrmTenderBidEvaluationScoringTableResp.SupplierInfo supplier =
                            new SrmTenderBidEvaluationScoringTableResp.SupplierInfo();
                    supplier.setTenantSupplierId(first.getTenantSupplierId());
                    supplier.setSupplierName(first.getSupplierName());
                    supplier.setQuoteStatus(first.getQuoteStatus());
                    return supplier;
                })
                .toList();

        // 按评审标准分组
        Map<Long, List<SrmTenderBidEvaluationScoringQueryResp>> standardGroups = rawData.stream()
                .collect(Collectors.groupingBy(
                        SrmTenderBidEvaluationScoringQueryResp::getStandardId,
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        // 构建评审标准行数据
        List<SrmTenderBidEvaluationScoringTableResp.EvaluationStandardRow> evaluationStandards =
                standardGroups.entrySet().stream()
                        .map(entry -> {
                            List<SrmTenderBidEvaluationScoringQueryResp> standardItems = entry.getValue();
                            SrmTenderBidEvaluationScoringQueryResp first = standardItems.get(0);

                            SrmTenderBidEvaluationScoringTableResp.EvaluationStandardRow row =
                                    new SrmTenderBidEvaluationScoringTableResp.EvaluationStandardRow();
                            row.setStandardId(first.getStandardId());
                            row.setItemName(first.getItemName());
                            row.setItemDescription(first.getItemDescription());
                            row.setMaxScore(first.getMaxScore());
                            row.setMinScore(first.getMinScore());
                            row.setWeight(first.getWeight());
                            row.setType(first.getType());
                            row.setIsOverallSummary(false);

                            // 为每个供应商创建打分信息
                            List<SrmTenderBidEvaluationScoringTableResp.SupplierScoringInfo> supplierScorings =
                                    new ArrayList<>();

                            for (SrmTenderBidEvaluationScoringTableResp.SupplierInfo supplier : suppliers) {
                                // 查找该供应商在当前评审标准下的打分信息
                                SrmTenderBidEvaluationScoringQueryResp scoringData = standardItems.stream()
                                        .filter(item -> item.getTenantSupplierId().equals(supplier.getTenantSupplierId())
                                                && item.getUserId().equals(req.getUserId()))
                                        .findFirst()
                                        .orElse(null);

                                SrmTenderBidEvaluationScoringTableResp.SupplierScoringInfo scoringInfo =
                                        new SrmTenderBidEvaluationScoringTableResp.SupplierScoringInfo();
                                scoringInfo.setTenantSupplierId(supplier.getTenantSupplierId());
                                scoringInfo.setTenantSupplierName(supplier.getSupplierName());
                                scoringInfo.setUserId(req.getUserId());

                                if (scoringData != null) {
                                    scoringInfo.setExpertName(scoringData.getExpertName());
                                    scoringInfo.setScoringId(scoringData.getScoringId());
                                    scoringInfo.setResponseId(scoringData.getResponseId());
                                    scoringInfo.setScore(scoringData.getScore());
                                    scoringInfo.setIsConform(scoringData.getIsConform());
                                    scoringInfo.setConclusion(scoringData.getConclusion());
                                    scoringInfo.setStatus(scoringData.getStatus());
                                    scoringInfo.setIsScored(scoringData.getIsScored());
                                    scoringInfo.setIsSummarized(SrmTenderBidEvaluationScoring.SubmitStatus.SUMMARIZED.equals(scoringData.getStatus()));
                                    scoringInfo.setIsLeaderSummary(false);
                                } else {
                                    // 未打分的情况
                                    scoringInfo.setIsScored(false);
                                    scoringInfo.setIsSummarized(false);
                                    scoringInfo.setIsLeaderSummary(false);
                                }

                                supplierScorings.add(scoringInfo);
                            }

                            row.setSupplierScorings(supplierScorings);
                            return row;
                        })
                        .collect(Collectors.toList());

        tableResp.setSuppliers(suppliers);
        tableResp.setEvaluationStandards(evaluationStandards);

        // 添加合计行
        addSummaryRow(req, tableResp, suppliers, nodeType, rawData);

        return tableResp;
    }

    /**
     * 添加合计行
     */
    private void addSummaryRow(SrmTenderBidEvaluationScoringQueryReq req,
                             SrmTenderBidEvaluationScoringTableResp tableResp,
                             List<SrmTenderBidEvaluationScoringTableResp.SupplierInfo> suppliers,
                             String nodeType,
                             List<SrmTenderBidEvaluationScoringQueryResp> rawData) {

        // 创建合计行
        SrmTenderBidEvaluationScoringTableResp.EvaluationStandardRow summaryRow =
                new SrmTenderBidEvaluationScoringTableResp.EvaluationStandardRow();
        summaryRow.setStandardId(null);
        summaryRow.setItemName("【合计】");
        summaryRow.setItemDescription("");
        summaryRow.setType(nodeType);
        summaryRow.setIsOverallSummary(true);

        List<SrmTenderBidEvaluationScoringTableResp.SupplierScoringInfo> supplierScorings = new ArrayList<>();

        // 为每个供应商计算合计结果
        for (SrmTenderBidEvaluationScoringTableResp.SupplierInfo supplier : suppliers) {
            SrmTenderBidEvaluationScoringTableResp.SupplierScoringInfo scoringInfo =
                    new SrmTenderBidEvaluationScoringTableResp.SupplierScoringInfo();
            scoringInfo.setTenantSupplierId(supplier.getTenantSupplierId());
            scoringInfo.setTenantSupplierName(supplier.getSupplierName());
            scoringInfo.setUserId(req.getUserId());
            scoringInfo.setExpertName("合计");
            scoringInfo.setIsLeaderSummary(false);

            if (ReviewEnum.SCORE.getCode().equals(nodeType)) {
                // 评分项：返回该专家对该供应商的累加分值
                calculateScoringSum(req, supplier, scoringInfo, rawData);
            } else if (ReviewEnum.REVIEW.getCode().equals(nodeType)) {
                // 评审项：返回该专家对该供应商的符合情况（全符合则符合，有一个不符合则不符合）
                calculateReviewSum(req, supplier, scoringInfo, rawData);
            }

            supplierScorings.add(scoringInfo);
        }

        summaryRow.setSupplierScorings(supplierScorings);
        tableResp.getEvaluationStandards().add(summaryRow);
    }

    /**
     * 计算评分项的累加分值
     */
    private void calculateScoringSum(SrmTenderBidEvaluationScoringQueryReq req,
                                   SrmTenderBidEvaluationScoringTableResp.SupplierInfo supplier,
                                   SrmTenderBidEvaluationScoringTableResp.SupplierScoringInfo scoringInfo,
                                   List<SrmTenderBidEvaluationScoringQueryResp> rawData) {

        // 计算该专家对该供应商在此节点下的总分
        BigDecimal totalScore = rawData.stream()
                .filter(data -> data.getTenantSupplierId().equals(supplier.getTenantSupplierId())
                        && data.getUserId().equals(req.getUserId())
                        && data.getScore() != null)
                .map(SrmTenderBidEvaluationScoringQueryResp::getScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        scoringInfo.setScore(totalScore);
        scoringInfo.setConclusion(totalScore.toString());
        scoringInfo.setIsScored(totalScore.compareTo(BigDecimal.ZERO) > 0);
        scoringInfo.setIsSummarized(true);
    }

    /**
     * 计算评审项的符合情况
     */
    private void calculateReviewSum(SrmTenderBidEvaluationScoringQueryReq req,
                                  SrmTenderBidEvaluationScoringTableResp.SupplierInfo supplier,
                                  SrmTenderBidEvaluationScoringTableResp.SupplierScoringInfo scoringInfo,
                                  List<SrmTenderBidEvaluationScoringQueryResp> rawData) {

        // 获取该专家对该供应商在此节点下的所有评审结果
        List<SrmTenderBidEvaluationScoringQueryResp> supplierReviews = rawData.stream()
                .filter(data -> data.getTenantSupplierId().equals(supplier.getTenantSupplierId())
                        && data.getUserId().equals(req.getUserId()))
                .toList();

        if (supplierReviews.isEmpty()) {
            scoringInfo.setIsConform(null);
            scoringInfo.setConclusion("未评审");
            scoringInfo.setIsScored(false);
            scoringInfo.setIsSummarized(false);
            return;
        }

        // 检查是否有评审结果
        boolean hasAnyResult = supplierReviews.stream()
                .anyMatch(review -> review.getIsConform() != null);

        if (!hasAnyResult) {
            scoringInfo.setIsConform(null);
            scoringInfo.setConclusion("未评审");
            scoringInfo.setIsScored(false);
            scoringInfo.setIsSummarized(false);
            return;
        }

        // 全符合则符合，有一个不符合则不符合
        boolean allConform = supplierReviews.stream()
                .filter(review -> review.getIsConform() != null)
                .allMatch(review -> review.getIsConform() == 1);

        scoringInfo.setIsConform(allConform ? 1 : 0);
        scoringInfo.setConclusion(allConform ? "符合" : "不符合");
        scoringInfo.setIsScored(true);
        scoringInfo.setIsSummarized(true);
    }


}
