package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmRecruitSupplierSignUpCertificate;
import com.ylz.saas.mapper.SrmRecruitSupplierSignUpCertificateMapper;
import com.ylz.saas.service.SrmRecruitSupplierSignUpCertificateService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 供应商招募报名表Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmRecruitSupplierSignUpCertificateServiceImpl extends ServiceImpl<SrmRecruitSupplierSignUpCertificateMapper, SrmRecruitSupplierSignUpCertificate> implements SrmRecruitSupplierSignUpCertificateService {

}
