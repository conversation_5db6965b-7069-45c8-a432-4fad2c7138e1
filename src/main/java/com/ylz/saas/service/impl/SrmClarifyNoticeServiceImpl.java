package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.entity.SrmClarifyAttachmentDownloadInfo;
import com.ylz.saas.entity.SrmClarifyNotice;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.mapper.SrmClarifyNoticeMapper;
import com.ylz.saas.req.AttachmentInfoReq;
import com.ylz.saas.req.ClarifyNoticeAddReq;
import com.ylz.saas.req.ClarifyNoticeReq;
import com.ylz.saas.resp.SrmClarifyNoticeResp;
import com.ylz.saas.service.SrmClarifyAttachmentDownloadInfoService;
import com.ylz.saas.service.SrmClarifyNoticeService;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmProjectAttachmentService;
import com.ylz.saas.service.SrmTenderNoticeService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【srm_clarify_notice(澄清公告)】的数据库操作Service实现
* @createDate 2025-07-10 14:53:22
*/
@Service
public class SrmClarifyNoticeServiceImpl extends ServiceImpl<SrmClarifyNoticeMapper, SrmClarifyNotice>
    implements SrmClarifyNoticeService{

    @Resource
    private SrmClarifyAttachmentDownloadInfoService downloadInfoService;

    @Resource
    private SrmTenderNoticeService tenderNoticeService;

    @Resource
    private SrmProcurementProjectService projectService;

    @Resource
    private SrmProjectAttachmentService projectAttachmentService;

    @Override
    public List<SrmClarifyNoticeResp> queryClarifyNotice(ClarifyNoticeReq clarifyNoticeReq) {
        List<SrmClarifyNoticeResp> clarifyNoticeResps = lambdaQuery()
                .eq(SrmClarifyNotice::getProjectId, clarifyNoticeReq.getProjectId())
                .eq(SrmClarifyNotice::getNoticeId, clarifyNoticeReq.getNoticeId())
                .eq(SrmClarifyNotice::getDelFlag, 0)
                .list()
                .stream()
                .map(entity -> {
                    SrmClarifyNoticeResp resp = new SrmClarifyNoticeResp();
                    BeanUtils.copyProperties(entity, resp);
                    return resp;
                })
                .toList();
        if(CollectionUtils.isEmpty(clarifyNoticeResps)){
            return clarifyNoticeResps;
        }
        List<Long> clarifyNoticeIds = clarifyNoticeResps.stream().map(SrmClarifyNoticeResp::getId).toList();
        List<SrmClarifyAttachmentDownloadInfo> attachmentDownloadInfos = downloadInfoService.lambdaQuery()
                .in(SrmClarifyAttachmentDownloadInfo::getClarifyNoticeId, clarifyNoticeIds).list();
        Map<Long, Long> supplierIdMapNum = attachmentDownloadInfos.stream().collect(Collectors.groupingBy(SrmClarifyAttachmentDownloadInfo::getClarifyNoticeId, Collectors.counting()));
        clarifyNoticeResps.forEach(info -> {
            Long num = supplierIdMapNum.get(info.getId());
            info.setAttachmentDownloadSupplierCount(num == null ? 0 : num.intValue());
        });
        return clarifyNoticeResps;
    }

    @Override
    public SrmClarifyNoticeResp queryClarifyDetail(Long id) {
        SrmClarifyNoticeResp resp = new SrmClarifyNoticeResp();
        SrmClarifyNotice entity = getById(id);
        ExceptionUtil.checkNonNull(entity, "澄清公告信息不存在！");
        BeanUtils.copyProperties(entity, resp);
        return resp;
    }

    @Override
    public void clarifyNoticeUpsert(ClarifyNoticeAddReq addReq) {
        Long projectId = addReq.getProjectId();
        Long noticeId = addReq.getNoticeId();
        // 项目存在
        SrmProcurementProject project = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(project, "项目不存在！");
        // 招标公告存在
        SrmTenderNotice tenderNotice = tenderNoticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(tenderNotice, "招标公告不存在！");

        // 增加澄清公告信息
        SrmClarifyNotice existOne = lambdaQuery().eq(SrmClarifyNotice::getProjectId, projectId)
                .eq(SrmClarifyNotice::getNoticeId, noticeId)
                .one();
        SrmClarifyNotice clarifyNotice = new SrmClarifyNotice();
        if(existOne != null){
            clarifyNotice.setId(existOne.getId());
        }
        clarifyNotice.setProjectId(projectId);
        clarifyNotice.setNoticeId(noticeId);
        clarifyNotice.setClarifyTitle(addReq.getClarifyTitle());
        clarifyNotice.setClarifyContent(addReq.getClarifyContent());
        clarifyNotice.setApproveStatus(ApproveStatusEnum.APPROVING);
        saveOrUpdate(clarifyNotice);
        Long clarifyNoticeId = clarifyNotice.getId();
        // 添加附件信息
        List<AttachmentInfoReq> attachmentList = addReq.getAttachmentList();
        // 附件信息
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            // 删除附件信息
            projectAttachmentService.lambdaUpdate().eq(SrmProjectAttachment::getBusinessId, clarifyNoticeId)
                    .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.CLARIFY_NOTICE_ATTACHMENT)
                    .remove();
            List<SrmProjectAttachment> attachments = attachmentList.stream().map(info -> {
                SrmProjectAttachment attachment = new SrmProjectAttachment();
                BeanUtils.copyProperties(info, attachment);
                attachment.setBusinessType(AttachmentTypeEnum.CLARIFY_NOTICE_ATTACHMENT);
                attachment.setBusinessId(clarifyNoticeId);
                return attachment;
            }).toList();
            projectAttachmentService.saveBatch(attachments);
        }
        // todo 审批流程
    }
}




