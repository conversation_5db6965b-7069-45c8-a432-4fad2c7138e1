package com.ylz.saas.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.enums.OpenStatusEnum;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.entity.SrmTenderOpen;
import com.ylz.saas.entity.SrmTenderOpenLog;
import com.ylz.saas.entity.SrmTenderRequirement;
import com.ylz.saas.entity.SrmTenderSupplierInvite;
import com.ylz.saas.entity.SrmTenderSupplierResponse;
import com.ylz.saas.enums.BidsSegmentTypeEnum;
import com.ylz.saas.enums.InviteStatusEnum;
import com.ylz.saas.enums.OperationRoleEnum;
import com.ylz.saas.enums.TenderSupplierRegisterStatusEnum;
import com.ylz.saas.mapper.SrmTenderOpenLogMapper;
import com.ylz.saas.req.SrmTenderOpenLogAddReq;
import com.ylz.saas.req.SrmTenderOpenReq;
import com.ylz.saas.resp.SrmTenderOpenLogResp;
import com.ylz.saas.service.SrmTenderNoticeService;
import com.ylz.saas.service.SrmTenderOpenLogService;
import com.ylz.saas.service.SrmTenderOpenService;
import com.ylz.saas.service.SrmTenderRequirementService;
import com.ylz.saas.service.SrmTenderSupplierInviteService;
import com.ylz.saas.service.SrmTenderSupplierResponseService;
import com.ylz.saas.util.IpUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【srm_tender_open_log(开标记录日志表)】的数据库操作Service实现
* @createDate 2025-06-17 19:14:59
*/
@Slf4j
@Service
public class SrmTenderOpenLogServiceImpl extends ServiceImpl<SrmTenderOpenLogMapper, SrmTenderOpenLog>
    implements SrmTenderOpenLogService{

    @Resource
    private SrmTenderNoticeService tenderNoticeService;

    @Resource
    private SrmTenderSupplierResponseService supplierResponseService;

    @Resource
    private SrmTenderOpenService openService;

    @Resource
    private SrmTenderSupplierInviteService srmTenderSupplierInviteService;

    @Resource
    private SrmTenderRequirementService requirementService;

    @Override
    public void operationLogAdd(SrmTenderOpenLogAddReq req) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        SrmTenderNotice tenderNotice = tenderNoticeService.lambdaQuery().eq(SrmTenderNotice::getId, noticeId)
                .eq(SrmTenderNotice::getProjectId, projectId).one();
        ExceptionUtil.checkNonNull(tenderNotice, "未找到对应的招标公告");
        // 查询开标信息
        SrmTenderOpen open = openService.lambdaQuery().eq(SrmTenderOpen::getNoticeId, noticeId)
                .eq(SrmTenderOpen::getProjectId, projectId)
                .last(" limit 1")
                .one();
        // 如果不是开标状态，则不记录
        if (open == null || open.getOpenStatus() != OpenStatusEnum.OPENED) {
            log.info("招标公告ID:{}，采购立项ID:{}，开标状态为非开标状态，不记录日志，open信息：{}", noticeId, projectId, JSONObject.toJSONString(open));
            return;
        }
        SrmTenderOpenLog openLog = new SrmTenderOpenLog();
        BeanUtils.copyProperties(req, openLog);
        // 设置ip
        String clientIP = IpUtils.getClientIP();
        openLog.setOperationIp(clientIP);
        LocalDateTime nowTime = LocalDateTime.now();
        String currentTime = nowTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        openLog.setOperationContent(req.getOperationByName() + "在" + currentTime + req.getOperationType().getDesc());
        openLog.setOperationTime(nowTime);
        OperationRoleEnum operationRoleEnum = req.getOperationRoleEnum();
        switch (operationRoleEnum) {
            case SUPPLIER:
                openLog.setTenantSupplierId(req.getOperationById());
                openLog.setSupplierName(req.getOperationByName());
                openLog.setOperationBy(String.valueOf(req.getOperationById()));
                openLog.setOperationByName(req.getOperationByName());
                break;
            case BID_OPENER:
                openLog.setOperationBy(String.valueOf(req.getOperationById()));
                openLog.setOperationByName(req.getOperationByName());
                break;
            default:
                ExceptionUtil.checkNonNull(null, "操作角色错误！");
                break;
        }
        save(openLog);
    }

    @Override
    public SrmTenderOpenLogResp operationLog(SrmTenderOpenReq req) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        SrmTenderNotice tenderNotice = tenderNoticeService.lambdaQuery().eq(SrmTenderNotice::getId, noticeId)
                .eq(SrmTenderNotice::getProjectId, projectId).one();
        ExceptionUtil.checkNonNull(tenderNotice, "未找到对应的招标公告");
        List<Long> sectionIdList = req.getSectionIdList();
        List<SrmTenderOpen> tenderOpenList = openService.lambdaQuery().eq(SrmTenderOpen::getNoticeId, noticeId)
                .in(CollectionUtils.isNotEmpty(sectionIdList), SrmTenderOpen::getSectionId, sectionIdList)
                .eq(SrmTenderOpen::getProjectId, projectId).list();
        SrmTenderOpenLogResp openLogResp = new SrmTenderOpenLogResp();
        // 开标时间
        openLogResp.setOpenTime(tenderNotice.getBidOpenTime());
        // 开标状态
        openLogResp.setOpenStatus(CollectionUtils.isEmpty(tenderOpenList) ? "未开标" : tenderOpenList.get(0).getOpenStatus().getDesc());
        // 文件交付数量
        handlerDeliverNum(req, openLogResp);
        LambdaQueryWrapper<SrmTenderOpenLog> queryWrapper = Wrappers.<SrmTenderOpenLog>lambdaQuery().eq(SrmTenderOpenLog::getNoticeId, noticeId)
                .eq(SrmTenderOpenLog::getProjectId, projectId)
                .orderByDesc(SrmTenderOpenLog::getOperationTime);
        SrmTenderOpenReq pageLast = this.page(req, queryWrapper);
        Page<SrmTenderOpenLogResp.SrmTenderOpenLogInfo> openLogInfoList = new Page<>();
        BeanUtils.copyProperties(pageLast, openLogInfoList);
        List<SrmTenderOpenLogResp.SrmTenderOpenLogInfo> openLogInfos = pageLast.getRecords().stream().map(info -> {
            SrmTenderOpenLogResp.SrmTenderOpenLogInfo openLogInfo = new SrmTenderOpenLogResp.SrmTenderOpenLogInfo();
            BeanUtils.copyProperties(info, openLogInfo);
            return openLogInfo;
        }).toList();
        openLogInfoList.setRecords(openLogInfos);
        openLogResp.setOpenLogInfoList(openLogInfoList);
        return openLogResp;
    }

    private void handlerDeliverNum(SrmTenderOpenReq req, SrmTenderOpenLogResp openLogResp) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        Long inviteSupplierCount = requirementService.lambdaQuery().eq(SrmTenderRequirement::getNoticeId, noticeId)
                .eq(SrmTenderRequirement::getRequirementType, BidsSegmentTypeEnum.INVITE_SUPPLIERS).count();
        int size;
        if(inviteSupplierCount > 0){
            // 邀请供应商，说明这个是邀请函
            List<SrmTenderSupplierInvite> inviteList = srmTenderSupplierInviteService.lambdaQuery()
                    .eq(SrmTenderSupplierInvite::getNoticeId, req.getNoticeId())
                    .eq(SrmTenderSupplierInvite::getInviteStatus, InviteStatusEnum.PASSED)
                    .list();
            Map<String, List<SrmTenderSupplierInvite>> collect = inviteList.stream().collect(
                    Collectors.groupingBy(info -> info.getNoticeId() + "" + info.getTenantSupplierId()));
            size = collect.size();
        } else {
            List<SrmTenderSupplierResponse> supplierResponseList = supplierResponseService.lambdaQuery().eq(SrmTenderSupplierResponse::getNoticeId, noticeId)
                    .eq(SrmTenderSupplierResponse::getProjectId, projectId)
                    .eq(SrmTenderSupplierResponse::getRegisterStatus, TenderSupplierRegisterStatusEnum.PASSED)
                    .list();
            Map<String, List<SrmTenderSupplierResponse>> collect = supplierResponseList.stream().collect(
                    Collectors.groupingBy(info -> info.getNoticeId() +""+ info.getTenantSupplierId()));
            size = collect.size();
        }
        openLogResp.setFileDeliveryCount(size);
    }

    @Override
    public Integer ipWarn(SrmTenderOpenReq req) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        List<Long> sectionIdList = req.getSectionIdList();
        SrmTenderNotice tenderNotice = tenderNoticeService.lambdaQuery().eq(SrmTenderNotice::getId, noticeId)
                .eq(SrmTenderNotice::getProjectId, projectId).one();
        ExceptionUtil.checkNonNull(tenderNotice, "未找到对应的招标公告");
        List<SrmTenderOpenLog> openLogs = lambdaQuery().eq(SrmTenderOpenLog::getNoticeId, noticeId)
                .eq(SrmTenderOpenLog::getProjectId, projectId)
                .in(CollectionUtils.isNotEmpty(sectionIdList), SrmTenderOpenLog::getSectionId, sectionIdList)
                .list();
        Map<String, List<SrmTenderOpenLog>> operationByIdMapList = openLogs.stream().collect(
                Collectors.groupingBy(SrmTenderOpenLog::getOperationIp));
        AtomicInteger ipWarnCount = new AtomicInteger(0);
        operationByIdMapList.forEach((operationIp, openLogList) -> {
            Set<String> operationBySet = openLogList.stream().map(SrmTenderOpenLog::getOperationBy).collect(Collectors.toSet());
            if(operationBySet.size() > 1){
                ipWarnCount.addAndGet(operationBySet.size());
            }
        });
        return ipWarnCount.get();

    }
}




