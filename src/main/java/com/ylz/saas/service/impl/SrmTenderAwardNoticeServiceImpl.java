package com.ylz.saas.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.ylz.saas.admin.api.dto.MessageSmsDTO;
import com.ylz.saas.admin.service.SysMessageService;
import com.ylz.saas.common.SmsBizCodeConstant;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmTenderAwardNotice;
import com.ylz.saas.enums.NoticeStatusEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.mapper.SrmTenderAwardNoticeMapper;
import com.ylz.saas.req.AwardedNoticeDetailReq;
import com.ylz.saas.req.SrmTenderAwardNoticeReq;
import com.ylz.saas.req.SrmTenderEditAwardNoticeReq;
import com.ylz.saas.req.SrmTenderEditBatchAwardNoticeReq;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmTenderAwardNoticeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【srm_tender_award_notice(中标通知书表)】的数据库操作Service实现
 * @createDate 2025-06-17 19:17:17
 */
@Slf4j
@Service
@AllArgsConstructor
public class SrmTenderAwardNoticeServiceImpl extends ServiceImpl<SrmTenderAwardNoticeMapper, SrmTenderAwardNotice>
        implements SrmTenderAwardNoticeService {

    private final SysMessageService sysMessageService;

    private final SrmProcurementProjectService projectService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String awardNoticeSent(SrmTenderAwardNoticeReq req) {
        List<Long> supplierIdList = req.getBidCompanyList().stream().map(
                SrmTenderAwardNoticeReq.BidCompany::getTenantSupplierId).toList();
        List<Long> sectionIdList = req.getBidCompanyList().stream().map(
                SrmTenderAwardNoticeReq.BidCompany::getSectionId).toList();
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();

        List<SrmTenderAwardNotice> awardNoticeList = this.baseMapper.getAwardNoticeList(noticeId, projectId, supplierIdList, sectionIdList);
        if (CollectionUtils.isEmpty(awardNoticeList)) {
            return "不需要发送通知书";
        }
        // 发送中标通知书
        LocalDateTime now = LocalDateTime.now();
        List<SrmTenderAwardNotice> updateList = Lists.newArrayList();
        for (SrmTenderAwardNotice notice : awardNoticeList) {
            notice.setNoticeTime(now);
            notice.setNoticeStatus(NoticeStatusEnum.SENT);
            try {
                this.sendAwardNoticeMessage(notice);
            } catch (Exception e) {
                log.error("发送中标通知书失败：{}", e.getMessage());
            }
            updateList.add(notice);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        // 发标公告下所有的通知书都已发送，则修改项目状态为询价完成
        List<SrmTenderAwardNotice> dbAwardNoticeList = lambdaQuery().eq(SrmTenderAwardNotice::getNoticeId, noticeId).list();
        boolean allMatch = dbAwardNoticeList.stream().allMatch(notice -> notice.getNoticeStatus() == NoticeStatusEnum.SENT);
        if (allMatch) {
            // 修改项目状态为询价完成
            log.info("项目下所有的中标通知书发送完成，修改项目状态开始");
            SrmProcurementProject procurementProject = projectService.getById(projectId);
            projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.COMPLETED, true);
        }

        return String.format("共有%s条通知发送成功，成功%s条，失败%s条", awardNoticeList.size(), updateList.size(), awardNoticeList.size() - updateList.size());
    }

    private void sendAwardNoticeMessage(SrmTenderAwardNotice notice) {


        LinkedHashMap<String, String> params = Maps.newLinkedHashMap();
        params.put("noticeTitle", notice.getContactPerson());
        params.put("newRound", String.valueOf(notice.getNoticeContent()));
        params.put("quoteEndTime", notice.getNoticeTime().toString());

        LinkedHashMap<String, String> testParams = Maps.newLinkedHashMap();
        testParams.put("code", "2212");
        // 发送通知短信
        MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder()
                .biz(SmsBizCodeConstant.QUOTE_AGAIN_NOTIFY)
                .build();
        messageSmsDTO.setMobiles(List.of(notice.getContactPhone()));
        messageSmsDTO.setParams(testParams);
        sysMessageService.sendSms(messageSmsDTO);


    }

    @Override
    public void awardNoticeSignature(SrmTenderAwardNoticeReq req) {
        // todo 签章中标通知书
    }

    @Override
    public void editAwardNotice(SrmTenderEditAwardNoticeReq req) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        Long sectionId = req.getSectionId();
        Long tenantSupplierId = req.getTenantSupplierId();
        SrmTenderAwardNotice awardNotice = lambdaQuery().eq(SrmTenderAwardNotice::getNoticeId, noticeId)
                .eq(SrmTenderAwardNotice::getProjectId, projectId)
                .eq(SrmTenderAwardNotice::getTenantSupplierId, tenantSupplierId)
                .eq(SrmTenderAwardNotice::getSectionId, sectionId)
                .one();
        ExceptionUtil.checkNonNull(awardNotice, "未找到对应的中标通知书信息");
        if (awardNotice.getNoticeStatus() == NoticeStatusEnum.SENT) {
            ExceptionUtil.checkNonNull(null, "该中标通知书已发送，不能编辑！");
        }
        Long awardNoticeId = awardNotice.getId();
        // 修改中标通知书信息
        lambdaUpdate().set(SrmTenderAwardNotice::getNoticeContent, req.getNoticeContent())
                .eq(SrmTenderAwardNotice::getId, awardNoticeId)
                .update();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAwardNotice(List<SrmTenderEditBatchAwardNoticeReq> req) {
        // 校验请求参数非空
        ExceptionUtil.checkNotEmpty(req, "请求参数不能为空");
        // 校验evaluationResultId不能为空
        req.forEach(item -> {
            ExceptionUtil.checkNonNull(item.getEvaluationResultId(), "evaluationResultId不能为空");
        });

        // 遍历每个请求对象，批量生成中标通知书信息
        req.forEach(item -> {
            // 如果不存在，则新建中标通知书
            SrmTenderAwardNotice newNotice = new SrmTenderAwardNotice();
            newNotice.setNoticeId(item.getNoticeId());
            newNotice.setEvaluationResultId(item.getEvaluationResultId());
            newNotice.setProjectId(item.getProjectId());
            newNotice.setSectionId(item.getSectionId());
            newNotice.setTemplateId(item.getTemplateId());
            newNotice.setTenantSupplierId(item.getTenantSupplierId());
            newNotice.setSupplierName(item.getSupplierName());
            newNotice.setNoticeContent(item.getNoticeContent());
            newNotice.setNoticeStatus(NoticeStatusEnum.UNSENT); // 默认状态为未发送
            try {
                save(newNotice);
            } catch (Exception e) {
                lambdaUpdate().set(SrmTenderAwardNotice::getNoticeContent, item.getNoticeContent())
                        .eq(SrmTenderAwardNotice::getEvaluationResultId, item.getEvaluationResultId())
                        .eq(SrmTenderAwardNotice::getSectionId, item.getSectionId())
                        .eq(SrmTenderAwardNotice::getTenantSupplierId, item.getTenantSupplierId())
                        .eq(SrmTenderAwardNotice::getProjectId, item.getProjectId())
                        .eq(SrmTenderAwardNotice::getNoticeId, item.getNoticeId())
                        .update();
                log.error("中标通知书新增，处理异常，newNotice：{}", JSONObject.toJSONString(newNotice));
            }
        });
    }

    @Override
    public SrmTenderAwardNotice awardNoticeDetail(AwardedNoticeDetailReq req) {
        Long noticeId = req.getNoticeId();
        Long sectionId = req.getSectionId();
        Long tenantSupplierId = req.getTenantSupplierId();
        return lambdaQuery().eq(SrmTenderAwardNotice::getNoticeId, noticeId)
                .eq(SrmTenderAwardNotice::getSectionId, sectionId)
                .eq(SrmTenderAwardNotice::getTenantSupplierId, tenantSupplierId)
                .eq(SrmTenderAwardNotice::getNoticeStatus, NoticeStatusEnum.SENT)
                .last(" limit 1")
                .one();
    }
}




