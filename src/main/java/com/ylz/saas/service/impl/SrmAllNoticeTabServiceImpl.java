package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.service.SysDeptService;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmProjectMember;
import com.ylz.saas.entity.SrmTenderEvaluationResult;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.PublicityStatusEnum;
import com.ylz.saas.req.SrmAllNoticePageReq;
import com.ylz.saas.resp.SrmAllInvitePageResp;
import com.ylz.saas.resp.SrmAllNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicityPageResp;
import com.ylz.saas.service.SrmAllNoticeTabService;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmProjectAttachmentService;
import com.ylz.saas.service.SrmProjectMemberService;
import com.ylz.saas.service.SrmTenderEvaluationResultService;
import com.ylz.saas.service.SrmTenderNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 统一公告
 *
 * 性能优化建议：
 * 1. 数据库索引优化：
 *    - srm_tender_notice: (del_flag, status, create_time)
 *    - srm_procurement_project: (invite_method, del_flag)
 *    - srm_project_member: (project_id, del_flag, user_id)
 *    - sys_user: (user_id, name)
 *
 * 2. 查询优化：
 *    - 批量查询项目成员，避免N+1问题
 *    - 在数据库层面过滤PUBLICITY类型项目
 *    - 使用缓存减少重复查询
 */
@Service
@RequiredArgsConstructor
public class SrmAllNoticeTabServiceImpl implements SrmAllNoticeTabService {

    private final SrmTenderNoticeService srmTenderNoticeService;
    private final SrmProcurementProjectService srmProcurementProjectService;
    private final SrmTenderEvaluationResultService srmTenderEvaluationResultService;
    private final SrmProjectAttachmentService srmProjectAttachmentService;
    private final SrmProjectMemberService srmProjectMemberService;
    private final SysUserService sysUserService;
    private final SysDeptService sysDeptService;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public Page<SrmAllNoticePageResp> tenderNoticePage(SrmAllNoticePageReq req) {
        // 构建招标公告查询条件
        LambdaQueryWrapper<SrmTenderNotice> noticeWrapper = new LambdaQueryWrapper<>();
        noticeWrapper.eq(SrmTenderNotice::getDelFlag, 0);

        // 公告标题搜索
        if (req.getNoticeTitle() != null && !req.getNoticeTitle().isEmpty()) {
            noticeWrapper.like(SrmTenderNotice::getNoticeTitle, req.getNoticeTitle());
        }

        // 发布时间范围
        if (req.getPublishStartTime() != null) {
            noticeWrapper.ge(SrmTenderNotice::getPublishNoticeStartTime, req.getPublishStartTime());
        }
        if (req.getPublishEndTime() != null) {
            noticeWrapper.le(SrmTenderNotice::getPublishNoticeStartTime, req.getPublishEndTime());
        }

        // 排序：按发布时间倒序
        noticeWrapper.orderByDesc(SrmTenderNotice::getPublishNoticeStartTime);

        // 查询所有符合条件的公告
        List<SrmTenderNotice> allNotices = srmTenderNoticeService.list(noticeWrapper);

        if (CollectionUtils.isEmpty(allNotices)) {
            return createEmptyPage(req);
        }

        // 批量查询项目信息，并在数据库层面过滤PUBLICITY
        Map<Long, SrmProcurementProject> projectMap = getProjectMapWithPublicityFilter(allNotices);

        // 过滤出有效项目的公告（项目必须存在且为PUBLICITY）
        List<SrmTenderNotice> publicityNotices = allNotices.stream()
                .filter(notice -> projectMap.containsKey(notice.getProjectId()))
                .collect(Collectors.toList());

        // 应用其他过滤条件
        List<SrmTenderNotice> filteredNotices = filterNoticesByProject(publicityNotices, projectMap, req);

        // 手动分页并返回结果
        return createPageFromFilteredData(filteredNotices, req, projectMap, this::convertToNoticePageResp);
    }

    @Override
    public Page<SrmAllInvitePageResp> invitePage(SrmAllNoticePageReq req) {
        // 构建邀请函查询条件（只查询邀请方式的招标公告）
        LambdaQueryWrapper<SrmTenderNotice> noticeWrapper = new LambdaQueryWrapper<>();
        noticeWrapper.eq(SrmTenderNotice::getDelFlag, 0);

        // 公告标题搜索
        if (req.getNoticeTitle() != null && !req.getNoticeTitle().isEmpty()) {
            noticeWrapper.like(SrmTenderNotice::getNoticeTitle, req.getNoticeTitle());
        }

        // 发布时间范围
        if (req.getPublishStartTime() != null) {
            noticeWrapper.ge(SrmTenderNotice::getPublishNoticeStartTime, req.getPublishStartTime());
        }
        if (req.getPublishEndTime() != null) {
            noticeWrapper.le(SrmTenderNotice::getPublishNoticeStartTime, req.getPublishEndTime());
        }

        // 排序：按发布时间倒序
        noticeWrapper.orderByDesc(SrmTenderNotice::getPublishNoticeStartTime);

        // 查询所有符合条件的公告
        List<SrmTenderNotice> allNotices = srmTenderNoticeService.list(noticeWrapper);

        if (CollectionUtils.isEmpty(allNotices)) {
            return createEmptyPage(req);
        }

        // 批量查询项目信息
        Map<Long, SrmProcurementProject> projectMap = getProjectMap(allNotices);

        // 过滤数据（包括邀请方式过滤）
        List<SrmTenderNotice> filteredNotices = filterNoticesByProject(allNotices, projectMap, req)
                .stream()
                .filter(notice -> {
                    SrmProcurementProject project = projectMap.get(notice.getProjectId());
                    return project != null && InviteMethodEnum.INVITE.equals(project.getInviteMethod());
                })
                .collect(Collectors.toList());

        // 手动分页
        return createPageFromFilteredData(filteredNotices, req, projectMap, this::convertToInvitePageResp);
    }

    @Override
    public Page<SrmAllPublicityPageResp> publicityPage(SrmAllNoticePageReq req) {
        // 构建评标结果查询条件（只查询有公示状态的）
        LambdaQueryWrapper<SrmTenderEvaluationResult> evaluationWrapper = new LambdaQueryWrapper<>();
        evaluationWrapper.eq(SrmTenderEvaluationResult::getDelFlag, 0)
                .isNotNull(SrmTenderEvaluationResult::getPublicityStatus);

        // 发布时间范围（使用公示开始时间）
        if (req.getPublishStartTime() != null) {
            evaluationWrapper.ge(SrmTenderEvaluationResult::getPublicityStartTime, req.getPublishStartTime());
        }
        if (req.getPublishEndTime() != null) {
            evaluationWrapper.le(SrmTenderEvaluationResult::getPublicityStartTime, req.getPublishEndTime());
        }

        // 排序：按公示开始时间倒序
        evaluationWrapper.orderByDesc(SrmTenderEvaluationResult::getPublicityStartTime);

        // 查询所有符合条件的评标结果
        List<SrmTenderEvaluationResult> allResults = srmTenderEvaluationResultService.list(evaluationWrapper);

        if (CollectionUtils.isEmpty(allResults)) {
            return createEmptyPage(req);
        }

        // 批量查询项目和公告信息
        Map<Long, SrmProcurementProject> projectMap = getProjectMapFromEvaluationResults(allResults);
        Map<Long, SrmTenderNotice> noticeMap = getNoticeMapFromEvaluationResults(allResults);

        // 过滤数据
        List<SrmTenderEvaluationResult> filteredResults = filterEvaluationResultsByProject(allResults, projectMap, noticeMap, req);

        // 手动分页
        return createPageFromFilteredData(filteredResults, req, projectMap, this::convertToPublicityPageResp);
    }

    @Override
    public Page<SrmAllPublicNoticePageResp> publicNoticePage(SrmAllNoticePageReq req) {
        // 构建评标结果查询条件（只查询已发布公告的）
        LambdaQueryWrapper<SrmTenderEvaluationResult> evaluationWrapper = new LambdaQueryWrapper<>();
        evaluationWrapper.eq(SrmTenderEvaluationResult::getDelFlag, 0)
                .eq(SrmTenderEvaluationResult::getPublicityStatus, PublicityStatusEnum.PUBLISHED);

        // 发布时间范围（使用公告时间）
        if (req.getPublishStartTime() != null) {
            evaluationWrapper.ge(SrmTenderEvaluationResult::getNoticeTime, req.getPublishStartTime());
        }
        if (req.getPublishEndTime() != null) {
            evaluationWrapper.le(SrmTenderEvaluationResult::getNoticeTime, req.getPublishEndTime());
        }

        // 排序：按公告时间倒序
        evaluationWrapper.orderByDesc(SrmTenderEvaluationResult::getNoticeTime);

        // 查询所有符合条件的评标结果
        List<SrmTenderEvaluationResult> allResults = srmTenderEvaluationResultService.list(evaluationWrapper);

        if (CollectionUtils.isEmpty(allResults)) {
            return createEmptyPage(req);
        }

        // 批量查询项目和公告信息
        Map<Long, SrmProcurementProject> projectMap = getProjectMapFromEvaluationResults(allResults);
        Map<Long, SrmTenderNotice> noticeMap = getNoticeMapFromEvaluationResults(allResults);

        // 过滤数据
        List<SrmTenderEvaluationResult> filteredResults = filterEvaluationResultsByProject(allResults, projectMap, noticeMap, req);

        // 手动分页（使用不同的附件类型）
        return createPublicNoticePageFromFilteredData(filteredResults, req, projectMap, noticeMap, this::convertToPublicNoticePageResp);
    }
