package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.mapper.SrmAllNoticeTabMapper;
import com.ylz.saas.req.SrmAllNoticePageReq;
import com.ylz.saas.resp.SrmAllInvitePageResp;
import com.ylz.saas.resp.SrmAllNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicityPageResp;
import com.ylz.saas.service.SrmAllNoticeTabService;
import com.ylz.saas.service.SrmProjectAttachmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 统一公告服务实现类
 * 通过XML文件实现复杂的SQL查询逻辑
 */
@Service
@RequiredArgsConstructor
public class SrmAllNoticeTabServiceImpl implements SrmAllNoticeTabService {

    private final SrmAllNoticeTabMapper srmAllNoticeTabMapper;
    private final SrmProjectAttachmentService srmProjectAttachmentService;
    private final member srmProjectAttachmentService;

    @Override
    public Page<SrmAllNoticePageResp> tenderNoticePage(SrmAllNoticePageReq req) {
        // 创建分页对象
        Page<SrmAllNoticePageResp> page = new Page<>(req.getCurrent(), req.getSize());

        // 通过XML SQL查询招标公告分页数据
        Page<SrmAllNoticePageResp> result = srmAllNoticeTabMapper.selectTenderNoticePage(page, req);

        // 批量加载附件信息
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            loadAttachments(result.getRecords(), AttachmentTypeEnum.NOTICE);
        }

        return result;
    }

    @Override
    public Page<SrmAllInvitePageResp> invitePage(SrmAllNoticePageReq req) {
        // 创建分页对象
        Page<SrmAllInvitePageResp> page = new Page<>(req.getCurrent(), req.getSize());

        // 通过XML SQL查询邀请函分页数据
        Page<SrmAllInvitePageResp> result = srmAllNoticeTabMapper.selectInvitePage(page, req);

        // 批量加载附件信息
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            loadAttachments(result.getRecords(), AttachmentTypeEnum.NOTICE);
        }

        return result;
    }

    @Override
    public Page<SrmAllPublicityPageResp> publicityPage(SrmAllNoticePageReq req) {
        // 创建分页对象
        Page<SrmAllPublicityPageResp> page = new Page<>(req.getCurrent(), req.getSize());

        // 通过XML SQL查询公示分页数据
        Page<SrmAllPublicityPageResp> result = srmAllNoticeTabMapper.selectPublicityPage(page, req);

        // 批量加载附件信息
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            loadAttachments(result.getRecords(), AttachmentTypeEnum.PUBLICITY_ATTACHMENT);
        }

        return result;
    }

    @Override
    public Page<SrmAllPublicNoticePageResp> publicNoticePage(SrmAllNoticePageReq req) {
        // 创建分页对象
        Page<SrmAllPublicNoticePageResp> page = new Page<>(req.getCurrent(), req.getSize());

        // 通过XML SQL查询公告分页数据
        Page<SrmAllPublicNoticePageResp> result = srmAllNoticeTabMapper.selectPublicNoticePage(page, req);

        // 批量加载附件信息
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            loadAttachments(result.getRecords(), AttachmentTypeEnum.NOTICE);
        }

        return result;
    }

    /**
     * 批量加载附件信息
     * @param records 记录列表
     * @param attachmentType 附件类型
     */
    private <T> void loadAttachments(List<T> records, AttachmentTypeEnum attachmentType) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        try {
            // 收集所有需要查询附件的业务ID
            List<Long> businessIds = new ArrayList<>();

            for (T record : records) {
                Long businessId = getBusinessId(record);
                if (businessId != null) {
                    businessIds.add(businessId);
                }
            }

            if (CollectionUtils.isEmpty(businessIds)) {
                return;
            }

            // 批量查询附件信息
            List<SrmProjectAttachment> attachments = srmProjectAttachmentService.lambdaQuery()
                    .eq(SrmProjectAttachment::getDelFlag, 0)
                    .eq(SrmProjectAttachment::getBusinessType, attachmentType)
                    .in(SrmProjectAttachment::getBusinessId, businessIds)
                    .list();

            if (CollectionUtils.isEmpty(attachments)) {
                return;
            }

            // 按业务ID分组
            Map<Long, List<SrmProjectAttachment>> attachmentMap = attachments.stream()
                    .collect(Collectors.groupingBy(SrmProjectAttachment::getBusinessId));

            // 设置附件信息到对应的记录中
            for (T record : records) {
                Long businessId = getBusinessId(record);
                if (businessId != null) {
                    List<SrmProjectAttachment> recordAttachments = attachmentMap.get(businessId);
                    setAttachmentList(record, recordAttachments != null ? recordAttachments : new ArrayList<>());
                }
            }
        } catch (Exception e) {
            // 附件加载失败不影响主要数据的返回，只记录日志
            // log.warn("批量加载附件信息失败", e);
        }
    }

    /**
     * 获取业务ID
     */
    private <T> Long getBusinessId(T record) {
        if (record instanceof SrmAllNoticePageResp) {
            return ((SrmAllNoticePageResp) record).getId();
        } else if (record instanceof SrmAllInvitePageResp) {
            return ((SrmAllInvitePageResp) record).getId();
        } else if (record instanceof SrmAllPublicityPageResp) {
            return ((SrmAllPublicityPageResp) record).getEvaluationResultId();
        } else if (record instanceof SrmAllPublicNoticePageResp) {
            return ((SrmAllPublicNoticePageResp) record).getId();
        }
        return null;
    }

    /**
     * 设置附件列表
     */
    private <T> void setAttachmentList(T record, List<SrmProjectAttachment> attachments) {
        if (record instanceof SrmAllNoticePageResp) {
            ((SrmAllNoticePageResp) record).setAttachmentList(attachments);
        } else if (record instanceof SrmAllInvitePageResp) {
            ((SrmAllInvitePageResp) record).setAttachmentList(attachments);
        } else if (record instanceof SrmAllPublicityPageResp) {
            ((SrmAllPublicityPageResp) record).setAttachmentList(attachments);
        } else if (record instanceof SrmAllPublicNoticePageResp) {
            ((SrmAllPublicNoticePageResp) record).setAttachmentList(attachments);
        }
    }
}
