package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.service.SysDeptService;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmProjectMember;
import com.ylz.saas.entity.SrmTenderEvaluationResult;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.PublicityStatusEnum;
import com.ylz.saas.req.SrmAllNoticePageReq;
import com.ylz.saas.resp.SrmAllInvitePageResp;
import com.ylz.saas.resp.SrmAllNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicityPageResp;
import com.ylz.saas.service.SrmAllNoticeTabService;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmProjectAttachmentService;
import com.ylz.saas.service.SrmProjectMemberService;
import com.ylz.saas.service.SrmTenderEvaluationResultService;
import com.ylz.saas.service.SrmTenderNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 统一公告
 *
 * 性能优化建议：
 * 1. 数据库索引优化：
 *    - srm_tender_notice: (del_flag, status, create_time)
 *    - srm_procurement_project: (invite_method, del_flag)
 *    - srm_project_member: (project_id, del_flag, user_id)
 *    - sys_user: (user_id, name)
 *
 * 2. 查询优化：
 *    - 批量查询项目成员，避免N+1问题
 *    - 在数据库层面过滤PUBLICITY类型项目
 *    - 使用缓存减少重复查询
 */
@Service
@RequiredArgsConstructor
public class SrmAllNoticeTabServiceImpl implements SrmAllNoticeTabService {

    private final SrmTenderNoticeService srmTenderNoticeService;
    private final SrmProcurementProjectService srmProcurementProjectService;
    private final SrmTenderEvaluationResultService srmTenderEvaluationResultService;
    private final SrmProjectAttachmentService srmProjectAttachmentService;
    private final SrmProjectMemberService srmProjectMemberService;
    private final SysUserService sysUserService;
    private final SysDeptService sysDeptService;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public Page<SrmAllNoticePageResp> tenderNoticePage(SrmAllNoticePageReq req) {
        // 由于需要过滤publicCategory为PUBLICITY的数据，必须关联项目表查询
        // 这种情况下总是需要复杂查询处理
        return getTenderNoticePageForPublicityOnly(req);
    }

    /**
     * 获取招标公告分页数据（只查询publicCategory为PUBLICITY的数据）
     */
    private Page<SrmAllNoticePageResp> getTenderNoticePageForPublicityOnly(SrmAllNoticePageReq req) {
        // 第一步：构建基础查询条件
        LambdaQueryWrapper<SrmTenderNotice> noticeWrapper = new LambdaQueryWrapper<>();
        noticeWrapper.eq(SrmTenderNotice::getDelFlag, 0);

        // 公告标题搜索
        if (req.getNoticeTitle() != null && !req.getNoticeTitle().isEmpty()) {
            noticeWrapper.like(SrmTenderNotice::getNoticeTitle, req.getNoticeTitle());
        }

        // 发布时间范围
        if (req.getPublishStartTime() != null) {
            noticeWrapper.ge(SrmTenderNotice::getPublishNoticeStartTime, req.getPublishStartTime());
        }
        if (req.getPublishEndTime() != null) {
            noticeWrapper.le(SrmTenderNotice::getPublishNoticeStartTime, req.getPublishEndTime());
        }

        // 开标时间范围
        if (req.getBidOpenStartTime() != null) {
            noticeWrapper.ge(SrmTenderNotice::getBidOpenTime, req.getBidOpenStartTime());
        }
        if (req.getBidOpenEndTime() != null) {
            noticeWrapper.le(SrmTenderNotice::getBidOpenTime, req.getBidOpenEndTime());
        }

        noticeWrapper.orderByDesc(SrmTenderNotice::getCreateTime);

        // 第二步：查询所有符合基础条件的公告
        List<SrmTenderNotice> allNotices = srmTenderNoticeService.list(noticeWrapper);

        if (CollectionUtils.isEmpty(allNotices)) {
            return createEmptyPage(req);
        }

        // 第三步：批量查询项目信息，并在数据库层面过滤PUBLICITY
        Map<Long, SrmProcurementProject> projectMap = getProjectMapWithPublicityFilter(allNotices);

        // 第四步：过滤出有效项目的公告（项目必须存在且为PUBLICITY）
        List<SrmTenderNotice> publicityNotices = allNotices.stream()
                .filter(notice -> projectMap.containsKey(notice.getProjectId()))
                .collect(Collectors.toList());

        // 第五步：应用其他过滤条件
        List<SrmTenderNotice> filteredNotices = filterNoticesByProject(publicityNotices, projectMap, req);

        // 第六步：手动分页并返回结果
        return createPageFromFilteredData(filteredNotices, req, projectMap, this::convertToNoticePageResp);
    }



    /**
     * 判断是否为简单查询（不需要关联项目表过滤）
     * 注意：招标公告查询由于需要过滤PUBLICITY，总是需要复杂查询
     */
    private boolean isSimpleQuery(SrmAllNoticePageReq req) {
        return (req.getSearchContent() == null || req.getSearchContent().isEmpty()) &&
                (req.getProjectLeaderName() == null || req.getProjectLeaderName().isEmpty()) &&
                CollectionUtils.isEmpty(req.getSourcingType());
    }

    /**
     * 创建空分页结果
     */
    private <T> Page<T> createEmptyPage(SrmAllNoticePageReq req) {
        Page<T> result = new Page<>();
        result.setCurrent(req.getCurrent());
        result.setSize(req.getSize());
        result.setTotal(0);
        result.setPages(0);
        result.setRecords(new ArrayList<>());
        return result;
    }

    /**
     * 获取项目信息映射
     */
    private Map<Long, SrmProcurementProject> getProjectMap(List<SrmTenderNotice> notices) {
        List<Long> projectIds = notices.stream()
                .map(SrmTenderNotice::getProjectId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        return projectIds.isEmpty() ?
                Map.of() :
                srmProcurementProjectService.listByIds(projectIds).stream()
                        .collect(Collectors.toMap(SrmProcurementProject::getId, p -> p));
    }

    /**
     * 获取项目信息映射（只返回PUBLICITY类型的项目）
     */
    private Map<Long, SrmProcurementProject> getProjectMapWithPublicityFilter(List<SrmTenderNotice> notices) {
        List<Long> projectIds = notices.stream()
                .map(SrmTenderNotice::getProjectId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (projectIds.isEmpty()) {
            return Map.of();
        }

        // 在数据库层面过滤PUBLICITY项目
        return srmProcurementProjectService.lambdaQuery()
                .in(SrmProcurementProject::getId, projectIds)
                .eq(SrmProcurementProject::getInviteMethod, InviteMethodEnum.PUBLICITY)
                .eq(SrmProcurementProject::getDelFlag, 0)
                .list()
                .stream()
                .collect(Collectors.toMap(SrmProcurementProject::getId, p -> p));
    }

    /**
     * 从过滤后的数据创建分页结果
     */
    private <T, R> Page<R> createPageFromFilteredData(List<T> filteredData,
                                                      SrmAllNoticePageReq req,
                                                      Map<Long, SrmProcurementProject> projectMap,
                                                      DataConverter<T, R> converter) {
        // 计算分页
        int total = filteredData.size();
        int current = Math.toIntExact(req.getCurrent());
        int size = Math.toIntExact(req.getSize());
        int offset = (current - 1) * size;

        // 获取当前页数据
        List<T> pageData;
        if (offset >= total) {
            pageData = new ArrayList<>();
        } else {
            int toIndex = Math.min(offset + size, total);
            pageData = filteredData.subList(offset, toIndex);
        }

        // 转换数据
        List<R> respList = converter.convert(pageData, projectMap);

        // 批量查询附件
        loadAttachments(respList, AttachmentTypeEnum.NOTICE);

        // 构建分页结果
        Page<R> result = new Page<>();
        result.setCurrent(req.getCurrent());
        result.setSize(req.getSize());
        result.setTotal(total);
        result.setPages((long) Math.ceil((double) total / size));
        result.setRecords(respList);

        return result;
    }

    /**
     * 数据转换器接口
     */
    @FunctionalInterface
    private interface DataConverter<T, R> {
        List<R> convert(List<T> data, Map<Long, SrmProcurementProject> projectMap);
    }

    /**
     * 转换简单查询结果
     */
    private <T> Page<T> convertToPageResult(Page<SrmTenderNotice> noticeResult,
                                            SrmAllNoticePageReq req,
                                            DataConverter<SrmTenderNotice, T> converter) {
        Page<T> result = new Page<>();
        result.setCurrent(noticeResult.getCurrent());
        result.setSize(noticeResult.getSize());
        result.setTotal(noticeResult.getTotal());
        result.setPages(noticeResult.getPages());

        if (CollectionUtils.isEmpty(noticeResult.getRecords())) {
            result.setRecords(new ArrayList<>());
            return result;
        }

        Map<Long, SrmProcurementProject> projectMap = getProjectMap(noticeResult.getRecords());
        List<T> respList = converter.convert(noticeResult.getRecords(), projectMap);
        loadAttachments(respList, AttachmentTypeEnum.NOTICE);

        result.setRecords(respList);
        return result;
    }

    /**
     * 根据项目条件过滤公告
     */
    private List<SrmTenderNotice> filterNoticesByProject(List<SrmTenderNotice> notices,
                                                         Map<Long, SrmProcurementProject> projectMap,
                                                         SrmAllNoticePageReq req) {
        // 如果有项目负责人搜索条件，预先批量查询项目成员信息
        Map<Long, Boolean> projectMemberMatchCache = new HashMap<>();
        if (req.getProjectLeaderName() != null && !req.getProjectLeaderName().isEmpty()) {
            projectMemberMatchCache = batchCheckProjectMembersByName(
                projectMap.keySet(), req.getProjectLeaderName());
        }

        // 使用final变量以便在lambda中使用
        final Map<Long, Boolean> memberMatchCache = projectMemberMatchCache;

        return notices.stream().filter(notice -> {
            SrmProcurementProject project = projectMap.get(notice.getProjectId());
            if (project == null) {
                return false;
            }

            // 项目编号/名称搜索
            if (req.getSearchContent() != null && !req.getSearchContent().isEmpty()) {
                String searchContent = req.getSearchContent().toLowerCase();
                boolean matchProject = (project.getProjectCode() != null && project.getProjectCode().toLowerCase().contains(searchContent)) ||
                        (project.getProjectName() != null && project.getProjectName().toLowerCase().contains(searchContent));
                if (!matchProject) {
                    return false;
                }
            }

            // 寻源方式筛选
            if (!CollectionUtils.isEmpty(req.getSourcingType())) {
                if (!req.getSourcingType().contains(project.getSourcingType())) {
                    return false;
                }
            }

            // 项目负责人搜索（性能优化）
            if (req.getProjectLeaderName() != null && !req.getProjectLeaderName().isEmpty()) {
                boolean matchLeader = project.getProjectLeaderName() != null &&
                        project.getProjectLeaderName().contains(req.getProjectLeaderName());

                // 如果项目表中没有匹配，使用预查询的缓存结果
                if (!matchLeader) {
                    matchLeader = memberMatchCache.getOrDefault(project.getId(), false);
                }

                if (!matchLeader) {
                    return false;
                }
            }

            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 批量检查项目成员中是否有匹配的用户名（性能优化）
     */
    private Map<Long, Boolean> batchCheckProjectMembersByName(Set<Long> projectIds, String userName) {
        if (CollectionUtils.isEmpty(projectIds) || userName == null || userName.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询所有项目的成员
        List<SrmProjectMember> allMembers = srmProjectMemberService.lambdaQuery()
                .in(SrmProjectMember::getProjectId, projectIds)
                .eq(SrmProjectMember::getDelFlag, 0)
                .list();

        if (CollectionUtils.isEmpty(allMembers)) {
            return projectIds.stream().collect(Collectors.toMap(id -> id, id -> false));
        }

        // 获取所有用户ID
        List<Long> userIds = allMembers.stream()
                .map(SrmProjectMember::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (userIds.isEmpty()) {
            return projectIds.stream().collect(Collectors.toMap(id -> id, id -> false));
        }

        // 批量查询匹配的用户
        List<SysUser> matchedUsers = sysUserService.lambdaQuery()
                .in(SysUser::getUserId, userIds)
                .like(SysUser::getName, userName)
                .list();

        if (CollectionUtils.isEmpty(matchedUsers)) {
            return projectIds.stream().collect(Collectors.toMap(id -> id, id -> false));
        }

        // 构建匹配用户ID集合
        Set<Long> matchedUserIds = matchedUsers.stream()
                .map(SysUser::getUserId)
                .collect(Collectors.toSet());

        // 按项目分组成员
        Map<Long, List<SrmProjectMember>> membersByProject = allMembers.stream()
                .collect(Collectors.groupingBy(SrmProjectMember::getProjectId));

        // 构建结果映射
        Map<Long, Boolean> result = new HashMap<>();
        for (Long projectId : projectIds) {
            List<SrmProjectMember> projectMembers = membersByProject.getOrDefault(projectId, List.of());
            boolean hasMatch = projectMembers.stream()
                    .map(SrmProjectMember::getUserId)
                    .filter(Objects::nonNull)
                    .anyMatch(matchedUserIds::contains);
            result.put(projectId, hasMatch);
        }

        return result;
    }

    /**
     * 检查项目成员中是否有匹配的用户名（保留原方法作为备用）
     */
    private boolean checkProjectMemberByName(Long projectId, String userName) {
        List<SrmProjectMember> members = srmProjectMemberService.lambdaQuery()
                .eq(SrmProjectMember::getProjectId, projectId)
                .eq(SrmProjectMember::getDelFlag, 0)
                .list();

        if (CollectionUtils.isEmpty(members)) {
            return false;
        }

        List<Long> userIds = members.stream()
                .map(SrmProjectMember::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (userIds.isEmpty()) {
            return false;
        }

        return sysUserService.lambdaQuery()
                .in(SysUser::getUserId, userIds)
                .like(SysUser::getName, userName)
                .exists();
    }

    /**
     * 转换为招标公告响应对象
     */
    private List<SrmAllNoticePageResp> convertToNoticePageResp(List<SrmTenderNotice> notices,
                                                               Map<Long, SrmProcurementProject> projectMap) {
        return notices.stream().map(notice -> {
            SrmAllNoticePageResp resp = new SrmAllNoticePageResp();
            resp.setId(notice.getId());
            resp.setNoticeTitle(notice.getNoticeTitle());

            SrmProcurementProject project = projectMap.get(notice.getProjectId());
            if (project != null) {
                resp.setProjectId(project.getId());
                resp.setProjectCode(project.getProjectCode());
                resp.setProjectName(project.getProjectName());
                resp.setSourcingMethod(project.getSourcingType());
                resp.setPublicCategory(project.getInviteMethod());
            }

            resp.setNoticeAuthor(notice.getCreateByName());
            resp.setPublisher(notice.getCreateByName());

            if (notice.getPublishNoticeStartTime() != null) {
                resp.setPublishTime(notice.getPublishNoticeStartTime().format(DATE_TIME_FORMATTER));
            }
            if (notice.getRegisterStartTime() != null) {
                resp.setRegistrationStartTime(notice.getRegisterStartTime().format(DATE_TIME_FORMATTER));
            }
            if (notice.getRegisterEndTime() != null) {
                resp.setRegistrationEndTime(notice.getRegisterEndTime().format(DATE_TIME_FORMATTER));
            }
            if (notice.getBidOpenTime() != null) {
                resp.setBidOpeningTime(notice.getBidOpenTime().format(DATE_TIME_FORMATTER));
            }

            resp.setContactPerson(notice.getContactPerson());
            resp.setContactPhone(notice.getContactPhone());

            return resp;
        }).collect(Collectors.toList());
    }


    /**
     * 批量加载附件信息
     */
    private void loadAttachments(List<? extends Object> respList, AttachmentTypeEnum attachmentType) {
        if (CollectionUtils.isEmpty(respList)) {
            return;
        }

        List<Long> businessIds = respList.stream()
                .map(resp -> {
                    if (resp instanceof SrmAllNoticePageResp) {
                        return ((SrmAllNoticePageResp) resp).getId();
                    } else if (resp instanceof SrmAllInvitePageResp) {
                        return ((SrmAllInvitePageResp) resp).getId();
                    } else if (resp instanceof SrmAllPublicityPageResp) {
                        return ((SrmAllPublicityPageResp) resp).getEvaluationResultId();
                    } else if (resp instanceof SrmAllPublicNoticePageResp) {
                        return ((SrmAllPublicNoticePageResp) resp).getId();
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (businessIds.isEmpty()) {
            return;
        }

        List<SrmProjectAttachment> attachments = srmProjectAttachmentService.lambdaQuery()
                .in(SrmProjectAttachment::getBusinessId, businessIds)
                .eq(SrmProjectAttachment::getBusinessType, attachmentType)
                .eq(SrmProjectAttachment::getDelFlag, 0)
                .list();

        Map<Long, List<SrmProjectAttachment>> attachmentMap = attachments.stream()
                .collect(Collectors.groupingBy(SrmProjectAttachment::getBusinessId));

        // 设置附件信息
        respList.forEach(resp -> {
            Long businessId = null;
            if (resp instanceof SrmAllNoticePageResp) {
                businessId = ((SrmAllNoticePageResp) resp).getId();
                ((SrmAllNoticePageResp) resp).setAttachmentList(attachmentMap.getOrDefault(businessId, List.of()));
            } else if (resp instanceof SrmAllInvitePageResp) {
                businessId = ((SrmAllInvitePageResp) resp).getId();
                ((SrmAllInvitePageResp) resp).setAttachmentList(attachmentMap.getOrDefault(businessId, List.of()));
            } else if (resp instanceof SrmAllPublicityPageResp) {
                businessId = ((SrmAllPublicityPageResp) resp).getEvaluationResultId();
                ((SrmAllPublicityPageResp) resp).setAttachmentList(attachmentMap.getOrDefault(businessId, List.of()));
            } else if (resp instanceof SrmAllPublicNoticePageResp) {
                businessId = ((SrmAllPublicNoticePageResp) resp).getId();
                ((SrmAllPublicNoticePageResp) resp).setAttachmentList(attachmentMap.getOrDefault(businessId, List.of()));
            }
        });
    }

    @Override
    public Page<SrmAllInvitePageResp> invitePage(SrmAllNoticePageReq req) {
        // 构建邀请函查询条件（只查询邀请方式的招标公告）
        LambdaQueryWrapper<SrmTenderNotice> noticeWrapper = new LambdaQueryWrapper<>();
        noticeWrapper.eq(SrmTenderNotice::getDelFlag, 0);

        // 公告标题搜索
        if (req.getNoticeTitle() != null && !req.getNoticeTitle().isEmpty()) {
            noticeWrapper.like(SrmTenderNotice::getNoticeTitle, req.getNoticeTitle());
        }

        // 发布时间范围
        if (req.getPublishStartTime() != null) {
            noticeWrapper.ge(SrmTenderNotice::getPublishNoticeStartTime, req.getPublishStartTime());
        }
        if (req.getPublishEndTime() != null) {
            noticeWrapper.le(SrmTenderNotice::getPublishNoticeStartTime, req.getPublishEndTime());
        }

        // 开标时间范围
        if (req.getBidOpenStartTime() != null) {
            noticeWrapper.ge(SrmTenderNotice::getBidOpenTime, req.getBidOpenStartTime());
        }
        if (req.getBidOpenEndTime() != null) {
            noticeWrapper.le(SrmTenderNotice::getBidOpenTime, req.getBidOpenEndTime());
        }

        noticeWrapper.orderByDesc(SrmTenderNotice::getCreateTime);

        return getInvitePageWithCorrectPagination(req, noticeWrapper);
    }

    /**
     * 获取邀请函分页数据（正确处理分页信息）
     */
    private Page<SrmAllInvitePageResp> getInvitePageWithCorrectPagination(SrmAllNoticePageReq req,
                                                                          LambdaQueryWrapper<SrmTenderNotice> noticeWrapper) {
        // 邀请函查询总是需要过滤邀请方式，所以查询所有数据
        List<SrmTenderNotice> allNotices = srmTenderNoticeService.list(noticeWrapper);

        if (CollectionUtils.isEmpty(allNotices)) {
            return createEmptyPage(req);
        }

        // 批量查询项目信息
        Map<Long, SrmProcurementProject> projectMap = getProjectMap(allNotices);

        // 过滤数据（包括邀请方式过滤）
        List<SrmTenderNotice> filteredNotices = filterNoticesByProject(allNotices, projectMap, req)
                .stream()
                .filter(notice -> {
                    SrmProcurementProject project = projectMap.get(notice.getProjectId());
                    return project != null && InviteMethodEnum.INVITE.equals(project.getInviteMethod());
                })
                .collect(Collectors.toList());

        // 手动分页
        return createPageFromFilteredData(filteredNotices, req, projectMap, this::convertToInvitePageResp);
    }

    /**
     * 转换为邀请函响应对象
     */
    private List<SrmAllInvitePageResp> convertToInvitePageResp(List<SrmTenderNotice> notices,
                                                               Map<Long, SrmProcurementProject> projectMap) {
        return notices.stream().map(notice -> {
            SrmAllInvitePageResp resp = new SrmAllInvitePageResp();
            resp.setId(notice.getId());
            resp.setNoticeTitle(notice.getNoticeTitle());

            SrmProcurementProject project = projectMap.get(notice.getProjectId());
            if (project != null) {
                resp.setProjectCode(project.getProjectCode());
                resp.setProjectName(project.getProjectName());
                resp.setSourcingMethod(project.getSourcingType());
                resp.setPublicCategory(project.getInviteMethod());
            }

            resp.setNoticeAuthor(notice.getCreateByName());
            resp.setPublisher(notice.getCreateByName());

            if (notice.getPublishNoticeStartTime() != null) {
                resp.setPublishTime(notice.getPublishNoticeStartTime().format(DATE_TIME_FORMATTER));
            }
            if (notice.getRegisterStartTime() != null) {
                resp.setRegistrationStartTime(notice.getRegisterStartTime().format(DATE_TIME_FORMATTER));
            }
            if (notice.getRegisterEndTime() != null) {
                resp.setRegistrationEndTime(notice.getRegisterEndTime().format(DATE_TIME_FORMATTER));
            }
            if (notice.getBidOpenTime() != null) {
                resp.setBidOpeningTime(notice.getBidOpenTime().format(DATE_TIME_FORMATTER));
            }

            resp.setContactPerson(notice.getContactPerson());
            resp.setContactPhone(notice.getContactPhone());

            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public Page<SrmAllPublicityPageResp> publicityPage(SrmAllNoticePageReq req) {
        // 构建评标结果查询条件（只查询有公示状态的）
        LambdaQueryWrapper<SrmTenderEvaluationResult> evaluationWrapper = new LambdaQueryWrapper<>();
        evaluationWrapper.eq(SrmTenderEvaluationResult::getDelFlag, 0)
                .isNotNull(SrmTenderEvaluationResult::getPublicityStatus);

        // 发布时间范围（使用公示开始时间）
        if (req.getPublishStartTime() != null) {
            evaluationWrapper.ge(SrmTenderEvaluationResult::getPublicityStartTime, req.getPublishStartTime());
        }
        if (req.getPublishEndTime() != null) {
            evaluationWrapper.le(SrmTenderEvaluationResult::getPublicityStartTime, req.getPublishEndTime());
        }

        evaluationWrapper.orderByDesc(SrmTenderEvaluationResult::getCreateTime);

        return getPublicityPageWithCorrectPagination(req, evaluationWrapper);
    }

    /**
     * 获取公示分页数据（正确处理分页信息）
     */
    private Page<SrmAllPublicityPageResp> getPublicityPageWithCorrectPagination(SrmAllNoticePageReq req,
                                                                                LambdaQueryWrapper<SrmTenderEvaluationResult> evaluationWrapper) {
        // 如果没有复杂的过滤条件，直接使用数据库分页
        if (isSimpleQuery(req)) {
            Page<SrmTenderEvaluationResult> evaluationPage = new Page<>(req.getCurrent(), req.getSize());
            Page<SrmTenderEvaluationResult> evaluationResult = srmTenderEvaluationResultService.page(evaluationPage, evaluationWrapper);

            return convertToEvaluationPageResult(evaluationResult, req, this::convertToPublicityPageResp);
        }

        // 有复杂过滤条件时，查询所有数据进行过滤后分页
        List<SrmTenderEvaluationResult> allResults = srmTenderEvaluationResultService.list(evaluationWrapper);

        if (CollectionUtils.isEmpty(allResults)) {
            return createEmptyPage(req);
        }

        // 批量查询项目和公告信息
        Map<Long, SrmProcurementProject> projectMap = getProjectMapFromEvaluationResults(allResults);
        Map<Long, SrmTenderNotice> noticeMap = getNoticeMapFromEvaluationResults(allResults);

        // 过滤数据
        List<SrmTenderEvaluationResult> filteredResults = filterEvaluationResultsByProject(allResults, projectMap, noticeMap, req);

        // 手动分页
        return createEvaluationPageFromFilteredData(filteredResults, req, projectMap, noticeMap, this::convertToPublicityPageResp);
    }

    /**
     * 从评标结果获取项目信息映射
     */
    private Map<Long, SrmProcurementProject> getProjectMapFromEvaluationResults(List<SrmTenderEvaluationResult> results) {
        List<Long> projectIds = results.stream()
                .map(SrmTenderEvaluationResult::getProjectId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        return projectIds.isEmpty() ?
                Map.of() :
                srmProcurementProjectService.listByIds(projectIds).stream()
                        .collect(Collectors.toMap(SrmProcurementProject::getId, p -> p));
    }

    /**
     * 从评标结果获取公告信息映射
     */
    private Map<Long, SrmTenderNotice> getNoticeMapFromEvaluationResults(List<SrmTenderEvaluationResult> results) {
        List<Long> noticeIds = results.stream()
                .map(SrmTenderEvaluationResult::getNoticeId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        return noticeIds.isEmpty() ?
                Map.of() :
                srmTenderNoticeService.listByIds(noticeIds).stream()
                        .collect(Collectors.toMap(SrmTenderNotice::getId, n -> n));
    }

    /**
     * 评标结果数据转换器接口
     */
    @FunctionalInterface
    private interface EvaluationDataConverter<R> {
        List<R> convert(List<SrmTenderEvaluationResult> data,
                        Map<Long, SrmProcurementProject> projectMap,
                        Map<Long, SrmTenderNotice> noticeMap);
    }

    /**
     * 从过滤后的评标结果数据创建分页结果
     */
    private <R> Page<R> createEvaluationPageFromFilteredData(List<SrmTenderEvaluationResult> filteredData,
                                                             SrmAllNoticePageReq req,
                                                             Map<Long, SrmProcurementProject> projectMap,
                                                             Map<Long, SrmTenderNotice> noticeMap,
                                                             EvaluationDataConverter<R> converter) {
        // 计算分页
        int total = filteredData.size();
        int current = Math.toIntExact(req.getCurrent());
        int size = Math.toIntExact(req.getSize());
        int offset = (current - 1) * size;

        // 获取当前页数据
        List<SrmTenderEvaluationResult> pageData;
        if (offset >= total) {
            pageData = new ArrayList<>();
        } else {
            int toIndex = Math.min(offset + size, total);
            pageData = filteredData.subList(offset, toIndex);
        }

        // 转换数据
        List<R> respList = converter.convert(pageData, projectMap, noticeMap);

        // 批量查询附件
        loadAttachments(respList, AttachmentTypeEnum.PUBLICITY_ATTACHMENT);

        // 构建分页结果
        Page<R> result = new Page<>();
        result.setCurrent(req.getCurrent());
        result.setSize(req.getSize());
        result.setTotal(total);
        result.setPages((long) Math.ceil((double) total / size));
        result.setRecords(respList);

        return result;
    }

    /**
     * 转换简单评标结果查询结果
     */
    private <T> Page<T> convertToEvaluationPageResult(Page<SrmTenderEvaluationResult> evaluationResult,
                                                      SrmAllNoticePageReq req,
                                                      EvaluationDataConverter<T> converter) {
        Page<T> result = new Page<>();
        result.setCurrent(evaluationResult.getCurrent());
        result.setSize(evaluationResult.getSize());
        result.setTotal(evaluationResult.getTotal());
        result.setPages(evaluationResult.getPages());

        if (CollectionUtils.isEmpty(evaluationResult.getRecords())) {
            result.setRecords(new ArrayList<>());
            return result;
        }

        Map<Long, SrmProcurementProject> projectMap = getProjectMapFromEvaluationResults(evaluationResult.getRecords());
        Map<Long, SrmTenderNotice> noticeMap = getNoticeMapFromEvaluationResults(evaluationResult.getRecords());
        List<T> respList = converter.convert(evaluationResult.getRecords(), projectMap, noticeMap);
        loadAttachments(respList, AttachmentTypeEnum.PUBLICITY_ATTACHMENT);

        result.setRecords(respList);
        return result;
    }

    /**
     * 根据项目条件过滤评标结果
     */
    private List<SrmTenderEvaluationResult> filterEvaluationResultsByProject(
            List<SrmTenderEvaluationResult> results,
            Map<Long, SrmProcurementProject> projectMap,
            Map<Long, SrmTenderNotice> noticeMap,
            SrmAllNoticePageReq req) {

        // 如果有项目负责人搜索条件，预先批量查询项目成员信息
        Map<Long, Boolean> projectMemberMatchCache = new HashMap<>();
        if (req.getProjectLeaderName() != null && !req.getProjectLeaderName().isEmpty()) {
            projectMemberMatchCache = batchCheckProjectMembersByName(
                projectMap.keySet(), req.getProjectLeaderName());
        }

        // 使用final变量以便在lambda中使用
        final Map<Long, Boolean> memberMatchCache = projectMemberMatchCache;

        return results.stream().filter(result -> {
            SrmProcurementProject project = projectMap.get(result.getProjectId());
            SrmTenderNotice notice = noticeMap.get(result.getNoticeId());

            if (project == null) {
                return false;
            }

            // 项目编号/名称搜索
            if (req.getSearchContent() != null && !req.getSearchContent().isEmpty()) {
                String searchContent = req.getSearchContent().toLowerCase();
                boolean matchProject = (project.getProjectCode() != null && project.getProjectCode().toLowerCase().contains(searchContent)) ||
                        (project.getProjectName() != null && project.getProjectName().toLowerCase().contains(searchContent));
                if (!matchProject) {
                    return false;
                }
            }

            // 公告标题搜索
            if (req.getNoticeTitle() != null && !req.getNoticeTitle().isEmpty()) {
                boolean matchTitle = (result.getPublicityTitle() != null && result.getPublicityTitle().contains(req.getNoticeTitle())) ||
                        (notice != null && notice.getNoticeTitle() != null && notice.getNoticeTitle().contains(req.getNoticeTitle()));
                if (!matchTitle) {
                    return false;
                }
            }

            // 寻源方式筛选
            if (!CollectionUtils.isEmpty(req.getSourcingType())) {
                if (!req.getSourcingType().contains(project.getSourcingType())) {
                    return false;
                }
            }

            // 项目负责人搜索（性能优化）
            if (req.getProjectLeaderName() != null && !req.getProjectLeaderName().isEmpty()) {
                boolean matchLeader = project.getProjectLeaderName() != null &&
                        project.getProjectLeaderName().contains(req.getProjectLeaderName());

                // 如果项目表中没有匹配，使用预查询的缓存结果
                if (!matchLeader) {
                    matchLeader = memberMatchCache.getOrDefault(project.getId(), false);
                }

                if (!matchLeader) {
                    return false;
                }
            }

            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 转换为公示响应对象
     */
    private List<SrmAllPublicityPageResp> convertToPublicityPageResp(
            List<SrmTenderEvaluationResult> results,
            Map<Long, SrmProcurementProject> projectMap,
            Map<Long, SrmTenderNotice> noticeMap) {
        return results.stream().map(result -> {
            SrmAllPublicityPageResp resp = new SrmAllPublicityPageResp();
            resp.setNoticeTitle(result.getPublicityTitle());
            resp.setEvaluationResultId(result.getId());

            SrmProcurementProject project = projectMap.get(result.getProjectId());
            if (project != null) {
                resp.setProjectCode(project.getProjectCode());
                resp.setProjectName(project.getProjectName());
                resp.setSourcingMethod(project.getSourcingType());
                resp.setPublicCategory(project.getInviteMethod());
            }

            resp.setNoticeAuthor(result.getCreateByName());
            resp.setPublisher(result.getCreateByName());

            if (result.getPublicityStartTime() != null) {
                resp.setPublishTime(result.getPublicityStartTime().format(DATE_TIME_FORMATTER));
            }

            resp.setPublicityStartTime(result.getPublicityStartTime());
            resp.setPublicityEndTime(result.getPublicityEndTime());
            resp.setPublicityTitle(result.getPublicityTitle());
            resp.setPublicityContent(result.getPublicityContent());
            resp.setAwardedAmountTotal(result.getAwardedAmountTotal());
            resp.setTenantSupplierId(result.getTenantSupplierId());

            // 设置公示状态描述
            if (result.getPublicityStatus() != null) {
                resp.setPublicityStatus(result.getPublicityStatus());
                resp.setNoticeStatus(result.getNoticeStatus());
            }

            // 设置审核状态描述
            if (result.getPublicityAuditStatus() != null) {
                resp.setPublicityAuditStatus(result.getPublicityAuditStatus());
            }

            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public Page<SrmAllPublicNoticePageResp> publicNoticePage(SrmAllNoticePageReq req) {
        // 构建评标结果查询条件（只查询已发布公告的）
        LambdaQueryWrapper<SrmTenderEvaluationResult> evaluationWrapper = new LambdaQueryWrapper<>();
        evaluationWrapper.eq(SrmTenderEvaluationResult::getDelFlag, 0)
                .eq(SrmTenderEvaluationResult::getPublicityStatus, PublicityStatusEnum.PUBLISHED);

        // 发布时间范围（使用公告时间）
        if (req.getPublishStartTime() != null) {
            evaluationWrapper.ge(SrmTenderEvaluationResult::getNoticeTime, req.getPublishStartTime());
        }
        if (req.getPublishEndTime() != null) {
            evaluationWrapper.le(SrmTenderEvaluationResult::getNoticeTime, req.getPublishEndTime());
        }

        evaluationWrapper.orderByDesc(SrmTenderEvaluationResult::getCreateTime);

        return getPublicNoticePageWithCorrectPagination(req, evaluationWrapper);
    }

    /**
     * 获取公告分页数据（正确处理分页信息）
     */
    private Page<SrmAllPublicNoticePageResp> getPublicNoticePageWithCorrectPagination(SrmAllNoticePageReq req,
                                                                                      LambdaQueryWrapper<SrmTenderEvaluationResult> evaluationWrapper) {
        // 如果没有复杂的过滤条件，直接使用数据库分页
        if (isSimpleQuery(req)) {
            Page<SrmTenderEvaluationResult> evaluationPage = new Page<>(req.getCurrent(), req.getSize());
            Page<SrmTenderEvaluationResult> evaluationResult = srmTenderEvaluationResultService.page(evaluationPage, evaluationWrapper);

            return convertToEvaluationPageResult(evaluationResult, req, this::convertToPublicNoticePageResp);
        }

        // 有复杂过滤条件时，查询所有数据进行过滤后分页
        List<SrmTenderEvaluationResult> allResults = srmTenderEvaluationResultService.list(evaluationWrapper);

        if (CollectionUtils.isEmpty(allResults)) {
            return createEmptyPage(req);
        }

        // 批量查询项目和公告信息
        Map<Long, SrmProcurementProject> projectMap = getProjectMapFromEvaluationResults(allResults);
        Map<Long, SrmTenderNotice> noticeMap = getNoticeMapFromEvaluationResults(allResults);

        // 过滤数据
        List<SrmTenderEvaluationResult> filteredResults = filterEvaluationResultsByProject(allResults, projectMap, noticeMap, req);

        // 手动分页（使用不同的附件类型）
        return createPublicNoticePageFromFilteredData(filteredResults, req, projectMap, noticeMap, this::convertToPublicNoticePageResp);
    }

    /**
     * 从过滤后的公告数据创建分页结果（使用公告附件类型）
     */
    private Page<SrmAllPublicNoticePageResp> createPublicNoticePageFromFilteredData(List<SrmTenderEvaluationResult> filteredData,
                                                                                    SrmAllNoticePageReq req,
                                                                                    Map<Long, SrmProcurementProject> projectMap,
                                                                                    Map<Long, SrmTenderNotice> noticeMap,
                                                                                    EvaluationDataConverter<SrmAllPublicNoticePageResp> converter) {
        // 计算分页
        int total = filteredData.size();
        int current = Math.toIntExact(req.getCurrent());
        int size = Math.toIntExact(req.getSize());
        int offset = (current - 1) * size;

        // 获取当前页数据
        List<SrmTenderEvaluationResult> pageData;
        if (offset >= total) {
            pageData = new ArrayList<>();
        } else {
            int toIndex = Math.min(offset + size, total);
            pageData = filteredData.subList(offset, toIndex);
        }

        // 转换数据
        List<SrmAllPublicNoticePageResp> respList = converter.convert(pageData, projectMap, noticeMap);

        // 批量查询附件（使用公告附件类型）
        loadAttachments(respList, AttachmentTypeEnum.NOTICE_ATTACHMENT);

        // 构建分页结果
        Page<SrmAllPublicNoticePageResp> result = new Page<>();
        result.setCurrent(req.getCurrent());
        result.setSize(req.getSize());
        result.setTotal(total);
        result.setPages((long) Math.ceil((double) total / size));
        result.setRecords(respList);

        return result;
    }

    /**
     * 转换为公告响应对象
     */
    private List<SrmAllPublicNoticePageResp> convertToPublicNoticePageResp(
            List<SrmTenderEvaluationResult> results,
            Map<Long, SrmProcurementProject> projectMap,
            Map<Long, SrmTenderNotice> noticeMap) {
        return results.stream().map(result -> {
            SrmAllPublicNoticePageResp resp = new SrmAllPublicNoticePageResp();
            resp.setId(result.getId());
            resp.setNoticeTitle(result.getNoticeTitle());

            SrmProcurementProject project = projectMap.get(result.getProjectId());
            if (project != null) {
                resp.setProjectCode(project.getProjectCode());
                resp.setProjectName(project.getProjectName());
                resp.setSourcingMethod(project.getSourcingType());
                resp.setPublicCategory(project.getInviteMethod());
            }

            resp.setNoticeAuthor(result.getCreateByName());
            resp.setPublisher(result.getCreateByName());

            if (result.getNoticeTime() != null) {
                resp.setPublishTime(result.getNoticeTime().format(DATE_TIME_FORMATTER));
            }

            return resp;
        }).collect(Collectors.toList());
    }

    /**
     * 性能监控方法（用于调试和优化）
     */
    private void logPerformanceMetrics(String operation, long startTime, int dataSize) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 如果查询时间超过1秒，记录警告日志
        if (duration > 1000) {
            System.out.println(String.format(
                "性能警告: %s操作耗时%dms，数据量：%d条",
                operation, duration, dataSize));
        }

        // 如果查询时间超过3秒，记录错误日志
        if (duration > 3000) {
            System.err.println(String.format(
                "性能严重警告: %s操作耗时%dms，数据量：%d条，建议优化查询逻辑",
                operation, duration, dataSize));
        }
    }

    /**
     * 带性能监控的招标公告查询
     */
    public Page<SrmAllNoticePageResp> tenderNoticePageWithMonitoring(SrmAllNoticePageReq req) {
        long startTime = System.currentTimeMillis();

        try {
            Page<SrmAllNoticePageResp> result = tenderNoticePage(req);
            logPerformanceMetrics("招标公告查询", startTime, result.getRecords().size());
            return result;
        } catch (Exception e) {
            logPerformanceMetrics("招标公告查询(异常)", startTime, 0);
            throw e;
        }
    }
}
