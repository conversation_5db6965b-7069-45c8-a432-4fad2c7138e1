package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.GlobalResultCode;
import com.ylz.saas.entity.*;
import com.ylz.saas.enums.*;
import com.ylz.saas.mapper.SrmTenderBidEvaluationMapper;
import com.ylz.saas.mapper.SrmTenderBidEvaluationScoringMapper;
import com.ylz.saas.req.SrmTenderBidEvaluationReq;
import com.ylz.saas.req.SrmTenderEvaluationSummaryReq;
import com.ylz.saas.req.SrmTenderSectionPageReq;
import com.ylz.saas.req.SrmTenderEvaluationSummaryQueryReq;
import com.ylz.saas.req.SrmTenderOfflineEvaluationSummaryQueryReq;
import com.ylz.saas.req.SrmTenderOfflineEvaluationSummaryReq;
import com.ylz.saas.req.SrmTenderSupplierReviewDetailQueryReq;
import com.ylz.saas.req.SrmTenderLeaderSummaryReviewReq;
import com.ylz.saas.resp.SrmTenderBidEvaluationResp;
import com.ylz.saas.resp.SrmTenderSectionPageResp;
import com.ylz.saas.resp.SrmTenderEvaluationSummaryQueryResp;
import com.ylz.saas.resp.SrmTenderEvaluationSummarySupplierResp;
import com.ylz.saas.resp.SrmTenderOfflineEvaluationSummaryQueryResp;
import com.ylz.saas.resp.SrmTenderOfflineEvaluationSummarySupplierResp;
import com.ylz.saas.resp.SrmTenderSupplierReviewDetailQueryResp;
import com.ylz.saas.dto.SectionSignatureProgressDto;
import com.ylz.saas.dto.NodeScoreDto;
import com.ylz.saas.service.*;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 针对表【srm_tender_bid_evaluation(评标委员会)】的数据库操作Service实现
 * <AUTHOR>
 * @createDate 2025-07-09
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmTenderBidEvaluationServiceImpl extends ServiceImpl<SrmTenderBidEvaluationMapper, SrmTenderBidEvaluation>
        implements SrmTenderBidEvaluationService {


    private final SrmProjectMemberService srmProjectMemberService;
    private final SrmTenderSupplierResponseService srmTenderSupplierResponseService;
    private final SrmTenderBidEvaluationScoringService srmTenderBidEvaluationScoringService;
    private final SrmTenderNoticeService srmTenderNoticeService;
    private final SrmTenderEvaluationSignatureService srmTenderEvaluationSignatureService;
    private final SrmProcurementProjectService srmProcurementProjectService;
    private final SrmTenderBidEvaluationScoringMapper srmTenderBidEvaluationScoringMapper;
    private final SrmTenderOpenService srmTenderOpenService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBidEvaluation(SrmTenderBidEvaluationReq req) {
        // 检查是否已存在相同的评标委员会记录
        long existingCount = lambdaQuery()
                .eq(SrmTenderBidEvaluation::getProjectId, req.getProjectId())
                .eq(SrmTenderBidEvaluation::getNoticeId, req.getNoticeId())
                .eq(SrmTenderBidEvaluation::getSectionId, req.getSectionId())
                .eq(SrmTenderBidEvaluation::getDelFlag, 0)
                .count();

        ExceptionUtil.check(existingCount > 0, "500", "该项目标段的评标委员会已存在，不允许重复创建");

        Long noticeId = req.getNoticeId();
        SrmTenderNotice byId = srmTenderNoticeService.getById(noticeId);
        ExceptionUtil.check(byId == null, "500", "招标公告不存在");
        // 保存评标委员会
        SrmTenderBidEvaluation entity = new SrmTenderBidEvaluation();
        BeanUtil.copyProperties(req, entity);
        entity.setDelFlag(0);
        entity.setSummaryStatus(EvaluationSummaryStatusEnum.PENDING);
        entity.setTenderWay(byId.getTenderWay());
        boolean saveResult = save(entity);

        if (saveResult && CollectionUtil.isNotEmpty(req.getMemberList())) {
            // 保存评标委员会成员
            saveMemberList(req.getProjectId(), entity.getId(), req.getMemberList());
        }

        return saveResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBidEvaluation(SrmTenderBidEvaluationReq req) {
        // 更新评标委员会
        SrmTenderBidEvaluation entity = new SrmTenderBidEvaluation();
        BeanUtil.copyProperties(req, entity);
        boolean updateResult = updateById(entity);

        if (updateResult) {
            // 重新保存成员列表
            if (CollectionUtil.isNotEmpty(req.getMemberList())) {
                saveMemberList(entity.getProjectId(), entity.getId(), req.getMemberList());
            }
        }

        return updateResult;
    }


    @Override
    public SrmTenderBidEvaluationResp getBySectionId(Long sectionId) {
        // 使用XML SQL联查获取评标委员会及其成员详细信息
        return baseMapper.getBySectionIdWithMembers(sectionId);
    }



    /**
     * 保存评标委员会成员列表
     * @param bidEvaluationId 招标公告ID
     * @param memberList 成员列表
     */
    private void saveMemberList(Long projectId, Long bidEvaluationId, List<SrmTenderBidEvaluationReq.EvaluationMember> memberList) {
        List<SrmProjectMember> existsMemberList = srmProjectMemberService.lambdaQuery()
                .eq(SrmProjectMember::getProjectId, projectId)
                .list();

        srmProjectMemberService.lambdaUpdate()
                .eq(SrmProjectMember::getBusinessId, bidEvaluationId)
                .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.EVALUATION_MEMBER)
                .remove();
        List<SrmProjectMember> projectMembers = memberList.stream()
                .map(member -> {
                    SrmProjectMember projectMember = new SrmProjectMember();
                    projectMember.setProjectId(projectId);
                    projectMember.setBusinessId(bidEvaluationId);
                    projectMember.setUserId(member.getUserId());
                    projectMember.setMemberType(ProjectMemberTypeEnum.EVALUATION_MEMBER);
                    projectMember.setRole(member.getRole());
                    projectMember.setContactPhone(member.getContactPhone());
                    projectMember.setDelFlag(0);
                    return projectMember;
                })
                .collect(Collectors.toList());

        srmProjectMemberService.saveBatch(projectMembers);

        Set<Long> newUserIds = memberList.stream().map(SrmTenderBidEvaluationReq.EvaluationMember::getUserId).collect(Collectors.toSet());
        Set<Long> otherTypesUserIds = existsMemberList.stream()
                .filter(item -> !Objects.equals(ProjectMemberTypeEnum.EVALUATION_MEMBER, item.getMemberType()))
                .map(SrmProjectMember::getUserId).collect(Collectors.toSet());
        newUserIds.addAll(otherTypesUserIds);
        Set<Long> existsUserIds = existsMemberList.stream().map(SrmProjectMember::getUserId).collect(Collectors.toSet());
        srmProcurementProjectService.insertProjectUserIdentityDataPermission(srmProcurementProjectService.getById(projectId), newUserIds, existsUserIds);
    }

    @Override
    public IPage<SrmTenderSectionPageResp> pageSections(SrmTenderSectionPageReq req) {
        // 创建分页对象
        Page<SrmTenderSectionPageResp> page = new Page<>(req.getCurrent(), req.getSize());

        // 调用Mapper方法进行分页查询
        IPage<SrmTenderSectionPageResp> result = baseMapper.pageSections(page, req);

        // 批量查询签名进度
        if (!result.getRecords().isEmpty()) {
            List<Long> sectionIds = result.getRecords().stream()
                    .map(SrmTenderSectionPageResp::getSectionId)
                    .collect(Collectors.toList());

            List<SectionSignatureProgressDto> progressList =
                    baseMapper.batchGetSignatureProgress(sectionIds);

            Map<Long, String> progressMap = progressList.stream()
                    .collect(Collectors.toMap(
                            SectionSignatureProgressDto::getSectionId,
                            SectionSignatureProgressDto::getProgressText
                    ));

            // 获取当前用户ID
            Long currentUserId = null;
            try {
                currentUserId = SecurityUtils.getUser().getId();
            } catch (Exception e) {
                log.warn("获取当前用户信息失败", e);
            }

            // 设置签名进度、当前用户签名状态和操作权限
            final Long userId = currentUserId;
            result.getRecords().forEach(resp -> {
                resp.setSignatureProgress(progressMap.getOrDefault(resp.getSectionId(), "0/0"));

                // 设置当前用户是否已签名
                if (userId != null) {
                    boolean hasUserSigned = srmTenderEvaluationSignatureService.hasUserSigned(resp.getSectionId(), userId);
                    resp.setHasCurrentUserSigned(hasUserSigned);
                } else {
                    resp.setHasCurrentUserSigned(false);
                }

                setOperationPermissions(resp);
            });
        }

        return result;
    }

    /**
     * 设置操作权限
     * @param resp 响应对象
     */
    private void setOperationPermissions(SrmTenderSectionPageResp resp) {
        ExpertExtractionStatusEnum extractionStatus = resp.getExpertExtractionStatus();
        EvaluationSummaryStatusEnum summaryStatus = resp.getSummaryStatus();

        // 设置操作权限
        resp.setCanExtract(extractionStatus == ExpertExtractionStatusEnum.PENDING);
        resp.setCanViewDetail(extractionStatus == ExpertExtractionStatusEnum.EXTRACTED);
        resp.setCanReExtract(extractionStatus == ExpertExtractionStatusEnum.EXTRACTED);

        // 设置状态描述
        resp.setExpertExtractionStatusDesc(extractionStatus != null ? extractionStatus.getDesc() : "");
        resp.setSummaryStatusDesc(summaryStatus != null ? summaryStatus.getDesc() : "待汇总");

        // 如果汇总状态为空，设置默认值
        if (summaryStatus == null) {
            resp.setSummaryStatus(EvaluationSummaryStatusEnum.PENDING);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean summaryEvaluation(SrmTenderEvaluationSummaryReq req) {
        // 更新评标委员会的汇总信息
        SrmTenderBidEvaluation evaluation = getById(req.getEvaluationId());
        if (evaluation == null) {
            log.error("评标委员会不存在，ID: {}", req.getEvaluationId());
            return false;
        }

        evaluation.setSummaryStatus(EvaluationSummaryStatusEnum.SUMMARIZED);
        evaluation.setSummaryTime(LocalDateTime.now());
        evaluation.setReportContent(req.getReportContent());
        evaluation.setReportTemplateId(req.getReportTemplateId());
        evaluation.setSignedReportContent(req.getSignedReportContent());

        // 设置汇总人信息
        try {
            evaluation.setSummaryBy(SecurityUtils.getUser().getUsername());
            evaluation.setSummaryByName(SecurityUtils.getUser().getName());
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认值: {}", e.getMessage());
            evaluation.setSummaryBy("system");
            evaluation.setSummaryByName("系统");
        }

        // 更新评标委员会信息
        boolean updateResult = updateById(evaluation);
        if (!updateResult) {
            log.error("更新评标委员会信息失败，ID: {}", req.getEvaluationId());
            return false;
        }
        this.updateProjectProgressStatus(evaluation.getProjectId(), evaluation.getNoticeId());

        // 更新供应商中标候选人信息
        if (req.getSupplierWinnerInfoList() != null && !req.getSupplierWinnerInfoList().isEmpty()) {
            boolean updateSupplierResult = updateSupplierWinnerInfo(req.getSectionId(), req.getSupplierWinnerInfoList());
            if (!updateSupplierResult) {
                log.error("更新供应商中标候选人信息失败，标段ID: {}", req.getSectionId());
                return false;
            }
        }

        return true;
    }

    /**
     * 更新供应商中标候选人信息
     * @param sectionId 标段ID
     * @param supplierWinnerInfoList 供应商中标候选人信息列表
     * @return 是否成功
     */
    private boolean updateSupplierWinnerInfo(Long sectionId, List<SrmTenderEvaluationSummaryReq.SupplierWinnerInfo> supplierWinnerInfoList) {
        try {
            // 批量更新供应商中标候选人信息
            for (SrmTenderEvaluationSummaryReq.SupplierWinnerInfo winnerInfo : supplierWinnerInfoList) {
                boolean updateResult = srmTenderSupplierResponseService.lambdaUpdate()
                        .set(SrmTenderSupplierResponse::getIsRecommendedWinner, winnerInfo.getIsRecommendedWinner())
                        .set(SrmTenderSupplierResponse::getWinnerCandidateOrder, winnerInfo.getWinnerCandidateOrder())
                        .eq(SrmTenderSupplierResponse::getSectionId, sectionId)
                        .eq(SrmTenderSupplierResponse::getTenantSupplierId, winnerInfo.getTenantSupplierId())
                        .eq(SrmTenderSupplierResponse::getDelFlag, 0)
                        .update();

                if (!updateResult) {
                    log.error("更新供应商中标候选人信息失败，标段ID: {}, 供应商ID: {}",
                            sectionId, winnerInfo.getTenantSupplierId());
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("更新供应商中标候选人信息异常，标段ID: {}", sectionId, e);
            return false;
        }
    }


    private void updateProjectProgressStatus(long projectId, long noticeId){
        boolean existsPending = this.lambdaQuery().eq(SrmTenderBidEvaluation::getProjectId, projectId)
                .eq(SrmTenderBidEvaluation::getNoticeId, noticeId)
                .eq(SrmTenderBidEvaluation::getSummaryStatus, EvaluationSummaryStatusEnum.PENDING)
                .exists();
        if (!existsPending) {
            SrmProcurementProject project = srmProcurementProjectService.getById(projectId);
            ExceptionUtil.checkNonNull(project, "项目不存在");
            srmProcurementProjectService.updateProgressStatus(project.getProjectCode(), ProjectProgressStatusEnum.EVALUATED, true);
        }
    }


    @Override
    public SrmTenderEvaluationSummaryQueryResp queryEvaluationSummary(SrmTenderEvaluationSummaryQueryReq req) {
        SrmTenderEvaluationSummaryQueryResp resp = new SrmTenderEvaluationSummaryQueryResp();

        // 1. 查询标段下所有供应商基本信息
        List<SrmTenderEvaluationSummarySupplierResp> supplierList =
                baseMapper.querySupplierBasicInfo(req.getSectionId(), req.getNoticeId(), req.getProjectId());

        // 2. 查询评标节点列表
        List<String> evaluationNodes =
                baseMapper.queryEvaluationNodes(req.getSectionId(), req.getNoticeId(), req.getProjectId());

        // 3. 为每个供应商填充详细信息
        for (SrmTenderEvaluationSummarySupplierResp supplier : supplierList) {
            // 3.1 查询评审项汇总
            String reviewSummary = baseMapper.querySupplierReviewSummary(
                    req.getSectionId(), req.getNoticeId(), req.getProjectId(), supplier.getTenantSupplierId());
            supplier.setReviewSummary(reviewSummary);

            // 3.2 查询各节点评分项分数
            List<NodeScoreDto> nodeScoreList = baseMapper.querySupplierNodeScores(
                    req.getSectionId(), req.getNoticeId(), req.getProjectId(), supplier.getTenantSupplierId());

            Map<String, Integer> nodeScores = new HashMap<>();
            int totalScore = 0;
            for (NodeScoreDto nodeScore : nodeScoreList) {
                String nodeName = nodeScore.getNodeName();
                Integer score = nodeScore.getTotalScore();
                nodeScores.put(nodeName + "（评分项）", score);
                totalScore += score;
            }
            supplier.setNodeScores(nodeScores);
            supplier.setTotalScore(totalScore);

            // 3.3 查询投标价格
            BigDecimal bidPrice = baseMapper.querySupplierBidPrice(
                    req.getSectionId(), req.getNoticeId(), req.getProjectId(), supplier.getTenantSupplierId());
            supplier.setBidPrice(bidPrice);

            // 3.4 设置中标候选顺序描述
            if (supplier.getWinnerCandidateOrder() != null) {
                supplier.setWinnerCandidateOrderDesc(supplier.getWinnerCandidateOrder().getDesc());
            }
        }

        // 4. 计算排名（根据评审项汇总和总分进行排序）
        supplierList.sort((s1, s2) -> {
            // 首先按评审项汇总排序（符合的排在前面）
            int reviewCompare = "符合".equals(s2.getReviewSummary()) ?
                    ("符合".equals(s1.getReviewSummary()) ? 0 : 1) :
                    ("符合".equals(s1.getReviewSummary()) ? -1 : 0);
            if (reviewCompare != 0) {
                return reviewCompare;
            }
            // 然后按总分降序排序
            return s2.getTotalScore().compareTo(s1.getTotalScore());
        });

        // 设置排名
        for (int i = 0; i < supplierList.size(); i++) {
            supplierList.get(i).setRanking(i + 1);
        }

        resp.setSupplierList(supplierList);
        resp.setEvaluationNodes(evaluationNodes);

        // 5. 查询评标报告内容和汇总信息
        SrmTenderBidEvaluationResp evaluation = getBySectionId(req.getSectionId());
        if (evaluation != null) {
            resp.setReportContent(evaluation.getReportContent());
            resp.setReportTemplateId(evaluation.getReportTemplateId());
            resp.setSummaryStatus(evaluation.getSummaryStatus());
            resp.setSummaryBy(evaluation.getSummaryBy());
            resp.setSummaryByName(evaluation.getSummaryByName());
            resp.setSummaryTime(evaluation.getSummaryTime());
            resp.setEvaluationId(evaluation.getId());
        }

        return resp;
    }

    @Override
    public SrmTenderOfflineEvaluationSummaryQueryResp queryOfflineEvaluationSummary(SrmTenderOfflineEvaluationSummaryQueryReq req) {
        SrmTenderOfflineEvaluationSummaryQueryResp resp = new SrmTenderOfflineEvaluationSummaryQueryResp();

        SrmTenderOpen tenderOpen = srmTenderOpenService.lambdaQuery()
                .eq(SrmTenderOpen::getProjectId, req.getProjectId())
                .eq(SrmTenderOpen::getNoticeId, req.getNoticeId())
                .eq(SrmTenderOpen::getSectionId, req.getSectionId()).one();
        ExceptionUtil.check(tenderOpen == null || tenderOpen.getOpenStatus() != OpenStatusEnum.END_OPENED,
                GlobalResultCode.INVALID_STATE, "开标信息不存在或未结束开标");


        // 1. 查询标段下所有供应商基本信息
        List<SrmTenderOfflineEvaluationSummarySupplierResp> supplierList =
                baseMapper.queryOfflineSupplierBasicInfo(req.getSectionId(), req.getNoticeId(), req.getProjectId(), tenderOpen.getCurrentRound());

        // 2. 查询评标委员会信息
        SrmTenderBidEvaluationResp evaluation = getBySectionId(req.getSectionId());
        if (evaluation != null) {
            resp.setEvaluationId(evaluation.getId());
            resp.setReportContent(evaluation.getReportContent());
            resp.setSummaryStatus(evaluation.getSummaryStatus());
            resp.setSummaryBy(evaluation.getSummaryBy());
            resp.setSummaryByName(evaluation.getSummaryByName());
            resp.setSummaryTime(evaluation.getSummaryTime());
        }

        resp.setSupplierList(supplierList);
        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean summaryOfflineEvaluation(SrmTenderOfflineEvaluationSummaryReq req) {
        // 1. 更新评标委员会的汇总信息
        SrmTenderBidEvaluation evaluation = getById(req.getEvaluationId());
        if (evaluation == null) {
            log.error("评标委员会不存在，ID: {}", req.getEvaluationId());
            return false;
        }

        evaluation.setSummaryStatus(EvaluationSummaryStatusEnum.SUMMARIZED);
        evaluation.setSummaryTime(LocalDateTime.now());
        evaluation.setReportContent(req.getReportContent());

        // 设置汇总人信息
        try {
            evaluation.setSummaryBy(SecurityUtils.getUser().getUsername());
            evaluation.setSummaryByName(SecurityUtils.getUser().getName());
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认值: {}", e.getMessage());
            evaluation.setSummaryBy("system");
            evaluation.setSummaryByName("系统");
        }

        // 更新评标委员会信息
        boolean updateResult = updateById(evaluation);
        if (!updateResult) {
            log.error("更新评标委员会信息失败，ID: {}", req.getEvaluationId());
            return false;
        }

        this.updateProjectProgressStatus(evaluation.getProjectId(), evaluation.getNoticeId());

        // 2. 更新供应商响应表中的评标信息
        if (CollectionUtil.isNotEmpty(req.getSupplierEvaluationInfoList())) {
            for (SrmTenderOfflineEvaluationSummaryReq.OfflineSupplierEvaluationInfo supplierInfo : req.getSupplierEvaluationInfoList()) {
                // 查询供应商响应记录
                LambdaQueryWrapper<SrmTenderSupplierResponse> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                        .eq(SrmTenderSupplierResponse::getTenantSupplierId, supplierInfo.getTenantSupplierId());

                SrmTenderSupplierResponse supplierResponse = srmTenderSupplierResponseService.getOne(queryWrapper);
                if (supplierResponse != null) {
                    // 更新中标相关信息
                    supplierResponse.setIsRecommendedWinner(supplierInfo.getIsRecommendedWinner());
                    supplierResponse.setWinnerCandidateOrder(supplierInfo.getWinnerCandidateOrder());

                    // 更新投标价格（手动输入的价格保存到quote_amount字段）
                    if (supplierInfo.getBidPrice() != null) {
                        supplierResponse.setQuoteAmount(supplierInfo.getBidPrice());
                    }

                    // 更新评标总分（手动输入的总分保存到total_score字段）
                    if (supplierInfo.getTotalScore() != null) {
                        supplierResponse.setTotalScore(supplierInfo.getTotalScore());
                    }

                    boolean updateSupplierResult = srmTenderSupplierResponseService.updateById(supplierResponse);
                    if (!updateSupplierResult) {
                        log.error("更新供应商响应信息失败，供应商ID: {}", supplierInfo.getTenantSupplierId());
                        return false;
                    }
                } else {
                    log.warn("未找到供应商响应记录，供应商ID: {}", supplierInfo.getTenantSupplierId());
                }
            }
        }

        log.info("线下评标汇总完成，评标委员会ID: {}", req.getEvaluationId());
        return true;
    }

    @Override
    public SrmTenderSupplierReviewDetailQueryResp querySupplierReviewDetail(SrmTenderSupplierReviewDetailQueryReq req) {
        SrmTenderSupplierReviewDetailQueryResp resp = new SrmTenderSupplierReviewDetailQueryResp();

        // 1. 查询供应商基本信息
        LambdaQueryWrapper<SrmTenderSupplierResponse> supplierWrapper = new LambdaQueryWrapper<>();
        supplierWrapper.eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                .eq(SrmTenderSupplierResponse::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierResponse::getProjectId, req.getProjectId())
                .eq(SrmTenderSupplierResponse::getTenantSupplierId, req.getTenantSupplierId());

        SrmTenderSupplierResponse supplier = srmTenderSupplierResponseService.getOne(supplierWrapper);
        if (supplier != null) {
            resp.setTenantSupplierId(supplier.getTenantSupplierId());
            resp.setSupplierName(supplier.getSupplierName());
        }

        // 2. 查询评审项节点列表
        List<String> reviewNodes = baseMapper.queryReviewNodes(req.getSectionId(), req.getNoticeId(), req.getProjectId());
        resp.setReviewNodes(reviewNodes);

        // 3. 查询专家评审详情
        List<SrmTenderSupplierReviewDetailQueryResp.ExpertReviewDetail> expertReviewList =
                baseMapper.queryExpertReviewDetails(req.getEvaluationId());

        // 4. 为每个专家填充节点评审结果
        for (SrmTenderSupplierReviewDetailQueryResp.ExpertReviewDetail expert : expertReviewList) {
            List<SrmTenderSupplierReviewDetailQueryResp.NodeReviewResult> nodeReviewResults =
                    baseMapper.queryExpertNodeReviewResults(req.getSectionId(), req.getNoticeId(), req.getProjectId(), req.getTenantSupplierId(), expert.getUserId());
            expert.setNodeReviewResults(nodeReviewResults);
        }

        // 5. 添加组长汇总行
        List<SrmTenderSupplierReviewDetailQueryResp.NodeReviewResult> leaderSummaryResults =
                baseMapper.queryLeaderSummaryResults(req.getSectionId(), req.getNoticeId(), req.getProjectId(), req.getTenantSupplierId());

        if (!leaderSummaryResults.isEmpty()) {
            SrmTenderSupplierReviewDetailQueryResp.ExpertReviewDetail leaderSummary =
                    new SrmTenderSupplierReviewDetailQueryResp.ExpertReviewDetail();
            leaderSummary.setUserId(null); // 组长汇总不关联具体用户
            leaderSummary.setExpertName("【组长汇总】");
            leaderSummary.setExpertCode("LEADER_SUMMARY");
            leaderSummary.setExpertCategory("汇总结果");
            leaderSummary.setRole("评标组长");
            leaderSummary.setNodeReviewResults(leaderSummaryResults);

            expertReviewList.add(leaderSummary);
        }

        resp.setExpertReviewList(expertReviewList);
        return resp;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean leaderSaveSummaryReview(SrmTenderLeaderSummaryReviewReq req) {
        if (CollectionUtil.isEmpty(req.getReviewItemSummaryList())) {
            log.warn("评审项汇总结果列表为空");
            return true;
        }

        // 获取当前用户信息
        String currentUserId = SecurityUtils.getUser().getId().toString();
        String currentUserName = SecurityUtils.getUser().getUsername();

        // 直接处理评审项列表，不按节点归纳
        for (SrmTenderLeaderSummaryReviewReq.ReviewItemSummary itemSummary : req.getReviewItemSummaryList()) {
            if (itemSummary.getScoringId() != null && itemSummary.getScoringId() > 0) {
                // 更新现有记录
                SrmTenderBidEvaluationScoring existingSummary = srmTenderBidEvaluationScoringService.getById(itemSummary.getScoringId());
                if (existingSummary != null) {
                    existingSummary.setIsConform(itemSummary.getReviewSummaryResult());
                    existingSummary.setConclusion(itemSummary.getSummaryConclusion());
                    srmTenderBidEvaluationScoringService.updateById(existingSummary);
                } else {
                    log.error("组长汇总记录不存在，ID: {}", itemSummary.getScoringId());
                    return false;
                }
            } else {
                // 新增记录
                if (itemSummary.getStandardId() == null) {
                    log.error("新增组长汇总记录时，评标标准ID不能为空");
                    return false;
                }

                // 检查是否已存在相同的组长汇总记录
                SrmTenderBidEvaluationScoring existingSummary = srmTenderBidEvaluationScoringService.lambdaQuery()
                        .eq(SrmTenderBidEvaluationScoring::getSectionId, req.getSectionId())
                        .eq(SrmTenderBidEvaluationScoring::getNoticeId, req.getNoticeId())
                        .eq(SrmTenderBidEvaluationScoring::getProjectId, req.getProjectId())
                        .eq(SrmTenderBidEvaluationScoring::getTenantSupplierId, req.getTenantSupplierId())
                        .eq(SrmTenderBidEvaluationScoring::getScoringDetailId, itemSummary.getStandardId())
                        .eq(SrmTenderBidEvaluationScoring::getScoringType, SrmTenderBidEvaluationScoring.ScoringType.LEADER_SUMMARY)
                        .one();

                if (existingSummary != null) {
                    // 如果已存在，则更新
                    existingSummary.setIsConform(itemSummary.getReviewSummaryResult());
                    existingSummary.setConclusion(itemSummary.getSummaryConclusion());
                    srmTenderBidEvaluationScoringService.updateById(existingSummary);
                } else {
                    // 创建新的组长汇总记录
                    SrmTenderBidEvaluationScoring leaderSummary = new SrmTenderBidEvaluationScoring();
                    leaderSummary.setProjectId(req.getProjectId());
                    leaderSummary.setNoticeId(req.getNoticeId());
                    leaderSummary.setSectionId(req.getSectionId());
                    leaderSummary.setUserId(Long.valueOf(currentUserId));
                    leaderSummary.setScoringDetailId(itemSummary.getStandardId());
                    leaderSummary.setTenantSupplierId(req.getTenantSupplierId());
                    leaderSummary.setIsConform(itemSummary.getReviewSummaryResult());
                    leaderSummary.setType(ReviewEnum.REVIEW);
                    leaderSummary.setScoringType(SrmTenderBidEvaluationScoring.ScoringType.LEADER_SUMMARY);
                    leaderSummary.setConclusion(itemSummary.getSummaryConclusion());
                    leaderSummary.setStatus(SrmTenderBidEvaluationScoring.SubmitStatus.SUMMARIZED);
                    leaderSummary.setDelFlag(0);

                    srmTenderBidEvaluationScoringService.save(leaderSummary);
                }
            }
        }

        srmTenderBidEvaluationScoringService.lambdaUpdate().eq(SrmTenderBidEvaluationScoring::getNoticeId, req.getNoticeId())
                .eq(SrmTenderBidEvaluationScoring::getProjectId, req.getProjectId())
                .eq(SrmTenderBidEvaluationScoring::getSectionId, req.getSectionId())
                .eq(SrmTenderBidEvaluationScoring::getTenantSupplierId, req.getTenantSupplierId())
                .eq(SrmTenderBidEvaluationScoring::getScoringType, SrmTenderBidEvaluationScoring.ScoringType.EXPERT_SCORING)
                .eq(SrmTenderBidEvaluationScoring::getType, ReviewEnum.REVIEW)
                .set(SrmTenderBidEvaluationScoring::getStatus, SrmTenderBidEvaluationScoring.SubmitStatus.SUMMARIZED)
                .update();

        log.info("组长保存汇总评审结果完成，标段ID: {}, 供应商ID: {}, 评审项数量: {}",
                req.getSectionId(), req.getTenantSupplierId(), req.getReviewItemSummaryList().size());
        return true;
    }


    @Override
    public boolean getNoticeEvaluationCompleteStatus(Long noticeId) {
        return srmTenderBidEvaluationScoringMapper.countExpertEvaluationCompleted(noticeId) == 0;
    }
}
