package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmProjectMember;
import com.ylz.saas.entity.SrmTenderBidEvaluation;
import com.ylz.saas.entity.SrmTenderEvaluationSignature;
import com.ylz.saas.enums.ProjectMemberTypeEnum;
import com.ylz.saas.enums.SignatureStatusEnum;
import com.ylz.saas.mapper.SrmTenderEvaluationSignatureMapper;
import com.ylz.saas.mapper.SrmTenderBidEvaluationMapper;
import com.ylz.saas.service.SrmProjectMemberService;
import com.ylz.saas.service.SrmTenderEvaluationSignatureService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 针对表【srm_tender_evaluation_signature(评标汇总签名进度表)】的数据库操作Service实现
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmTenderEvaluationSignatureServiceImpl extends ServiceImpl<SrmTenderEvaluationSignatureMapper, SrmTenderEvaluationSignature>
        implements SrmTenderEvaluationSignatureService {

    private final SrmProjectMemberService srmProjectMemberService;
    private final SrmTenderBidEvaluationMapper srmTenderBidEvaluationMapper;




    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean signEvaluation(Long sectionId, Long userId, String signatureComment) {
        // 查询签名记录
        SrmTenderEvaluationSignature signature = baseMapper.getBySectionIdAndUserId(sectionId, userId);
        if (signature == null) {
            log.error("签名记录不存在，标段ID: {}, 用户ID: {}", sectionId, userId);
            return false;
        }

        // 更新签名状态
        signature.setSignatureStatus(SignatureStatusEnum.SIGNED);
        signature.setSignatureTime(LocalDateTime.now());
        signature.setSignatureComment(signatureComment);

        return updateById(signature);
    }

    @Override
    public int[] getSignatureProgress(Long sectionId) {
        int signedCount = baseMapper.countSignedBySectionId(sectionId);
        int totalCount = baseMapper.countTotalBySectionId(sectionId);
        return new int[]{signedCount, totalCount};
    }

    @Override
    public boolean isAllSigned(Long sectionId) {
        int[] progress = getSignatureProgress(sectionId);
        return progress[0] > 0 && progress[0] == progress[1];
    }

    @Override
    public boolean hasUserSigned(Long sectionId, Long userId) {
        SrmTenderEvaluationSignature signature = baseMapper.getBySectionIdAndUserId(sectionId, userId);
        return signature != null && SignatureStatusEnum.SIGNED.equals(signature.getSignatureStatus());
    }
}
