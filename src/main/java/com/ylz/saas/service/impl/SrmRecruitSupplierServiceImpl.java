package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.*;
import com.ylz.saas.enums.ApprovalType;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.RecruitMemberRoleEnum;
import com.ylz.saas.mapper.SrmRecruitSupplierMapper;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.ApproverInfo;
import com.ylz.saas.resp.SrmRecruitResultPageResp;
import com.ylz.saas.resp.SrmRecruitSignUpResp;
import com.ylz.saas.resp.SrmRecruitSupplierDetailResp;
import com.ylz.saas.resp.SrmRecruitSupplierPageResp;
import com.ylz.saas.service.*;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.sequence.generator.impl.DefaultCodeGenerator;
import lombok.AllArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Arrays;
import java.util.ArrayList;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 供应商招募公告表Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmRecruitSupplierServiceImpl extends ServiceImpl<SrmRecruitSupplierMapper, SrmRecruitSupplier> implements SrmRecruitSupplierService {


    private final SrmRecruitSupplierSignUpService srmRecruitSupplierSignUpService;

    private final SrmRecruitSupplierSignUpCertificateService srmRecruitSupplierSignUpCertificateService;

    private final SrmRecruitSupplierMemberService srmRecruitSupplierMemberService;

    private final SrmRecruitSupplierDetailService srmRecruitSupplierDetailService;

    private final SrmRecruitSupplierCertificateService srmRecruitSupplierCertificateService;

    private final SrmRecruitSupplierMediaService srmRecruitSupplierMediaService;

    private final DefaultCodeGenerator codeGenerator;

    @Override
    public IPage<SrmRecruitSupplierPageResp> pageRecruitSupplier(SrmRecruitSupplierPageReq req, Page<SrmRecruitSupplierPageResp> page) {
        return this.baseMapper.selectRecruitSupplierPage(page, req);
    }

    @Override
    public SrmRecruitSupplierDetailResp getRecruitSupplierDetail(String idOrCode) {
        // 根据ID或编码查询主表信息
        SrmRecruitSupplier recruitSupplier = getByIdOrCode(idOrCode);
        if (recruitSupplier == null) {
            return null;
        }

        // 创建响应对象并复制基础属性
        SrmRecruitSupplierDetailResp detailResp = new SrmRecruitSupplierDetailResp();
        BeanUtils.copyProperties(recruitSupplier, detailResp);

        // 查询招募小组成员列表（联查用户表和部门表）
        detailResp.setMemberList(getMemberList(recruitSupplier.getId()));

        // 查询物资清单列表
        detailResp.setMaterialList(getMaterialList(recruitSupplier.getId()));

        // 查询资质要求列表
        detailResp.setCertificateList(getCertificateList(recruitSupplier.getId()));

        // 查询合作媒体列表
        detailResp.setMediaList(getMediaList(recruitSupplier.getId()));

        // 查询指定审批人信息列表
        detailResp.setAuditPersonList(getApproverInfoFromIds(recruitSupplier.getAuditPersonIds()));

        // 查询指定终止审批人信息列表
        detailResp.setTerminationAuditPersonList(getApproverInfoFromIds(recruitSupplier.getTerminationAuditPersonIds()));

        return detailResp;
    }



    /**
     * 供工作流调用
     * @param id
     * @param statusEnum
     */
    @Override
    public void auditRecruitSupplier(Long id, ApproveStatusEnum statusEnum) {
        SrmRecruitSupplier byId = this.getById(id);
        byId.setApprovalStatus(statusEnum);
        if (ApproveStatusEnum.APPROVE.equals(statusEnum)) {
            byId.setProcess(SrmRecruitSupplier.Process.PROCESSING);
        }
        this.updateById(byId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SrmRecruitSupplier saveRecruitSupplier(SrmRecruitSupplierSaveReq req) {
        // 1. 业务校验
        validateRecruitSupplierData(req);

        // 2. 创建主表实体对象
        SrmRecruitSupplier recruitSupplier = new SrmRecruitSupplier();
        BeanUtils.copyProperties(req, recruitSupplier);

        // 处理审批人ID列表转换为字符串
        if (req.getAuditPersonIds() != null && !req.getAuditPersonIds().isEmpty()) {
            recruitSupplier.setAuditPersonIds(req.getAuditPersonIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
        }

        if (req.getTerminationAuditPersonIds() != null && !req.getTerminationAuditPersonIds().isEmpty()) {
            recruitSupplier.setTerminationAuditPersonIds(req.getTerminationAuditPersonIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
        }

        // 3. 生成招募编码：ZMXM+年月日（yyyyMMdd）+四位数计数（如0001）
        String recruitCode = codeGenerator.generateWithDate(CodeGeneratorPrefixEnum.ZMXM.name());
        recruitSupplier.setRecruitCode(recruitCode);

        // 4. 设置默认状态
        recruitSupplier.setProcess(SrmRecruitSupplier.Process.NEW); // 草稿状态
        recruitSupplier.setApprovalStatus(ApproveStatusEnum.TO_APPROVE); // 待审批
        recruitSupplier.setStoreApprovalStatus(ApproveStatusEnum.TO_APPROVE); // 入库待审批
        recruitSupplier.setTerminationAuditType(ApprovalType.AUTO); // 终止审批发起类型
        recruitSupplier.setTerminationApprovalStatus(ApproveStatusEnum.TO_APPROVE); // 终止审核状态默认为待审批
        recruitSupplier.setDelFlag(0); // 删除标识

        // 5. 保存主表到数据库
        save(recruitSupplier);

        // 6. 保存子表数据
        Long recruitId = recruitSupplier.getId();
        saveMemberList(req.getMemberList(), recruitId);
        saveMaterialList(req.getMaterialList(), recruitId);
        saveCertificateList(req.getCertificateList(), recruitId);
        saveMediaList(req.getMediaList(), recruitId);


//        7. 提交 todo 目前只有修改状态的方法
        this.submitRecruit(recruitSupplier);


        return recruitSupplier;
    }

    private void submitRecruit(SrmRecruitSupplier recruitSupplier) {

//        todo

        this.changeSrmRecruitSupplierProcess(recruitSupplier, SrmRecruitSupplier.Process.PROCESSING);

    }

    private void changeSrmRecruitSupplierProcess(SrmRecruitSupplier recruitSupplier, SrmRecruitSupplier.Process process) {

        this.lambdaUpdate().eq(SrmRecruitSupplier::getId, recruitSupplier.getId())
                .set(SrmRecruitSupplier::getProcess, process)
                .update();
    }

    /**
     * 根据ID或编码查询主表信息
     */
    private SrmRecruitSupplier getByIdOrCode(String idOrCode) {
        LambdaQueryWrapper<SrmRecruitSupplier> queryWrapper = Wrappers.lambdaQuery();

        // 尝试解析为Long类型，如果成功则按ID查询，否则按编码查询
        try {
            Long id = Long.parseLong(idOrCode);
            queryWrapper.eq(SrmRecruitSupplier::getId, id);
        } catch (NumberFormatException e) {
            queryWrapper.eq(SrmRecruitSupplier::getRecruitCode, idOrCode);
        }

        return getOne(queryWrapper);
    }

    /**
     * 查询招募小组成员列表
     */
    private List<SrmRecruitSupplierDetailResp.MemberInfo> getMemberList(Long recruitId) {
        return this.baseMapper.selectMemberList(recruitId);
    }

    /**
     * 查询物资清单列表
     */
    private List<SrmRecruitSupplierDetailResp.MaterialInfo> getMaterialList(Long recruitId) {
        return this.baseMapper.selectMaterialList(recruitId);
    }

    /**
     * 查询资质要求列表
     */
    private List<SrmRecruitSupplierDetailResp.CertificateInfo> getCertificateList(Long recruitId) {
        return this.baseMapper.selectCertificateList(recruitId);
    }

    /**
     * 查询合作媒体列表
     */
    private List<SrmRecruitSupplierDetailResp.MediaInfo> getMediaList(Long recruitId) {
        return this.baseMapper.selectMediaList(recruitId);
    }

    /**
     * 业务数据校验
     */
    private void validateRecruitSupplierData(SrmRecruitSupplierSaveReq req) {
        // 校验招募小组成员中是否有且仅有一个招募负责人
        if (!CollectionUtils.isEmpty(req.getMemberList())) {
            long leaderCount = req.getMemberList().stream()
                    .filter(member -> RecruitMemberRoleEnum.RECRUIT_LEADER.equals(member.getRole()))
                    .count();
            if (leaderCount != 1) {
                throw new RuntimeException("招募小组必须有且仅有一个招募负责人");
            }
        }

//        // 校验物资清单与招募类型的一致性
        if (!CollectionUtils.isEmpty(req.getMaterialList())) {
            req.getMaterialList().forEach(e -> {
                e.setRecruitType(req.getRecruitType());
                if (req.getRecruitType() == SrmRecruitSupplier.RecruitType.ENGINEERING) {
                    if (StringUtils.isBlank(e.getEngineeringContext())) {
                        throw new RuntimeException("招募类型为工程类时，请填入工程服务内容");
                    }
                } else {
                    if (StringUtils.isBlank(e.getMaterialName())  || e.getLocationId() == null) {
                        throw new RuntimeException("招募类型为物资类时，请选择物料与牧场");
                    }
                }

            });


        }


    }

    /**
     * 保存招募小组成员列表
     */
    private void saveMemberList(List<SrmRecruitSupplierSaveReq.MemberInfo> memberList, Long recruitId) {
        if (CollectionUtils.isEmpty(memberList)) {
            return;
        }

        List<SrmRecruitSupplierMember> members = new ArrayList<>();
        for (SrmRecruitSupplierSaveReq.MemberInfo memberInfo : memberList) {
            SrmRecruitSupplierMember member = new SrmRecruitSupplierMember();
            BeanUtils.copyProperties(memberInfo, member);
            member.setRecruitId(recruitId);
            member.setDelFlag(0);
            members.add(member);
        }
        srmRecruitSupplierMemberService.saveBatch(members);
    }

    /**
     * 更新招募小组成员列表
     */
    private void updateMemberList(List<SrmRecruitSupplierSaveReq.MemberInfo> memberList, Long recruitId) {
        // 先删除原有数据
        srmRecruitSupplierMemberService.lambdaUpdate()
                .eq(SrmRecruitSupplierMember::getRecruitId, recruitId)
                .set(SrmRecruitSupplierMember::getDelFlag, 1)
                .update();

        // 保存新数据
        saveMemberList(memberList, recruitId);
    }

    /**
     * 保存物资清单列表
     */
    private void saveMaterialList(List<SrmRecruitSupplierSaveReq.MaterialInfo> materialList, Long recruitId) {
        if (CollectionUtils.isEmpty(materialList)) {
            return;
        }

        List<SrmRecruitSupplierDetail> materials = new ArrayList<>();
        for (SrmRecruitSupplierSaveReq.MaterialInfo materialInfo : materialList) {
            SrmRecruitSupplierDetail material = new SrmRecruitSupplierDetail();
            BeanUtils.copyProperties(materialInfo, material);
            material.setRecruitId(recruitId);
            material.setDelFlag(0);
            materials.add(material);
        }
        srmRecruitSupplierDetailService.saveBatch(materials);
    }

    /**
     * 更新物资清单列表
     */
    private void updateMaterialList(List<SrmRecruitSupplierSaveReq.MaterialInfo> materialList, Long recruitId) {
        // 先删除原有数据
        srmRecruitSupplierDetailService.lambdaUpdate()
                .eq(SrmRecruitSupplierDetail::getRecruitId, recruitId)
                .set(SrmRecruitSupplierDetail::getDelFlag, 1)
                .update();

        // 保存新数据
        saveMaterialList(materialList, recruitId);
    }

    /**
     * 保存资质要求列表
     */
    private void saveCertificateList(List<SrmRecruitSupplierSaveReq.CertificateInfo> certificateList, Long recruitId) {
        if (CollectionUtils.isEmpty(certificateList)) {
            return;
        }

        List<SrmRecruitSupplierCertificate> certificates = new ArrayList<>();
        for (SrmRecruitSupplierSaveReq.CertificateInfo certificateInfo : certificateList) {
            SrmRecruitSupplierCertificate certificate = new SrmRecruitSupplierCertificate();
            BeanUtils.copyProperties(certificateInfo, certificate);
            certificate.setRecruitId(recruitId);
            certificate.setDelFlag(0);
            certificates.add(certificate);
        }
        srmRecruitSupplierCertificateService.saveBatch(certificates);
    }

    /**
     * 更新资质要求列表
     */
    private void updateCertificateList(List<SrmRecruitSupplierSaveReq.CertificateInfo> certificateList, Long recruitId) {
        // 先删除原有数据
        srmRecruitSupplierCertificateService.lambdaUpdate()
                .eq(SrmRecruitSupplierCertificate::getRecruitId, recruitId)
                .set(SrmRecruitSupplierCertificate::getDelFlag, 1)
                .update();

        // 保存新数据
        saveCertificateList(certificateList, recruitId);
    }

    /**
     * 保存合作媒体列表
     */
    private void saveMediaList(List<SrmRecruitSupplierSaveReq.MediaInfo> mediaList, Long recruitId) {
        if (CollectionUtils.isEmpty(mediaList)) {
            return;
        }

        List<SrmRecruitSupplierMedia> medias = new ArrayList<>();
        for (SrmRecruitSupplierSaveReq.MediaInfo mediaInfo : mediaList) {
            SrmRecruitSupplierMedia media = new SrmRecruitSupplierMedia();
            BeanUtils.copyProperties(mediaInfo, media);
            media.setRecruitId(recruitId);
            media.setDelFlag(0);
            medias.add(media);
        }
        srmRecruitSupplierMediaService.saveBatch(medias);
    }

    /**
     * 更新合作媒体列表
     */
    private void updateMediaList(List<SrmRecruitSupplierSaveReq.MediaInfo> mediaList, Long recruitId) {
        // 先删除原有数据
        srmRecruitSupplierMediaService.lambdaUpdate()
                .eq(SrmRecruitSupplierMedia::getRecruitId, recruitId)
                .set(SrmRecruitSupplierMedia::getDelFlag, 1)
                .update();

        // 保存新数据
        saveMediaList(mediaList, recruitId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object signUp(SrmRecruitSignUpReq req) {

//         1. 校验参数
        SrmRecruitSupplier srmRecruitSupplier = validateRecruitSignUpData(req);

        // 2. 创建实体对象
        SrmRecruitSupplierSignUp signUp = new SrmRecruitSupplierSignUp();
        BeanUtils.copyProperties(req, signUp);

//        3. 设置默认状态
        signUp.setApprovalStatus(ApproveStatusEnum.TO_APPROVE);
        signUp.setDelFlag(0);

//        4. 判断是否需要内部审核

        SrmRecruitSupplier.RecruitWay recruitWay = srmRecruitSupplier.getRecruitWay();
        if (SrmRecruitSupplier.RecruitWay.DIRECTLY.equals(recruitWay)) {
            signUp.setApprovalStatus(ApproveStatusEnum.APPROVE);
        } else if (SrmRecruitSupplier.RecruitWay.PREQUALIFICATION.equals(recruitWay)) {
            signUp.setApprovalStatus(ApproveStatusEnum.APPROVING);
        }


//        5. 保存
        srmRecruitSupplierSignUpService.save(signUp);

//        6. 保存子表数据
        this.saveSignUpCertificates(req.getSignUpCertificates(), signUp.getId());


        return signUp;
    }

    private void saveSignUpCertificates(List<SrmRecruitSignUpReq.SignUpCertificate> signUpCertificates, Long id) {
        if (CollUtil.isEmpty(signUpCertificates)) {
            return;
        }

        List<SrmRecruitSupplierSignUpCertificate> saveSignUpCertificates = Lists.newArrayList();
        for (SrmRecruitSignUpReq.SignUpCertificate signUpCertificate : signUpCertificates) {
            SrmRecruitSupplierSignUpCertificate saveSignUpCertificate = new SrmRecruitSupplierSignUpCertificate();
            BeanUtils.copyProperties(signUpCertificate, saveSignUpCertificate);
            saveSignUpCertificate.setDelFlag(0);
            saveSignUpCertificate.setRecruitSignUpId(id);
            saveSignUpCertificates.add(saveSignUpCertificate);
        }

        srmRecruitSupplierSignUpCertificateService.saveBatch(saveSignUpCertificates);


    }

    private SrmRecruitSupplier validateRecruitSignUpData(SrmRecruitSignUpReq req) {

        if (CollUtil.isNotEmpty(req.getSignUpCertificates())) {
            req.getSignUpCertificates().forEach(e -> e.setRecruitId(req.getRecruitId()));
        }
        SrmRecruitSupplier byId = this.getById(req.getRecruitId());
        SrmRecruitSupplier.Process process = byId.getProcess();

        ExceptionUtil.check(!SrmRecruitSupplier.Process.PROCESSING.equals(process), "500", "当前招募公告无法报名");

        return byId;
    }

    @Override
    public IPage<SrmRecruitResultPageResp> pageSignUp(SrmRecruitResultPageReq req, Page<SrmRecruitResultPageResp> page) {
        SrmRecruitSupplier srmRecruitSupplier = this.getById(req.getRecruitId());
        ExceptionUtil.check(srmRecruitSupplier == null, "500", "未查询到招募公告信息");
        return this.baseMapper.pageResult(req, page);
    }

    @Override
    public SrmRecruitSignUpResp getSignUpDetail(Long signUpId) {
        SrmRecruitSupplierSignUp signUp = srmRecruitSupplierSignUpService.getById(signUpId);
        ExceptionUtil.check(signUp == null, "500", "未查询到招募公告");
        SrmRecruitSignUpResp resp = new SrmRecruitSignUpResp();
        BeanUtils.copyProperties(signUp, resp);
        List<SrmRecruitSupplierSignUpCertificate> certificates = srmRecruitSupplierSignUpCertificateService.getBySignUpId(signUpId);

        if (CollUtil.isNotEmpty(certificates)) {
            resp.setSignUpCertificates(certificates.stream().map(e -> BeanUtil.copyProperties(e, SrmRecruitSignUpResp.SignUpCertificate.class)).toList());
        }
        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRecruitSupplierSubData(Long recruitId, SrmRecruitSupplierSaveReq req) {
        log.info("开始更新招募公告子表数据，招募公告ID：{}", recruitId);

        // 1. 更新招募小组成员列表
        updateMemberList(req.getMemberList(), recruitId);
        log.debug("招募小组成员列表更新完成");

        // 2. 更新物资清单列表
        updateMaterialList(req.getMaterialList(), recruitId);
        log.debug("物资清单列表更新完成");

        // 3. 更新资质要求列表
        updateCertificateList(req.getCertificateList(), recruitId);
        log.debug("资质要求列表更新完成");

        // 4. 更新合作媒体列表
        updateMediaList(req.getMediaList(), recruitId);
        log.debug("合作媒体列表更新完成");

        log.info("招募公告子表数据更新完成，招募公告ID：{}", recruitId);
    }

    @Override
    public Object auditSignUp(SrmRecruitSupplierSignUpApproveReq req) {
        SaasUser user = SecurityUtils.getUser();
        SrmRecruitSupplierSignUp signUp = srmRecruitSupplierSignUpService.getById(req.getSignUpId());
        ExceptionUtil.check(signUp == null, "500", "查询不到对应招募公告");
        ApproveStatusEnum approvalStatus = signUp.getApprovalStatus();
        ExceptionUtil.check(!ApproveStatusEnum.APPROVING.equals(approvalStatus), "500", "当前报名状态不允许审核");
        signUp.setApprovalStatus(req.getApprovalStatus());
        signUp.setApprovalBy(user.getId());
        signUp.setApprovalByName(user.getName());
        signUp.setApprovalRemark(req.getApproveRemark());
        srmRecruitSupplierSignUpService.updateById(signUp);
        return signUp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitStoreResult(SrmRecruitSupplierStoreSubmitReq req) {
        log.info("开始提交入库结果，招募公告ID：{}", req.getRecruitId());

        // 1. 校验招募公告状态
        SrmRecruitSupplier recruitSupplier = this.getById(req.getRecruitId());
        ExceptionUtil.check(recruitSupplier == null, "500", "招募公告不存在");

        SrmRecruitSupplier.Process process = recruitSupplier.getProcess();
        ExceptionUtil.check(!SrmRecruitSupplier.Process.PROCESSING.equals(process)
                && !SrmRecruitSupplier.Process.END.equals(process),
                "500", "只有招募中或招募结束状态的公告才能提交入库结果");

        // 2. 查询并校验报名记录
        List<Long> signUpIds = req.getStoreResults().stream()
                .map(SrmRecruitSupplierStoreSubmitReq.StoreResultItem::getSignUpId)
                .toList();

        List<SrmRecruitSupplierSignUp> signUpList = srmRecruitSupplierSignUpService.listByIds(signUpIds);
        ExceptionUtil.check(signUpList.size() != signUpIds.size(), "500", "部分报名记录不存在");

        // 3. 校验报名记录的审核状态必须为APPROVE
        for (SrmRecruitSupplierSignUp signUp : signUpList) {
            ExceptionUtil.check(!ApproveStatusEnum.APPROVE.equals(signUp.getApprovalStatus()),
                    "500", String.format("报名记录[%d]审核状态不是通过，无法提交入库结果", signUp.getId()));
            ExceptionUtil.check(!req.getRecruitId().equals(signUp.getRecruitId()),
                    "500", String.format("报名记录[%d]不属于当前招募公告", signUp.getId()));
        }

        // 4. 更新报名记录的入库状态
        Map<Long, Integer> storeResultMap = req.getStoreResults().stream()
                .collect(Collectors.toMap(
                        SrmRecruitSupplierStoreSubmitReq.StoreResultItem::getSignUpId,
                        SrmRecruitSupplierStoreSubmitReq.StoreResultItem::getIsStored
                ));

        for (SrmRecruitSupplierSignUp signUp : signUpList) {
            Integer isStored = storeResultMap.get(signUp.getId());
            signUp.setIsStored(isStored);
        }

        srmRecruitSupplierSignUpService.updateBatchById(signUpList);

        // 5. 更新招募公告的入库审核状态为APPROVING
        recruitSupplier.setStoreApprovalStatus(ApproveStatusEnum.APPROVING);
        this.updateById(recruitSupplier);

        log.info("入库结果提交完成，招募公告ID：{}，处理报名记录数：{}", req.getRecruitId(), signUpList.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminateRecruit(SrmRecruitSupplierTerminateReq req) {
        log.info("开始终止招募，招募公告ID：{}", req.getRecruitId());

        // 1. 校验招募公告状态
        SrmRecruitSupplier recruitSupplier = this.getById(req.getRecruitId());
        ExceptionUtil.check(recruitSupplier == null, "500", "招募公告不存在");

        SrmRecruitSupplier.Process process = recruitSupplier.getProcess();
        ExceptionUtil.check(!SrmRecruitSupplier.Process.PROCESSING.equals(process),
                "500", "只有招募中状态的公告才能提交终止审核");

        // 2. 校验终止审核状态
        ExceptionUtil.check(!ApproveStatusEnum.TO_APPROVE.equals(recruitSupplier.getTerminationApprovalStatus()),
                "500", "该招募公告已提交终止审核，无法重复提交");

        // 3. 更新终止审核状态和原因
        recruitSupplier.setTerminationApprovalStatus(ApproveStatusEnum.APPROVING);
        recruitSupplier.setTerminationReason(req.getTerminationReason());
        this.updateById(recruitSupplier);

        // 4. 这里可以调用工作流或其他审核流程
        // TODO: 启动终止审核流程

        log.info("终止招募提交成功，招募公告ID：{}，终止原因：{}", req.getRecruitId(), req.getTerminationReason());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditTerminateRecruit(Long recruitId, ApproveStatusEnum approvalStatus, String approvalRemark) {
        log.info("开始审核终止招募，招募公告ID：{}，审核状态：{}", recruitId, approvalStatus);

        // 1. 校验招募公告
        SrmRecruitSupplier recruitSupplier = this.getById(recruitId);
        ExceptionUtil.check(recruitSupplier == null, "500", "招募公告不存在");

        // 2. 校验当前状态
        ExceptionUtil.check(!ApproveStatusEnum.APPROVING.equals(recruitSupplier.getTerminationApprovalStatus()),
                "500", "该招募公告不在终止审核中状态");

        // 3. 更新审核状态
        recruitSupplier.setTerminationApprovalStatus(approvalStatus);

        // 4. 如果审核通过，修改招募进程为终止
        if (ApproveStatusEnum.APPROVE.equals(approvalStatus)) {
            recruitSupplier.setProcess(SrmRecruitSupplier.Process.TERMINATION);
            log.info("终止审核通过，招募公告状态已更新为终止，招募公告ID：{}", recruitId);
        } else if (ApproveStatusEnum.APPROVE_REJECT.equals(approvalStatus)) {
            // 如果审核驳回，恢复为待审核状态
            recruitSupplier.setTerminationApprovalStatus(ApproveStatusEnum.TO_APPROVE);
            log.info("终止审核驳回，招募公告终止审核状态已恢复为待审核，招募公告ID：{}", recruitId);
        }

        this.updateById(recruitSupplier);

        // 5. 这里可以发送通知给相关人员
        // TODO: 发送审核结果通知

        log.info("终止招募审核完成，招募公告ID：{}，审核结果：{}", recruitId, approvalStatus);
    }

    @Override
    public List<ApproverInfo> getApproverInfoByUserIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return List.of();
        }

        List<ApproverInfo> approverInfos = this.baseMapper.selectApproverInfoByUserIds(userIds);

        // 构建显示名称
        approverInfos.forEach(ApproverInfo::buildDisplayName);

        return approverInfos;
    }

    /**
     * 根据ID字符串获取审批人信息
     */
    private List<ApproverInfo> getApproverInfoFromIds(String idsStr) {
        if (idsStr == null || idsStr.trim().isEmpty()) {
            return List.of();
        }

        try {
            List<Long> userIds = Arrays.stream(idsStr.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::valueOf)
                    .toList();

            return getApproverInfoByUserIds(userIds);
        } catch (NumberFormatException e) {
            log.warn("解析审批人ID字符串失败: {}", idsStr, e);
            return List.of();
        }
    }
}
