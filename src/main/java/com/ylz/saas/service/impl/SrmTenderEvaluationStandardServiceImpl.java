package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.SrmTenderEvaluationStandard;
import com.ylz.saas.enums.ProjectMemberRoleEnum;
import com.ylz.saas.mapper.SrmTenderEvaluationStandardMapper;
import com.ylz.saas.req.SrmTenderEvaluationProgressReq;
import com.ylz.saas.req.SrmTenderEvaluationStandardReq;
import com.ylz.saas.resp.SrmTenderEvaluationProgressResp;
import com.ylz.saas.resp.SrmTenderEvaluationStandardResp;
import com.ylz.saas.service.SrmTenderEvaluationStandardService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 针对表【srm_tender_evaluation_standard(评标标准)】的数据库操作Service实现
 * <AUTHOR>
 * @createDate 2025-07-10
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmTenderEvaluationStandardServiceImpl extends ServiceImpl<SrmTenderEvaluationStandardMapper, SrmTenderEvaluationStandard>
        implements SrmTenderEvaluationStandardService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveEvaluationStandard(SrmTenderEvaluationStandardReq req) {
        SrmTenderEvaluationStandard entity = new SrmTenderEvaluationStandard();
        BeanUtil.copyProperties(req, entity);
        return save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateEvaluationStandard(SrmTenderEvaluationStandardReq req) {
        SrmTenderEvaluationStandard entity = new SrmTenderEvaluationStandard();
        BeanUtil.copyProperties(req, entity);
        return updateById(entity);
    }

    @Override
    public SrmTenderEvaluationStandardResp getByIdEvaluationStandard(Long id) {
        SrmTenderEvaluationStandard entity = getById(id);
        if (entity == null) {
            return null;
        }
        SrmTenderEvaluationStandardResp resp = new SrmTenderEvaluationStandardResp();
        BeanUtil.copyProperties(entity, resp);
        return resp;
    }

    @Override
    public List<SrmTenderEvaluationStandardResp> listBySectionId(Long sectionId) {
        LambdaQueryWrapper<SrmTenderEvaluationStandard> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SrmTenderEvaluationStandard::getSectionId, sectionId)
                .orderByAsc(SrmTenderEvaluationStandard::getSortOrder)
                .orderByAsc(SrmTenderEvaluationStandard::getCreateTime);

        List<SrmTenderEvaluationStandard> entityList = list(queryWrapper);

        return entityList.stream()
                .map(entity -> {
                    SrmTenderEvaluationStandardResp resp = new SrmTenderEvaluationStandardResp();
                    BeanUtil.copyProperties(entity, resp);
                    return resp;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<SrmTenderEvaluationStandardResp> listBySectionIdAndNoticeId(Long sectionId, Long noticeId) {
        LambdaQueryWrapper<SrmTenderEvaluationStandard> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SrmTenderEvaluationStandard::getSectionId, sectionId)
                .eq(SrmTenderEvaluationStandard::getNoticeId, noticeId)
                .orderByAsc(SrmTenderEvaluationStandard::getSortOrder)
                .orderByAsc(SrmTenderEvaluationStandard::getCreateTime);

        List<SrmTenderEvaluationStandard> entityList = list(queryWrapper);

        return entityList.stream()
                .map(entity -> {
                    SrmTenderEvaluationStandardResp resp = new SrmTenderEvaluationStandardResp();
                    BeanUtil.copyProperties(entity, resp);
                    return resp;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveEvaluationStandards(List<SrmTenderEvaluationStandardReq> standardList) {
        if (CollectionUtil.isEmpty(standardList)) {
            return true;
        }

        List<SrmTenderEvaluationStandard> entityList = standardList.stream()
                .map(req -> {
                    SrmTenderEvaluationStandard entity = new SrmTenderEvaluationStandard();
                    BeanUtil.copyProperties(req, entity);
                    return entity;
                })
                .collect(Collectors.toList());

        return saveBatch(entityList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdsEvaluationStandard(List<Long> ids) {
        return removeByIds(ids);
    }

    @Override
    public SrmTenderEvaluationProgressResp getEvaluationProgress(SrmTenderEvaluationProgressReq req) {
        SrmTenderEvaluationProgressResp resp = new SrmTenderEvaluationProgressResp();

        // 1. 查询评标委员会信息
        SrmTenderEvaluationProgressResp.EvaluationCommitteeInfo committeeInfo =
                baseMapper.getCommitteeInfo(req.getSectionId(), req.getNoticeId());
        resp.setCommitteeInfo(committeeInfo);

        // 2. 查询评审节点列表
        List<SrmTenderEvaluationProgressResp.EvaluationNode> evaluationNodes =
                baseMapper.getEvaluationNodes(req.getSectionId(), req.getNoticeId());

        // 3. 查询所有评审项详细信息
        List<SrmTenderEvaluationProgressResp.EvaluationItem> allItems =
                baseMapper.getEvaluationItems(req.getSectionId(), req.getNoticeId());

        // 4. 为每个评审节点分配对应的评审项
        Map<String, List<SrmTenderEvaluationProgressResp.EvaluationItem>> itemsByNode =
                allItems.stream().collect(Collectors.groupingBy(SrmTenderEvaluationProgressResp.EvaluationItem::getNodeName));

        for (SrmTenderEvaluationProgressResp.EvaluationNode node : evaluationNodes) {
            node.setItems(itemsByNode.getOrDefault(node.getNodeName(), new ArrayList<>()));
        }

        resp.setEvaluationNodes(evaluationNodes);

        // 5. 查询评标人员进度列表
        if (committeeInfo != null) {
            List<SrmTenderEvaluationProgressResp.EvaluatorProgress> evaluatorProgressList =
                    baseMapper.getEvaluatorProgressList(committeeInfo.getId());

            // 6. 为每个评标人员查询各评审节点的完成情况
            for (SrmTenderEvaluationProgressResp.EvaluatorProgress evaluator : evaluatorProgressList) {
                List<SrmTenderEvaluationProgressResp.NodeCompletionStatus> nodeCompletions =
                        baseMapper.getEvaluatorNodeCompletion(req.getSectionId(), req.getNoticeId(), evaluator.getUserId());

                Map<String, SrmTenderEvaluationProgressResp.NodeCompletionStatus> nodeCompletionMap = new HashMap<>();
                for (SrmTenderEvaluationProgressResp.NodeCompletionStatus completion : nodeCompletions) {
                    nodeCompletionMap.put(completion.getNodeName(), completion);
                }
                evaluator.setNodeCompletionMap(nodeCompletionMap);
            }
            resp.setEvaluatorProgressList(evaluatorProgressList);
        }

        // 7. 查询当前用户在该项目中的角色权限
        try {
            Long currentUserId = SecurityUtils.getUser().getId();
            String roleStr = baseMapper.getCurrentUserRole(req.getNoticeId(), currentUserId);
            if (roleStr != null) {
                ProjectMemberRoleEnum currentUserRole = ProjectMemberRoleEnum.valueOf(roleStr);
                resp.setCurrentUserRole(currentUserRole);
            }
        } catch (Exception e) {
            // 如果获取当前用户信息失败，不影响主要功能，只记录日志
            log.warn("获取当前用户角色权限失败: {}", e.getMessage());
        }

        return resp;
    }
}

