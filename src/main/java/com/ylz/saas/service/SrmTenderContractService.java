package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmTenderContract;
import com.ylz.saas.req.SrmTenderContractQueryReq;
import com.ylz.saas.req.SrmTenderContractSaveReq;
import com.ylz.saas.resp.AwardedSupplierResp;
import com.ylz.saas.resp.SrmTenderContractDetailResp;
import com.ylz.saas.resp.SrmTenderContractQueryResp;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_contract(合同表)】的数据库操作Service
* @createDate 2025-07-21 17:26:10
*/
public interface SrmTenderContractService extends IService<SrmTenderContract> {
    

    void saveContract(SrmTenderContractSaveReq req) throws InvocationTargetException, IllegalAccessException;

    void updateContract(SrmTenderContractSaveReq req);

    SrmTenderContract handlerUpdateContract(SrmTenderContractSaveReq req);

    Page<SrmTenderContractQueryResp> query(Page<SrmTenderContractQueryResp> page, SrmTenderContractQueryReq req);

    void delete(Long id);

    SrmTenderContractDetailResp queryDetail(String id);

    void revoke(Long id);

    void confirmSign(Long id);

    void contractReject(Long id);

    void contractStatusCheck();

    void submitApprovalByCode(String contractCode);

    String queryOldContract(String contractCode);

    /**
     * 过滤掉已有合同的中标供应商
     * @param awardedSuppliers 原始中标供应商列表
     * @return 过滤后的中标供应商列表
     */
    List<AwardedSupplierResp> filterAwardedSuppliersWithExistingContract(List<AwardedSupplierResp> awardedSuppliers);

    /**
     * 过滤掉所有标段都已有合同的项目
     * @param projects 原始项目列表
     * @return 过滤后的项目列表
     */
    List<SrmProcurementProject> filterProjectsWithAllSectionsContracted(List<SrmProcurementProject> projects);
    /**
     * 检查指定标段和供应商是否已有有效合同
     * @param sectionId 标段ID
     * @param tenantSupplierId 供应商ID
     * @return 是否已有有效合同
     */
    boolean hasExistingContract(Long sectionId, Long tenantSupplierId);

}

