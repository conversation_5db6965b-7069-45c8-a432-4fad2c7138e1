package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenderContract;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.req.SrmTenderContractQueryReq;
import com.ylz.saas.req.SrmTenderContractSaveReq;
import com.ylz.saas.resp.SrmTenderContractDetailResp;
import com.ylz.saas.resp.SrmTenderContractQueryResp;

import java.lang.reflect.InvocationTargetException;

/**
* <AUTHOR>
* @description 针对表【srm_tender_contract(合同表)】的数据库操作Service
* @createDate 2025-07-21 17:26:10
*/
public interface SrmTenderContractService extends IService<SrmTenderContract> {
    

    void saveContract(SrmTenderContractSaveReq req) throws InvocationTargetException, IllegalAccessException;

    void updateContract(SrmTenderContractSaveReq req);


    Page<SrmTenderContractQueryResp> query(Page<SrmTenderContractQueryResp> page, SrmTenderContractQueryReq req);

    void delete(Long id);

    SrmTenderContractDetailResp queryDetail(String id);

    void revoke(Long id);

    void approveReject(Long id);

    void confirmSign(Long id);

    void contractReject(Long id);

    void approveRevoke(String contractCode );

    void approve(Long id , String type);

    void contractStatusCheck();

    void submitApproval(Long id);

    void submitApprovalByCode(String contractCode);

    String queryOldContract(String contractCode);
}
