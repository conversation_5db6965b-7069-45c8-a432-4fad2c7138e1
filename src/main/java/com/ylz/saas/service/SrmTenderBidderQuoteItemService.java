package com.ylz.saas.service;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmProcurementProjectItem;
import com.ylz.saas.req.*;
import com.ylz.saas.entity.SrmTenderBidderQuoteItem;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.resp.*;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_bidder_quote_item(投标报价明细表)】的数据库操作Service
* @createDate 2025-06-13 14:57:59
*/
public interface SrmTenderBidderQuoteItemService extends IService<SrmTenderBidderQuoteItem> {

    void submitQuote(QuoteSubmitReq req);

    List<Pair<String, String>> getQuoteAgainSupplierList(QuoteAgainSupplierQueryReq req);

    void quoteAgain(QuoteAgainReq req);

    Page<QuoteQueryByMaterialResp> queryQuoteListByMaterial(BidderQuoteQueryReq req, Page<QuoteQueryByMaterialResp> page);

    List<QuoteQueryBySupplierResp> queryQuoteListBySupplier(BidderQuoteQueryReq req);

    /**
     * 根据查询条件查询中标明细列表
     *
     * @param req 查询请求参数
     * @return 中标明细列表（包含动态字段）
     */
    TenderBidderItemsResp queryAwardedItems(com.ylz.saas.req.AwardedItemsQueryReq req);

    /**
     * 根据查询条件查询中标供应商列表
     *
     * @param req 查询请求参数
     * @return 中标供应商列表
     */
    List<com.ylz.saas.resp.AwardedSupplierResp> queryAwardedSuppliers(com.ylz.saas.req.AwardedSuppliersQueryReq req);

    List<SrmProcurementProjectItem> queryMaterialList(QuoteMaterialQueryReq req);

    /**
     * 获取报价数量
     *
     * @param req 查询参数
     * @return 报价数量
     */
    Integer quoteCount(QuoteCountReq req);

    List<String> getQuoteLessThanThreeMaterialCodeList(QuoteLessThanThresholdSupplierQueryReq req);

    List<QuoteDetailQueryResp> queryQuoteDetail(QuoteDetailQueryReq req, Page page);

    /**
     * 查询报价明细（供应商）- 按轮次分组
     *
     * @param req 查询请求参数
     * @return 按轮次分组的供应商报价信息
     */
    com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierResp queryQuoteDetailBySupplier(com.ylz.saas.req.SrmTenderQuoteDetailBySupplierQueryReq req);

    /**
     * 查询报价明细（物料信息）- 按轮次分组
     *
     * @param req 查询请求参数
     * @return 按轮次分组的物料报价信息
     */
    com.ylz.saas.resp.SrmTenderQuoteDetailByMaterialResp queryQuoteDetailByMaterial(com.ylz.saas.req.SrmTenderQuoteDetailByMaterialQueryReq req);

}
