package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmProcessConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【srm_process_config(供应商响应表)】的数据库操作Service
 * @createDate 2025-06-12 16:41:38
 */
public interface SrmProcessConfigService extends IService<SrmProcessConfig> {


    // 业务类型 枚举
    @Getter
    @AllArgsConstructor
    enum BizTypeEnum {

        SRM_PROCUREMENT_PLAN("采购计划生效"),
        SRM_PROCUREMENT_PLAN_CANCEL("采购计划作废"),
        SRM_PROCUREMENT_PROJECT("采购项目生效"),
        SRM_PROCUREMENT_PROJECT_CANCEL("采购项目作废"),
        SRM_SUPPLIER_AUDIT("供应商审核"),
        SRM_INVITE_SUPPLIER_AUDIT("邀请供应商审核"),
        SRM_TENDER_NOTICE_AUDIT("发标信息审核"),
        SRM_TENDER_BID_AUDIT("定标信息审核"),
        SRM_PUBLICITY_AUDITING("中标公示信息审核"),
        SRM_BID_NOTICE_AUDITING("中标公告信息审核"),
        SRM_TENDER_FAILED_AUDIT("流标信息审核"),
        SRM_CHANGE_AUDIT("变更信息审核"),
        SRM_TENDER_DOC_AUDIT("标书/竞谈文件信息审核"),
        SRM_TENDER_CONTRACT_AUDIT("合同审核"),
        SRM_TENDER_CONTRACT_REVOKE_AUDIT("合同作废审核"),
        SRM_TENDER_ENSILAGE_AUDIT("青贮信息审核"),
        SRM_PROCUREMENT_DECISION_AUDIT("采购决策生效审核"),
        SRM_PROCUREMENT_DECISION_INVALID_AUDIT("采购决策作废审核"),
        ;

        private String desc;


        public static List<BizTypeEnum> getApproveTypes() {
            return Arrays.stream(BizTypeEnum.values()).toList();
        }
    }
}
