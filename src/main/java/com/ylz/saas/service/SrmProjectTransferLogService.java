package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.req.HandleTransferReq;
import com.ylz.saas.req.InitiateTransferReq;
import com.ylz.saas.entity.SrmProjectTransferLog;
import com.ylz.saas.vo.PendingTransferProjectVO;

public interface SrmProjectTransferLogService extends IService<SrmProjectTransferLog> {

    /**
     * 发起项目转交
     * @param req 请求参数，包含项目ID列表和接收人ID
     */
    String initiateTransfer(InitiateTransferReq req);


    /**
     * 根据转交批次ID，分页查询待处理的项目列表
     * @param page 分页参数
     * @param batchId 转交批次ID
     * @return 待处理的项目列表
     */
    Page<PendingTransferProjectVO> getPendingListByBatch(Page<PendingTransferProjectVO> page, String batchId);


    /**
     * 处理转交请求
     * @param req
     */
    void handleTransfer(HandleTransferReq req);

}