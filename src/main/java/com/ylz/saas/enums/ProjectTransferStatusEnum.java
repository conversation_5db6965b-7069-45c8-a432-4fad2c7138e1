package com.ylz.saas.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 项目转交状态枚举
 */
@Getter
public enum ProjectTransferStatusEnum {

     PENDING("转交中"),
     ACCEPTED("已接受"),
     REJECTED("已拒绝");

     @JsonValue
     private final String desc;

     ProjectTransferStatusEnum(String desc) {
          this.desc = desc;
     }

     @JsonCreator
     public static ProjectTransferStatusEnum fromDesc(String desc) {
          // 遍历所有枚举常量，找到desc匹配的那个
          return Arrays.stream(ProjectTransferStatusEnum.values())
                  .filter(status -> status.getDesc().equals(desc))
                  .findFirst()
                  .orElse(null);
     }
}