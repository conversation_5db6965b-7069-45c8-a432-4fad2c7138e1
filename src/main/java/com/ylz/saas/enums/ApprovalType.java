package com.ylz.saas.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ApprovalType {

    AUTO("自动发起", 0),
    CUSTOM("指定审批人", 1);

    private final String value;
    private final Integer code;

    private static final Map<String, ApprovalType> CACHE_MAP = EnumSet.allOf(ApprovalType.class)
            .stream()
            .collect(Collectors.toMap(ApprovalType::name, Function.identity()));

    public static ApprovalType geApprovalTypeByValue(String value) {
        return CACHE_MAP.getOrDefault(value.toUpperCase(), ApprovalType.AUTO);
    }
}