package com.ylz.saas.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/26 16:56
 * @Description
 */
@Getter
@AllArgsConstructor
public enum FixedFieldEnum {

    REQUIRE_NO("requireNo", "需求行号"),
    MATERIAL_CODE("materialCode", "物料code"),
    MATERIAL_NAME("materialName", "物料名称"),
    SPEC_MODEL("specModel", "规格参数"),
    UNIT("unit", "单位"),
    REQUIRED_QUANTITY("requiredQuantity", "需求数量"),

    USAGE_LOCATION_ID("usageLocationId", "需求牧场"),
    QUALITY_INDICATOR_ID("qualityIndicatorId", "质量标准"),
    USAGE_LOCATION_REGION("usageLocationRegion", "所在区域"),
    USAGE_LOCATION_ADDRESS("usageLocationAddress", "详细地址"),

    AVAILABLE_QUANTITY("availableQuantity", "可供数量"),
    QUOTE_PRICE("quotePrice", "单价"),
    QUOTE_AMOUNT("quoteAmount", "总价"),

    ;

    private final String code;
    private final String desc;


    public static FixedFieldEnum getByCode(String code) {
        for (FixedFieldEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static boolean isPlanRequireField(FixedFieldEnum fieldEnum) {
        return switch (fieldEnum) {
            case MATERIAL_CODE, MATERIAL_NAME, REQUIRED_QUANTITY, USAGE_LOCATION_ID, QUALITY_INDICATOR_ID -> true;
            default -> false;
        };
    }

    public static boolean isProjectRequireField(FixedFieldEnum fieldEnum) {
        return switch (fieldEnum) {
            case MATERIAL_CODE, MATERIAL_NAME, REQUIRED_QUANTITY, USAGE_LOCATION_ID, QUALITY_INDICATOR_ID -> true;
            default -> false;
        };
    }

    public static List<FixedFieldEnum> getProjectRequireField() {
        return List.of(MATERIAL_CODE, MATERIAL_NAME, REQUIRED_QUANTITY, USAGE_LOCATION_ID, QUALITY_INDICATOR_ID);
    }

    public static boolean isQuoteRequireField(FixedFieldEnum fieldEnum) {
        return switch (fieldEnum) {
            case AVAILABLE_QUANTITY, QUOTE_PRICE, QUOTE_AMOUNT -> true;
            default -> false;
        };
    }

}
