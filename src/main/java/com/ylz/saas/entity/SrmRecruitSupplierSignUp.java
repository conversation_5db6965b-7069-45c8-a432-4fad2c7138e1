package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 供应商招募报名表
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@TableName("srm_recruit_supplier_sign_up")
@Schema(description = "供应商招募报名表")
@TenantTable
public class SrmRecruitSupplierSignUp {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标识（0-正常、1-删除）")
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人ID")
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人ID")
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 招募公告id
     */
    @Schema(description = "招募公告id")
    private Long recruitId;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    private String enterpriseName;

    /**
     * 注册资本 （万元）
     */
    @Schema(description = "注册资本")
    private Long registerCapital;

    /**
     * 统一社会信用代码
     */
    @Schema(description = "统一社会信用代码")
    private String socialCreditCode;

    /**
     * 法人代表
     */
    @Schema(description = "法人代表")
    private String legalPerson;


    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long supplierId;

    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    private LocalDate incorporateDate;

    /**
     * 注册地址
     */
    @Schema(description = "注册地址")
    private String registerAddress;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String contacts;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactsPhone;

    /**
     * 联系邮箱
     */
    @Schema(description = "联系邮箱")
    private String contactsEmail;

    /**
     * 说明
     */
    @Schema(description = "说明")
    private String remark;

    /**
     * 报名ip
     */
    @Schema(description = "报名ip")
    private String ip;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private ApproveStatusEnum approvalStatus;

    /**
     * 是否入库
     */
    @Schema(description = "是否入库")
    private Integer isStored;
}
