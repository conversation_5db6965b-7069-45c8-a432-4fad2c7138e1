package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ylz.saas.enums.ProjectTransferStatusEnum; // 稍后会定义这个枚举
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 采购项目转交流水表 实体类
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@TableName("srm_project_transfer_log")
public class SrmProjectTransferLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 转交批次号，用于关联一次批量操作
     */
    private String transferBatchId;

    /**
     * 关联的采购项目ID
     */
    private Long projectId;

    /**
     * 采购项目编号
     */
    private String projectCode;

    /**
     * 发起转交的用户ID
     */
    private Long senderUserId;

    /**
     * 发起转交的用户名
     */
    private String senderUserName;

    /**
     * 接收人用户ID
     */
    private Long receiverUserId;

    /**
     * 接收人用户名
     */
    private String receiverUserName;

    /**
     * 转交状态 (PENDING, ACCEPTED, REJECTED)
     */
    private ProjectTransferStatusEnum status;

    /**
     * 处理时间（接受或拒绝的时间）
     */
    private LocalDateTime handleTime;

    /**
     * 逻辑删除（0正常 1删除）
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人账号
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间（等同于转交时间）
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateById;

    /**
     * 修改人账号
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;



    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}