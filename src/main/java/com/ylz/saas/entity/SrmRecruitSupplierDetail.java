package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 供应商招募物料/工程表
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@TableName("srm_recruit_supplier_detail")
@Schema(description = "供应商招募物料/工程表")
@TenantTable
public class SrmRecruitSupplierDetail {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标识（0-正常、1-删除）")
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人ID")
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人ID")
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 招募公告id
     */
    @Schema(description = "招募公告id")
    private Long recruitId;

    /**
     * 招募类型 MATERIALS（物资类） - ENGINEERING （工程类）
     */
    @Schema(description = "招募类型")
    private SrmRecruitSupplier.RecruitType recruitType;

    /**
     * 物料id
     */
    @Schema(description = "物料id")
    private Long materialId;

    /**
     * 物料编码
     */
    @Schema(description = "物料编码")
    private String materialCode;

    /**
     * 规格/型号
     */
    @Schema(description = "规格/型号")
    private String spec;

    /**
     * 计量单位
     */
    @Schema(description = "计量单位")
    private String unit;

    /**
     * 物料名称
     */
    @Schema(description = "物料名称")
    private String materialName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 工程服务内容
     */
    @Schema(description = "工程服务内容")
    private String engineeringContext;

    /**
     * 牧场id
     */
    @Schema(description = "牧场id")
    private Long locationId;

    /**
     * 牧场名称
     */
    @Schema(description = "牧场名称")
    private String locationName;

    /**
     * 牧场省份
     */
    @Schema(description = "牧场省份")
    private String locationProvince;

    /**
     * 牧场城市
     */
    @Schema(description = "牧场城市")
    private String locationCity;

    /**
     * 牧场区县
     */
    @Schema(description = "牧场区县")
    private String locationDistrict;

    /**
     * 牧场详细地址
     */
    @Schema(description = "牧场详细地址")
    private String locationAddress;

    /**
     * 计划采购数量
     */
    @Schema(description = "计划采购数量")
    private Long planPurchaseQuantity;

    /**
     * 计划金额
     */
    @Schema(description = "计划金额")
    private BigDecimal planAmount;
}
