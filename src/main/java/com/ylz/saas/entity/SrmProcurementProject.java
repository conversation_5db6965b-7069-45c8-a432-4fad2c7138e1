package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.srm_procurement_decision.entity.SrmProcurementDecisionEntity;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.service.SrmProcurementProjectService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购立项主表
 * @TableName srm_procurement_project
 */
@TableName(value = "srm_procurement_project")
@Data
@TenantTable
public class SrmProcurementProject implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 需求部门ID
     */
    private String purchaseDeptId;

    /**
     * 采购部门ID
     */
    private Long buyerDeptId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 采购业务类型
     */
    private Long serviceTypeId;

    /**
     * 寻源方式
     */
    private BaseServiceTypeFieldService.BuyWayEnum sourcingType;

    /**
     * 采购方式
     */
    private String sourcingMethod;

    /**
     * 邀请方式(PUBLIC-公开、INVITE-邀请)
     */
    private InviteMethodEnum inviteMethod;

    /**
     * 是否邀请回执 0否 1是
     */
    private Integer inviteReceipt;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 项目预算
     */
    private BigDecimal budgetAmount;

    /**
     * 采购意图
     */
    private String purchaseReason;

    /**
     * 供应商要求
     */
    private String supplierRequirement;

    /**
     * 项目概况
     */
    private String projectDesc;

    /**
     * 招标代理机构ID
     */
    private Long agencyOrgId;

    /**
     * 招标代理机构名称
     */
    private String agencyOrgName;

    /**
     * 是否多标段（0-否、1-是）
     */
    private Integer multiSection;

    /**
     * 是否资格预审（0-否、1-是）
     */
    private Integer preQualification;

    /**
     * 报名开始时间
     */
    private LocalDateTime registerStartTime;

    /**
     * 报名截止时间
     */
    private LocalDateTime registerEndTime;

    /**
     * 预审开始时间
     */
    private LocalDateTime preReviewStartTime;

    /**
     * 预审截止时间
     */
    private LocalDateTime preReviewEndTime;

    /**
     * 标书费缴纳开始时间
     */
    private LocalDateTime tenderFeePayStartTime;

    /**
     * 标书费缴纳截止时间
     */
    private LocalDateTime tenderFeePayEndTime;

    /**
     * 标书获取开始时间
     */
    private LocalDateTime tenderFileGainStartTime;

    /**
     * 标书获取截止时间
     */
    private LocalDateTime tenderFileGainEndTime;

    /**
     * 澄清开始时间
     */
    private LocalDateTime tenderClarifyStartTime;

    /**
     * 澄清截止时间
     */
    private LocalDateTime tenderClarifyEndTime;

    /**
     * 报价/投标/竞价开始时间
     */
    private LocalDateTime quoteStartTime;

    /**
     * 报价/投标/竞价截止时间
     */
    private LocalDateTime quoteEndTime;

    /**
     * 开标时间
     */
    private LocalDateTime bidOpenTime;

    /**
     * 项目状态
     */
    private SrmProcurementProjectService.DocumentStatus status;

    /**
     * 审批状态
     */
    private ApproveStatusEnum approveStatus;

    /**
     * 采购进度
     */
    private ProjectProgressStatusEnum progressStatus;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 项目负责人
     */
    @TableField(exist = false)
    private String projectLeaderName;

    /**
     * 业务类型名称
     */
    @TableField(exist = false)
    private String serviceTypeName;

    /**
     * 采购部门名称
     */
    @TableField(exist = false)
    private String buyerDeptName;

    /**
     * 需求部门名称
     */
    @TableField(exist = false)
    private String purchaseDeptName;

    /**
     * 付款方式名称
     */
    @TableField(exist = false)
    private String paymentName;

    /**
     * 账期名称
     */
    @TableField(exist = false)
    private String paymentPeriodName;

    /**
     * 关联计划编号
     */
    @TableField(exist = false)
    private List<String> relationPlanCodeList;
    /**
     * 关联计划列表
     */
    @TableField(exist = false)
    private List<SrmProcurementDecisionEntity.RelationPlanVO> relationPlanList;

    /**
     * 项目成员
     */
    @TableField(exist = false)
    private List<SrmProjectMember> projectMemberList;

    /**
     * 支付方式
     */
    @TableField(exist = false)
    private List<SrmProcurementProjectPayment> projectPaymentList;

    /**
     * 项目标段
     */
    @TableField(exist = false)
    private List<SrmProcurementProjectSection> projectSectionList;

    /**
     * 项目附件
     */
    @TableField(exist = false)
    private List<SrmProjectAttachment> projectAttachmentList;

    /**
     * 生效的公告
     */
    @TableField(exist = false)
    private SrmTenderNotice effectNotice;

    /**
     * 生效的公告id
     */
    @TableField(exist = false)
    private Long effectNoticeId;


    /**
     * 是否需要保证金
     */
    @TableField(exist = false)
    private Boolean needDeposit;

    /**
     * 是否需要标书费
     */
    @TableField(exist = false)
    private Boolean needTenderFee;

    /**
     * 流标id
     */
    @TableField(exist = false)
    private Long failedNoticeId;

    /**
     * 节点状态
     */
    @TableField(exist = false)
    private NodeStatus nodeStatus;

    /**
     * 对应节点审批key（id）
     */
    @TableField(exist = false)
    private String bizKey;

    /**
     * 项目对应的最新的公告信息
     */
    @TableField(exist = false)
    private SrmTenderNotice tenderNotice;

    /**
     * 物料编码
     */
    @TableField(exist = false)
    private String materialCode;

    /**
     * 物料名称
     */
    @TableField(exist = false)
    private String materialName;

    /**
     * 单位
     */
    @TableField(exist = false)
    private String unit;

    /**
     * 需求数量
     */
    @TableField(exist = false)
    private BigDecimal requiredQuantity;

    /**
     * 牧场id
     */
    @TableField(exist = false)
    private Long usageLocationId;
    /**
     * 需求名称
     */
    @TableField(exist = false)
    private String demandName;

    /**
     * 需求牧场中文字段
     */
    @TableField(exist = false)
    private String usageLocationName;

    /**
     * 所在区域字段
     */
    @TableField(exist = false)
    private String usageLocationRegion;

    /**
     * 详细地址
     */
    @TableField(exist = false)
    private String usageLocationAddress;

    /**
     * 拦标价
     */
    @TableField(exist = false)
    private BigDecimal interceptPrice;

    /**
     * 联系人
     */
    @TableField(exist = false)
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField(exist = false)
    private String contactPhone;

    /**
     * 采购执行人中文字段
     */
    @TableField(exist = false)
    private Long executorId;

    /**
     * 创建人
     */
    @TableField(exist = false)
    private String createUser;

    /**
     * 业务流标状态
     */
    @TableField(exist = false)
    private SrmProcessConfigService.BizTypeEnum bizType;

    /**
     * 业务流表中状态
     */
    @TableField(exist = false)
    private ApproveStatusEnum approvalStatus;

    @Getter
    @AllArgsConstructor
    public enum NodeStatus implements Serializable {

        NULL("无"),

        INVITE_AUDITING("邀请函审批中"),
        INVITE_REVOKE("邀请函撤回"),
        INVITE_REJECTED("邀请函驳回"),
        INVITE_APPROVED("邀请函通过"),

        NOTICE_AUDITING("公告审批中"),
        NOTICE_REVOKE("公告撤回"),
        NOTICE_REJECTED("公告驳回"),
        NOTICE_APPROVED("公告通过"),

        ENSILAGE_AUDITING("青贮审批中"),
        ENSILAGE_REVOKE("青贮撤回"),
        ENSILAGE_REJECTED("青贮驳回"),
        ENSILAGE_APPROVED("青贮通过"),

        TENDER_DOC_AUDITING("标书文件审批中"),
        TENDER_DOC_REVOKE("标书文件撤回"),
        TENDER_DOC_REJECTED("标书文件驳回"),
        TENDER_DOC_APPROVED("标书文件通过"),


        CHANGE_AUDITING("变更审批中"),
        CHANGE_REVOKE("变更撤回"),
        CHANGE_REJECTED("变更驳回"),
        CHANGE_APPROVED("变更通过"),

        FAILED_AUDITING("流标审批中"),
        FAILED_REVOKE("流标撤回"),
        FAILED_REJECTED("流标驳回"),
        FAILED_APPROVED("流标通过"),

        AWARD_AUDITING("定标审批中"),
        AWARD_REVOKE("定标撤回"),
        AWARD_REJECTED("定标驳回"),
        AWARD_APPROVED("定标通过"),

        PUBLICITY_AUDITING("中标公示审批中"),
        PUBLICITY_REVOKE("中标公示撤回"),
        PUBLICITY_REJECTED("中标公示驳回"),
        PUBLICITY_APPROVED("中标公示通过"),

        PUBLIC_NOTICE_AUDITING("中标公告审批中"),
        PUBLIC_NOTICE_REVOKE("中标公告撤回"),
        PUBLIC_NOTICE_REJECTED("中标公告驳回"),
        PUBLIC_NOTICE_APPROVED("中标公告通过");


        private final String value;

        /**
         * 获取转换后的邀请状态 如果是null就不做处理
         * @return
         */
        public static NodeStatus getInviteStatus(NodeStatus sourceStatus) {
            return switch (sourceStatus) {
                case INVITE_AUDITING -> NOTICE_AUDITING;
                case INVITE_REVOKE -> NOTICE_REVOKE;
                case INVITE_REJECTED -> NOTICE_REJECTED;
                case INVITE_APPROVED -> NOTICE_APPROVED;
                default -> null;
            };

        }

        /**
         * 获取转换后的邀请状态 如果是null就不做处理
         * @return
         */
        public static NodeStatus getPublicityStatus(NodeStatus sourceStatus) {
            return switch (sourceStatus) {
                case NOTICE_AUDITING -> NOTICE_AUDITING;
                case NOTICE_REVOKE -> NOTICE_REVOKE;
                case NOTICE_REJECTED -> NOTICE_REJECTED;
                case NOTICE_APPROVED -> NOTICE_APPROVED;
                default -> null;
            };

        }

        /**
         * 获取转换后的邀请状态 如果是null就不做处理
         * @return
         */
        public static NodeStatus setInviteStatus(NodeStatus sourceStatus) {
            return switch (sourceStatus) {
                case NOTICE_AUDITING -> INVITE_AUDITING;
                case NOTICE_REVOKE -> INVITE_REVOKE;
                case NOTICE_REJECTED -> INVITE_REJECTED;
                case NOTICE_APPROVED -> INVITE_APPROVED;
                default -> sourceStatus;
            };

        }
    }


}