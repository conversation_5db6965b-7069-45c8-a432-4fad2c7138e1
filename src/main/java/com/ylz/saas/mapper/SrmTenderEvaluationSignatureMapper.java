package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.entity.SrmTenderEvaluationSignature;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 针对表【srm_tender_evaluation_signature(评标汇总签名进度表)】的数据库操作Mapper
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Mapper
public interface SrmTenderEvaluationSignatureMapper extends BaseMapper<SrmTenderEvaluationSignature> {


    /**
     * 根据标段ID和用户ID查询签名记录
     * @param sectionId 标段ID
     * @param userId 用户ID
     * @return 签名记录
     */
    SrmTenderEvaluationSignature getBySectionIdAndUserId(@Param("sectionId") Long sectionId, @Param("userId") Long userId);

    /**
     * 统计标段下已签名的人数
     * @param sectionId 标段ID
     * @return 已签名人数
     */
    int countSignedBySectionId(@Param("sectionId") Long sectionId);

    /**
     * 统计标段下总的评标人员数
     * @param sectionId 标段ID
     * @return 总评标人员数
     */
    int countTotalBySectionId(@Param("sectionId") Long sectionId);

}
