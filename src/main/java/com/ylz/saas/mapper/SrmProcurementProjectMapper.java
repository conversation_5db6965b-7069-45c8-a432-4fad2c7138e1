package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.req.SrmEnsilagePageForAppletReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【srm_procurement_project(采购立项主表)】的数据库操作Mapper
* @createDate 2025-06-11 11:21:09
* @Entity com.ylz.saas.entity.SrmProcurementProject
*/
@Mapper
@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"id\",\"dataSourceField\":\"ID\"}]")
public interface SrmProcurementProjectMapper extends SaasBaseMapper<SrmProcurementProject> {

    /**
     * 供应商分页查询（禁用数据权限控制）
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    @DataPermission(roleEnabled = false, userEnabled = false)
    Page<SrmProcurementProject> selectPageForSupplier(Page<SrmProcurementProject> page,
                                                     @Param("ew") LambdaQueryWrapper<SrmProcurementProject> queryWrapper);

    /**
     * 小程序分页查询（关联多表查询）
     * @param page 分页参数
     * @param req 查询条件
     * @return 分页结果
     */
    @DataPermission(roleEnabled = false, userEnabled = false)
    Page<SrmProcurementProject> pageForApplet(Page<SrmProcurementProject> page, @Param("req") SrmEnsilagePageForAppletReq req);

    /**
     * 小程序分页查询（关联多表查询）提供供方查询
     * @param page 分页参数
     * @param req 查询条件
     * @return 分页结果
     */
    Page<SrmProcurementProject> pageForAppletForSupplier(Page<SrmProcurementProject> page, @Param("req") SrmEnsilagePageForAppletReq req);

}




