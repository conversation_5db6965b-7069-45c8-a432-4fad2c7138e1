package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmRecruitSupplier;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.req.SrmRecruitResultPageReq;
import com.ylz.saas.req.SrmRecruitSupplierPageReq;
import com.ylz.saas.resp.ApproverInfo;
import com.ylz.saas.resp.SrmRecruitResultPageResp;
import com.ylz.saas.resp.SrmRecruitSupplierDetailResp;
import com.ylz.saas.resp.SrmRecruitSupplierPageResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商招募公告表Mapper
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Mapper
public interface SrmRecruitSupplierMapper extends SaasBaseMapper<SrmRecruitSupplier> {

    IPage<SrmRecruitResultPageResp> pageResult(@Param("req") SrmRecruitResultPageReq req, Page<SrmRecruitResultPageResp> page);

    /**
     * 分页查询供应商招募信息
     *
     * @param page 分页参数
     * @param req  查询条件
     * @return 分页结果
     */
    IPage<SrmRecruitSupplierPageResp> selectRecruitSupplierPage(Page<SrmRecruitSupplierPageResp> page, @Param("req") SrmRecruitSupplierPageReq req);

    /**
     * 查询招募小组成员列表（联查用户表和部门表）
     *
     * @param recruitId 招募ID
     * @return 成员列表
     */
    List<SrmRecruitSupplierDetailResp.MemberInfo> selectMemberList(@Param("recruitId") Long recruitId);

    /**
     * 查询物资清单列表
     *
     * @param recruitId 招募ID
     * @return 物资清单列表
     */
    List<SrmRecruitSupplierDetailResp.MaterialInfo> selectMaterialList(@Param("recruitId") Long recruitId);

    /**
     * 查询资质要求列表
     *
     * @param recruitId 招募ID
     * @return 资质要求列表
     */
    List<SrmRecruitSupplierDetailResp.CertificateInfo> selectCertificateList(@Param("recruitId") Long recruitId);

    /**
     * 查询合作媒体列表
     *
     * @param recruitId 招募ID
     * @return 合作媒体列表
     */
    List<SrmRecruitSupplierDetailResp.MediaInfo> selectMediaList(@Param("recruitId") Long recruitId);

    /**
     * 根据用户ID列表查询审批人信息
     *
     * @param userIds 用户ID列表
     * @return 审批人信息列表
     */
    List<ApproverInfo> selectApproverInfoByUserIds(@Param("userIds") List<Long> userIds);
}
