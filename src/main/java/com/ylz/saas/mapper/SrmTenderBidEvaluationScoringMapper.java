package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.entity.SrmTenderBidEvaluationScoring;
import com.ylz.saas.req.SrmTenderBidEvaluationScoringQueryReq;
import com.ylz.saas.req.SrmTenderBidEvaluationSummarizeReq;
import com.ylz.saas.resp.SrmTenderBidEvaluationScoringQueryResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 针对表【srm_tender_bid_evaluation_scoring(评标专家打分)】的数据库操作Mapper
 * <AUTHOR>
 * @createDate 2025-07-09
 */
@Mapper
public interface SrmTenderBidEvaluationScoringMapper extends BaseMapper<SrmTenderBidEvaluationScoring> {

    /**
     * 查询专家对某节点的项目打分情况
     * @param req 查询条件，userId必传
     * @return 打分情况列表
     */
    List<SrmTenderBidEvaluationScoringQueryResp> getExpertScoringByNode(@Param("req") SrmTenderBidEvaluationScoringQueryReq req);

    /**
     * 根据节点名称撤回专家评分结果
     * @param req 查询条件，包含userId、projectId、noticeId、sectionId、nodeName
     * @return 影响的行数
     */
    int revokeScoringResultByNode(@Param("req") SrmTenderBidEvaluationScoringQueryReq req);

    /**
     * 查询节点下所有专家对各个供应商的评审/评分详情
     * @param req 查询条件，包含projectId、noticeId、sectionId、nodeName
     * @return 专家评审/评分详情列表
     */
    List<SrmTenderBidEvaluationScoringQueryResp> getNodeEvaluationDetails(@Param("req") SrmTenderBidEvaluationScoringQueryReq req);


    int countExpertEvaluationCompleted(@Param("noticeId") Long noticeId);

}
