package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.dto.SectionSignatureProgressDto;
import com.ylz.saas.entity.SrmTenderBidEvaluation;
import com.ylz.saas.req.SrmTenderSectionPageReq;
import com.ylz.saas.resp.SrmTenderBidEvaluationResp;
import com.ylz.saas.resp.SrmTenderSectionPageResp;
import com.ylz.saas.resp.SrmTenderEvaluationSummarySupplierResp;
import com.ylz.saas.resp.SrmTenderOfflineEvaluationSummarySupplierResp;
import com.ylz.saas.resp.SrmTenderSupplierReviewDetailQueryResp;
import com.ylz.saas.dto.NodeScoreDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 针对表【srm_tender_bid_evaluation(评标委员会)】的数据库操作Mapper
 * <AUTHOR>
 * @createDate 2025-07-09
 */
@Mapper
public interface SrmTenderBidEvaluationMapper extends BaseMapper<SrmTenderBidEvaluation> {

    /**
     * 根据标段ID查询评标委员会详情（包含成员信息）
     * @param sectionId 标段ID
     * @return 评标委员会详情
     */
    SrmTenderBidEvaluationResp getBySectionIdWithMembers(@Param("sectionId") Long sectionId);

    /**
     * 分页查询项目标段列表
     * @param page 分页参数
     * @param req 查询条件
     * @return 分页结果
     */
    IPage<SrmTenderSectionPageResp> pageSections(Page<SrmTenderSectionPageResp> page, @Param("req") SrmTenderSectionPageReq req);

    /**
     * 批量查询标段的签名进度
     * @param sectionIds 标段ID列表
     * @return 签名进度列表
     */
    List<SectionSignatureProgressDto> batchGetSignatureProgress(@Param("sectionIds") List<Long> sectionIds);

    /**
     * 查询标段下所有供应商基本信息
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @return 供应商基本信息列表
     */
    List<SrmTenderEvaluationSummarySupplierResp> querySupplierBasicInfo(@Param("sectionId") Long sectionId,
                                                                        @Param("noticeId") Long noticeId,
                                                                        @Param("projectId") Long projectId);

    /**
     * 查询供应商评审项汇总
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @param tenantSupplierId 租户供应商ID
     * @return 评审项汇总结果
     */
    String querySupplierReviewSummary(@Param("sectionId") Long sectionId,
                                     @Param("noticeId") Long noticeId,
                                     @Param("projectId") Long projectId,
                                     @Param("tenantSupplierId") Long tenantSupplierId);

    /**
     * 查询供应商各节点评分项分数
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @param tenantSupplierId 租户供应商ID
     * @return 节点分数列表
     */
    List<NodeScoreDto> querySupplierNodeScores(@Param("sectionId") Long sectionId,
                                              @Param("noticeId") Long noticeId,
                                              @Param("projectId") Long projectId,
                                              @Param("tenantSupplierId") Long tenantSupplierId);

    /**
     * 查询供应商投标价格
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @param tenantSupplierId 租户供应商ID
     * @return 投标价格
     */
    BigDecimal querySupplierBidPrice(@Param("sectionId") Long sectionId,
                                    @Param("noticeId") Long noticeId,
                                    @Param("projectId") Long projectId,
                                    @Param("tenantSupplierId") Long tenantSupplierId);

    /**
     * 查询评标节点列表
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @return 节点名称列表
     */
    List<String> queryEvaluationNodes(@Param("sectionId") Long sectionId,
                                     @Param("noticeId") Long noticeId,
                                     @Param("projectId") Long projectId);

    /**
     * 查询线下评标供应商基本信息
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @return 线下评标供应商基本信息列表
     */
    List<SrmTenderOfflineEvaluationSummarySupplierResp> queryOfflineSupplierBasicInfo(@Param("sectionId") Long sectionId,
                                                                                      @Param("noticeId") Long noticeId,
                                                                                      @Param("projectId") Long projectId,
                                                                                      @Param("quoteRound") Integer quoteRound);

    /**
     * 查询供应商评审项详情
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @param tenantSupplierId 租户供应商ID
     * @return 供应商评审项详情
     */
    SrmTenderSupplierReviewDetailQueryResp querySupplierReviewDetail(@Param("sectionId") Long sectionId,
                                                                     @Param("noticeId") Long noticeId,
                                                                     @Param("projectId") Long projectId,
                                                                     @Param("tenantSupplierId") Long tenantSupplierId);

    /**
     * 查询评审项节点列表
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @return 评审项节点名称列表
     */
    List<String> queryReviewNodes(@Param("sectionId") Long sectionId,
                                 @Param("noticeId") Long noticeId,
                                 @Param("projectId") Long projectId);

    /**
     * 查询专家对供应商的评审详情
     * @return 专家评审详情列表
     */
    List<SrmTenderSupplierReviewDetailQueryResp.ExpertReviewDetail> queryExpertReviewDetails(@Param("evaluationId") Long evaluationId);

    /**
     * 查询专家对供应商各评审项的评审结果
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @param tenantSupplierId 租户供应商ID
     * @param userId 专家用户ID
     * @return 节点评审结果列表
     */
    List<SrmTenderSupplierReviewDetailQueryResp.NodeReviewResult> queryExpertNodeReviewResults(@Param("sectionId") Long sectionId,
                                                                                               @Param("noticeId") Long noticeId,
                                                                                               @Param("projectId") Long projectId,
                                                                                               @Param("tenantSupplierId") Long tenantSupplierId,
                                                                                               @Param("userId") Long userId);

    /**
     * 查询组长对供应商各评审项的汇总结果
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @param tenantSupplierId 租户供应商ID
     * @return 组长汇总结果列表
     */
    List<SrmTenderSupplierReviewDetailQueryResp.NodeReviewResult> queryLeaderSummaryResults(@Param("sectionId") Long sectionId,
                                                                                            @Param("noticeId") Long noticeId,
                                                                                            @Param("projectId") Long projectId,
                                                                                            @Param("tenantSupplierId") Long tenantSupplierId);

}
