package com.ylz.saas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.req.SrmEnsilagePageForAppletReq;
import com.ylz.saas.req.SrmProcurementProjectCreateReq;
import com.ylz.saas.req.SrmProcurementProjectPageQueryReq;
import com.ylz.saas.resp.ProcessInstanceStartResp;
import com.ylz.saas.resp.ProjectArchiveResp;
import com.ylz.saas.service.SrmProcurementProjectService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 采购项目
 * <AUTHOR>
 * @Date 2025/6/18 15:55
 * @Description
 */
@RestController
@RequestMapping("/srmProcurementProject")
@RequiredArgsConstructor
public class SrmProcurementProjectController {

    private final SrmProcurementProjectService srmProcurementProjectService;

    /**
     * 新建/编辑采购项目
     */
    @PostMapping("/upsert")
    public R<ProcessInstanceStartResp> upsert(@RequestBody @Validated SrmProcurementProjectCreateReq req) {
        return R.ok(srmProcurementProjectService.upsertAndStartFlow(req));
    }


    /**
     * 分页查询采购立项
     */
    @PostMapping("/pageQuery")
    public R<Page<SrmProcurementProject>> pageQuery(@RequestBody SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        Page<SrmProcurementProject> srmProcurementProjectPage = srmProcurementProjectService.pageQuery(req, page);
        return R.ok(srmProcurementProjectPage);
    }

    /**
     * 分页查询采购方询价单
     */
    @PostMapping("/pageQueryWithNodeStatusPurchase")
    public R<Page<SrmProcurementProject>> pageQueryWithNodeStatusPurchase(@RequestBody SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        Page<SrmProcurementProject> srmProcurementProjectPage = srmProcurementProjectService.pageQueryWithProcessInstance(req, page);
        return R.ok(srmProcurementProjectPage);
    }
    /**
     * 供方采购询价单分页
     */
    @PostMapping("/pageQueryWithNodeStatusSupplier")
    public R<Page<SrmProcurementProject>> pageQueryWithNodeStatusSupplier(@RequestBody SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        Page<SrmProcurementProject> srmProcurementProjectPage = srmProcurementProjectService.pageQueryWithProcessInstanceForSupplier(req, page);
        return R.ok(srmProcurementProjectPage);
    }


    /**
     * 详情
     */
    @GetMapping("/detail")
    public R<SrmProcurementProject> detail(@RequestParam(value = "projectCode") String projectCode) {
        return R.ok(srmProcurementProjectService.detail(projectCode, false));
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    public R<Void> delete(@PathVariable Long id) {
        srmProcurementProjectService.delete(id);
        return R.ok();
    }


    /**
     * 完成项目（归档）
     */
    @PostMapping("/completeProject/{projectCode}")
    public R<Void> completeProject(@PathVariable String projectCode) {
        srmProcurementProjectService.updateProgressStatus(projectCode, ProjectProgressStatusEnum.COMPLETED, true);
        return R.ok();
    }


    /**
     * 获取项目归档信息
     */
    @GetMapping("/getProjectArchive/{projectCode}")
    public R<ProjectArchiveResp> getProjectArchive(@PathVariable String projectCode) {
        return R.ok(srmProcurementProjectService.getProjectArchive(projectCode));
    }


    /**
     * 青贮查询列表
     */
    @PostMapping("/pageEnsilageForWeb")
    public R<Page<SrmProcurementProject>> pageEnsilageForApplet(@RequestBody SrmEnsilagePageForAppletReq req, Page<SrmProcurementProject> page) {
        return R.ok(srmProcurementProjectService.pageForApplet(req, page));
    }

}
