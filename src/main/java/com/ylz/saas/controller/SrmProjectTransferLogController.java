package com.ylz.saas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.req.HandleTransferReq;
import com.ylz.saas.req.InitiateTransferReq;
import com.ylz.saas.service.SrmProjectTransferLogService; // 修改为 SrmProjectTransferLogService
import com.ylz.saas.vo.PendingTransferProjectVO;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/srmProjectTransfer") // 统一的请求路径
@RequiredArgsConstructor
public class SrmProjectTransferLogController {

    private final SrmProjectTransferLogService srmProjectTransferLogService;

    /**
     * 发起项目转交
     *
     * @param req 包含项目ID列表和接收人ID的请求体
     * @return 统一响应体
     */
    @PostMapping("/initiate")
    public R<String> initiateTransfer(@RequestBody @Validated InitiateTransferReq req) {
       String result = srmProjectTransferLogService.initiateTransfer(req);
        return R.ok(result);
    }


    /**
     * 根据转交批次ID，分页查询待处理的项目列表
     *
     * @param page      分页参数
     * @param batchId   转交批次ID，由前端从URL或点击事件中获取
     * @return 分页的待处理项目VO列表
     */
    @GetMapping("/pendingListByBatch") // 接口名更清晰
    @Validated // 开启参数校验
    public R<Page<PendingTransferProjectVO>> getPendingListByBatch(
            Page<PendingTransferProjectVO> page,
            @RequestParam @NotBlank(message = "转交批次ID不能为空") String batchId
    ) {
        return R.ok(srmProjectTransferLogService.getPendingListByBatch(page, batchId));
    }

    /**
     * 处理转交项目
     * @param req 包含转交批次ID、处理动作的请求体
     * @return 统一响应体
     */
    @PostMapping("/handle")
    public R<Void> handleTransfer(@RequestBody @Validated HandleTransferReq req) {
        srmProjectTransferLogService.handleTransfer(req);
        return R.ok(null,"项目转交处理成功");
    }


}