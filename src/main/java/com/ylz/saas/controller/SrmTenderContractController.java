package com.ylz.saas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmTenderContract;
import com.ylz.saas.entity.SrmTenderScoreTemplate;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.req.SrmProcurementProjectPageQueryReq;
import com.ylz.saas.req.SrmTenderContractQueryReq;
import com.ylz.saas.req.SrmTenderContractSaveReq;
import com.ylz.saas.req.SrmTenderScoreTemplateQueryReq;
import com.ylz.saas.resp.SrmTenderContractDetailResp;
import com.ylz.saas.resp.SrmTenderContractQueryResp;
import com.ylz.saas.resp.SrmTenderScoreTemplateDetailResp;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmTenderContractService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.InvocationTargetException;

/**
 * 合同管理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("/srmTenderContract")
@Tag(name = "合同管理", description = "合同相关接口")
@RequiredArgsConstructor
public class SrmTenderContractController {
    private final SrmTenderContractService srmTenderContractService;
    private final SrmProcurementProjectService srmProcurementProjectService;
    //采方操作
    /**
     * 1.新建/8.修改合同
     */
    @PostMapping("/save")
    @Operation(summary = "新建/修改合同", description = "新建/修改合同")
    public R<Boolean> saveContract(@RequestBody SrmTenderContractSaveReq req) throws InvocationTargetException, IllegalAccessException {
        if(req.getId() != null && req.getId() > 0){
            srmTenderContractService.updateContract(req);
        }else {
            srmTenderContractService.saveContract(req);
        }
        return R.ok(true);
    }
    /**
     * 分页查询合同（合同名称、供应商、采购方、签约时间、项目名称模糊搜索、标段ID、供应商ID、采购方ID、项目ID）
     */
    @PostMapping("/query/page")
    @Operation(summary = "分页查询合同", description = "分页查询合同合同名称、供应商、采购方、签约时间、项目名称模糊搜索、标段ID、供应商ID、采购方ID、项目ID）")
    public R<Page<SrmTenderContractQueryResp>> query(@RequestBody SrmTenderContractQueryReq req) {
        Page<SrmTenderContractQueryResp> page = new Page<>(req.getCurrent(), req.getSize());
        return R.ok(srmTenderContractService.query(page, req));
    }

    /**
     * 获取合同详情
     */
    @GetMapping("/detail/{contractCode}")
    @Operation(summary = "查询合同详情", description = "合同信息（基础信息、供方信息、合同清单、附件）+合同正文")
    public R<SrmTenderContractDetailResp> queryDetail(@PathVariable("contractCode") String contractCode) {
        SrmTenderContractDetailResp resp = srmTenderContractService.queryDetail(contractCode);
        return R.ok(resp);
    }


    /**
     * 逻辑删除合同
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "逻辑删除合同", description = "根据ID逻辑删除合同")
    public R<Boolean> deleteContract(@PathVariable("id") Long id) {
        srmTenderContractService.delete(id);
        return R.ok(true);
    }
    /**
     * 2.审批通过
     */
    @PutMapping("/approve/{id}")
    @Operation(summary = "审批通过", description = "根据ID审批通过")
    public R<Boolean> approveContract(@PathVariable("id") Long id) {
        srmTenderContractService.approve(id ,"COMMON");
        return R.ok(true);
    }
    /**
     * 7.作废合同
     */
    @PutMapping("/revoke/{id}")
    @Operation(summary = "作废合同", description = "根据ID作废合同")
    public R<Boolean> revokeContract(@PathVariable("id") Long id) {
        srmTenderContractService.revoke(id);
        return R.ok(true);
    }
    /**
     * 3.审批不通过
     */
    @PutMapping("/approveReject/{id}")
    @Operation(summary = "审批不通过", description = "根据ID审批不通过")
    public R<Boolean> approveRejectContract(@PathVariable("id") Long id) {
        srmTenderContractService.approveReject(id);
        return R.ok(true);
    }
    /**
     * 4.撤回审批
     */
    @PutMapping("/approveRevoke/{contractCode}")
    @Operation(summary = "撤回审批", description = "根据contractCode撤回审批")
    public R<Boolean> approveRevokeContract(@PathVariable("contractCode") String contractCode) {
        srmTenderContractService.approveRevoke(contractCode);
        return R.ok(true);
    }
    /**
     * 5.提交审核
     */
    @PutMapping("/submit/{id}")
    @Operation(summary = "提交审核", description = "根据ID提交审核")
    public R<Boolean> submitApproval(@PathVariable("id") Long id) {
        srmTenderContractService.submitApproval(id);
        return R.ok(true);
    }
    //供方操作

    /**
     * 1.确认+签章合同
     */
    @PutMapping("/confirmSign/{id}")
    @Operation(summary = "确认+签章合同", description = "根据ID确认+签章合同")
    public R<Boolean> confirmSignContract(@PathVariable("id") Long id) {
        srmTenderContractService.confirmSign(id);
        return R.ok(true);
    }
    /**
     * 2.退回合同
     */
    @PutMapping("/reject/{id}")
    @Operation(summary = "退回合同", description = "根据ID退回合同")
    public R<Boolean> rejectContract(@PathVariable("id") Long id) {
        srmTenderContractService.contractReject(id);
        return R.ok(true);
    }
    /**
     * 查询退回修改后旧合同数据（JSON）
     */
    @GetMapping("/queryOldContract/{contractCode}")
    @Operation(summary = "查询退回修改后旧合同数据", description = "查询退回修改后旧合同数据")
    public R<String> queryOldContract(@PathVariable("contractCode") String contractCode) {
        String oldContract = srmTenderContractService.queryOldContract(contractCode);
        return R.ok(oldContract);
    }
    /**
     * 分页查询采购方询价单（过滤供新建时使用）
     */
    @PostMapping("/pageQueryWithNodeStatusPurchase")
    public R<Page<SrmProcurementProject>> pageQueryWithNodeStatusPurchase(@RequestBody SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        Page<SrmProcurementProject> srmProcurementProjectPage = srmProcurementProjectService.pageQueryWithProcessInstance(req, page);

        if (CollectionUtils.isNotEmpty(srmProcurementProjectPage.getRecords())) {
            for (SrmProcurementProject record : srmProcurementProjectPage.getRecords()) {
                changeStatusAndProcess(record, true);
            }
        }
        return R.ok(srmProcurementProjectPage);
    }
    /**
     * 改变状态和流程（SrmProcurementProjectController复制）
     */
    private void changeStatusAndProcess(SrmProcurementProject record,Boolean flag) {
        InviteMethodEnum inviteMethod = record.getInviteMethod();
        if (InviteMethodEnum.INVITE == inviteMethod) {
//    如果是邀请类别
            ProjectProgressStatusEnum progressStatus = record.getProgressStatus();
            ProjectProgressStatusEnum projectProgressStatusEnum = ProjectProgressStatusEnum.setInviteStatus(progressStatus);
            record.setProgressStatus(projectProgressStatusEnum);
            if(flag){
                SrmProcurementProject.NodeStatus nodeStatus = record.getNodeStatus();
                SrmProcurementProject.NodeStatus inviteNodeStatus = SrmProcurementProject.NodeStatus.setInviteStatus(nodeStatus);
                record.setNodeStatus(inviteNodeStatus);
            }

        }


    }
}
