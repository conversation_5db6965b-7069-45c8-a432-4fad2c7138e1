package com.ylz.saas.req;

import com.baomidou.mybatisplus.annotation.*;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.enums.ContractTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SrmTenderContractSaveReq extends WorkFlowReq implements Serializable {
    /**
     * 主键ID(更新时需要)
     */
    private Long id;

    /**
     * 评标结果ID
     */
//    private Long evaluationResultId;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同类型
     */
    private ContractTypeEnum contractType;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 采购方ID
     */
    private String purchaserDeptId;

    /**
     * 采购方名称
     */
    private String purchaserDeptName;

    /**
     * 采购联系人ID
     */
    private Long purchaseContactId;

    /**
     * 采购联系电话
     */
    private String purchaseContactPhone;

    /**
     * 合同有效期起始时间
     */
    private LocalDateTime effectStartDate;

    /**
     * 合同有效期终止时间
     */
    private LocalDateTime effectEndDate;

    /**
     * 签约时间
     */
    private LocalDateTime signDate;

    /**
     * 签约地点
     */
    private String signLocation;

    /**
     * 支付方式ID
     */
//    private Long paymentMethodId;

    /**
     * 账期ID
     */
//    private Long paymentPeriodId;

    /**
     * 履约保证金
     */
    private BigDecimal performanceBond;

    /**
     * 合同含税总金额
     */
    private BigDecimal totalAmount;

    /**
     * 是否为电子章(0-否、1-是)
     */
    private Integer electronicSeal;

    /**
     * 供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 销售负责人
     */
    private String supplierSalesPrincipal;

    /**
     * 销售联系电话
     */
    private String supplierSalesPrincipalPhone;

    /**
     * 收款银行卡ID
     */
    private Long supplierBankCardId;

    /**
     * 收款银行
     */
    private String supplierBankName;

    /**
     * 收款银行账号
     */
    private String supplierBankAccount;

    /**
     * 合同清单
     */
    private List<ContractItem> contractItems;

    /**
     * 合同附件
     */
    private List<SrmProjectAttachment> contractAttachment;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 合同正文
     */
    private String contractBody;

    @Data
    public static class ContractItem implements Serializable {

        /**
         * 投标报价明细ID
         */
        private Long id;

        /**
         * 合同数量
         */
        private BigDecimal contractQuantity;

        /**
         * 合同总价
         */
        private BigDecimal contractAmount;

        /**
         * 支付方式ID
         */
        private Long paymentMethodId;

        @Serial
        private static final long serialVersionUID = 1L;
    }

    @Serial
    private static final long serialVersionUID = 1L;
}