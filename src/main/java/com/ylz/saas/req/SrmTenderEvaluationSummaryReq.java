package com.ylz.saas.req;

import com.ylz.saas.enums.WinnerCandidateOrderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 评标汇总请求类（新版）
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Data
@Schema(description = "评标汇总请求类（新版）")
public class SrmTenderEvaluationSummaryReq implements Serializable {

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评标委员会ID
     */
    @NotNull(message = "评标委员会ID不能为空")
    @Schema(description = "评标委员会ID")
    private Long evaluationId;

    /**
     * 评标报告内容
     */
    @Schema(description = "评标报告内容")
    private String reportContent;

    /**
     * 报告模板ID
     */
    @Schema(description = "报告模板ID")
    private Long reportTemplateId;

    /**
     * 电子签章评标报告
     */
    @Schema(description = "电子签章评标报告")
    private String signedReportContent;

    /**
     * 供应商中标候选人信息列表
     */
    @Schema(description = "供应商中标候选人信息列表")
    private List<SupplierWinnerInfo> supplierWinnerInfoList;

    /**
     * 供应商中标候选人信息
     */
    @Data
    @Schema(description = "供应商中标候选人信息")
    public static class SupplierWinnerInfo implements Serializable {

        /**
         * 租户供应商ID
         */
        @NotNull(message = "租户供应商ID不能为空")
        @Schema(description = "租户供应商ID")
        private Long tenantSupplierId;

        /**
         * 是否推荐中标(0-否、1-是)
         */
        @Schema(description = "是否推荐中标(0-否、1-是)")
        private Integer isRecommendedWinner;

        /**
         * 中标候选顺序
         */
        @Schema(description = "中标候选顺序")
        private WinnerCandidateOrderEnum winnerCandidateOrder;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
