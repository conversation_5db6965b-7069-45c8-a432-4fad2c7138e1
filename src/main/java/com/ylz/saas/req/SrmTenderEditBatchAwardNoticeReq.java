package com.ylz.saas.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 编辑中标通知书
 */
@Data
public class SrmTenderEditBatchAwardNoticeReq {
    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 评价结果ID
     */
    @NotNull(message = "评价结果ID不能为空", groups = {InterfaceFor.class})
    private Long evaluationResultId;

    /**
     * 模板ID
     */
    @NotNull(message = "模板ID不能为空")
    private Long templateId;

    /**
     * 租户供应商ID
     */
    @NotNull(message = "租户供应商ID不能为空")
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空")
    private String supplierName;

    /**
     * 通知书内容
     */
    private String noticeContent;

    /**
     * 业务使用
     */
    public interface ServiceFor{}

    /**
     * controller使用
     */
    public interface InterfaceFor{}

}