package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17 17:08
 * @Description
 */
@Data
public class QuoteAgainReq {

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 最新报价截止时间
     */
    @NotNull(message = "最新报价截止时间不能为空")
    private LocalDateTime latestQuoteEndTime;

    /**
     * 最新开标时间
     */
    @NotNull(message = "最新开标时间不能为空")
    private LocalDateTime bidOpenTime;

    /**
     * 再次报价的供应商ID列表
     */
    @NotNull(message = "再次报价的供应商ID列表不能为空")
    private List<Long> tenantSupplierIds;

}
