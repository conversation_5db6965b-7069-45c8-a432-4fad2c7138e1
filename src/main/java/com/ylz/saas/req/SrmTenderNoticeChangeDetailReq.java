package com.ylz.saas.req;

import com.ylz.saas.enums.ChangeTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 招标公告表
 * @TableName srm_tender_notice
 */
@Data
public class SrmTenderNoticeChangeDetailReq {

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 变更类型
     */
    @NotNull(message = "变更类型不能为空")
    private ChangeTypeEnum changeTypeEnum;

}