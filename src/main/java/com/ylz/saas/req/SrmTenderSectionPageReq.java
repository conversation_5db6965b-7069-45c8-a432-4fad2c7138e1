package com.ylz.saas.req;

import com.ylz.saas.enums.EvaluationSummaryStatusEnum;
import com.ylz.saas.enums.ExpertExtractionStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 标段分页查询请求类
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Data
@Schema(description = "标段分页查询请求类")
public class SrmTenderSectionPageReq implements Serializable {

    /**
     * 当前页
     */
    @Schema(description = "当前页")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小")
    private Long size = 10L;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 项目名称（模糊查询）
     */
    @Schema(description = "项目名称（模糊查询）")
    private String projectName;

    /**
     * 标段名称（模糊查询）
     */
    @Schema(description = "标段名称（模糊查询）")
    private String sectionName;

    /**
     * 专家抽取状态
     */
    @Schema(description = "专家抽取状态")
    private ExpertExtractionStatusEnum expertExtractionStatus;

    /**
     * 汇总状态
     */
    @Schema(description = "汇总状态")
    private EvaluationSummaryStatusEnum summaryStatus;

    /**
     * 操作人（创建评标委员会的人）
     */
    @Schema(description = "操作人（创建评标委员会的人）")
    private String operatorName;

    /**
     * 汇总人（进行评标汇总的人员）
     */
    @Schema(description = "汇总人（进行评标汇总的人员）")
    private String summaryByName;

    private static final long serialVersionUID = 1L;
}
