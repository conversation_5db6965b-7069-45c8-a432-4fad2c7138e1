package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 组长汇总评审结果请求类
 * <AUTHOR>
 * @createDate 2025-07-16
 */
@Data
@Schema(description = "组长汇总评审结果请求类")
public class SrmTenderLeaderSummaryReviewReq implements Serializable {
    /**
     * 招标委员会ID
     */
    @NotNull(message = "招标委员会ID不能为空")
    private Long evaluationId;

    /**
     * 报价轮次
     */
    @NotNull(message = "报价轮次")
    private Integer currentRound;
    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 租户供应商ID
     */
    @NotNull(message = "租户供应商ID不能为空")
    @Schema(description = "租户供应商ID")
    private Long tenantSupplierId;

    /**
     * 评审项汇总结果列表（包含所有评审项，不按节点归纳）
     */
    @Schema(description = "评审项汇总结果列表（包含所有评审项，不按节点归纳）")
    private List<ReviewItemSummary> reviewItemSummaryList;

    /**
     * 评分项节点分数修改列表（按节点分组）
     */
    @Schema(description = "评分项节点分数修改列表（按节点分组）")
    private List<NodeScoreSummary> nodeScoreSummaryList;



    /**
     * 评审项汇总信息
     */
    @Data
    @Schema(description = "评审项汇总信息")
    public static class ReviewItemSummary implements Serializable {

        /**
         * 评分记录ID（更新时必填，新增时为空）
         */
        @Schema(description = "评分记录ID（更新时必填，新增时为空）")
        private Long scoringId;

        /**
         * 评标标准ID（新增时必填）
         */
        @Schema(description = "评标标准ID（新增时必填）")
        private Long standardId;

        /**
         * 评审项汇总结果（1-符合，0-不符合）
         */
        @Schema(description = "评审项汇总结果（1-符合，0-不符合）")
        private Integer reviewSummaryResult;

        /**
         * 汇总结论
         */
        @Schema(description = "汇总结论")
        private String summaryConclusion;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 评分项节点分数汇总信息
     */
    @Data
    @Schema(description = "评分项节点分数汇总信息")
    public static class NodeScoreSummary implements Serializable {

        /**
         * 节点名称
         */
        @Schema(description = "节点名称")
        private String nodeName;

        /**
         * 组长修改的节点总分
         */
        @Schema(description = "组长修改的节点总分")
        private BigDecimal nodeScore;

        /**
         * 汇总结论
         */
        @Schema(description = "汇总结论")
        private String summaryConclusion;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
