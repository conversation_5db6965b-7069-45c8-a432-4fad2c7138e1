package com.ylz.saas.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import java.io.Serializable;

@Data
public class HandleTransferReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 要处理的转交批次ID (一次只处理一个批次)
     */
    @NotBlank(message = "转交批次ID不能为空")
    private String transferBatchId;

    /**
     * 处理动作 (1: 接受, 0: 拒绝)
     */
    @NotNull(message = "处理动作不能为空")
    // 使用正则表达式校验，确保只能是0或1
    @Pattern(regexp = "^[01]$", message = "处理动作参数不合法，必须为0或1") 
    private String action;

}