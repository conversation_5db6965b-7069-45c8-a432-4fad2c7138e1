package com.ylz.saas.req;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/8/8 16:21
 * @Description
 */
@Data
public class WorkFlowReq {


    @NotNull(message = "流程指定标识不能为空（0默认审批：1指定审批人）")
    @Min(value = 0, message = "流程指定标识不能小于0")
    @Max(value = 1, message = "流程指定标识不能大于1")
    private Integer approvalType;

    private List<Long> specialProcessExecutorList;

}
