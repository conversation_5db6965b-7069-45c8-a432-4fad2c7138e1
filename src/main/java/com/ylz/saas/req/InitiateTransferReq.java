package com.ylz.saas.req;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 发起项目转交 请求DTO
 */
@Data
public class InitiateTransferReq implements Serializable {

    /**
     * 要转交的项目ID列表
     */
    @NotEmpty(message = "请至少选择一个要转交的项目")
    private List<Long> projectIds;

    /**
     * 接收人用户ID
     */
    @NotNull(message = "请选择项目接收人")
    private Long receiverUserId;

}