package com.ylz.saas.req;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.ProjectMemberRoleEnum;
import com.ylz.saas.vo.DynamicFieldVo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购立项创建请求对象
 * 用于接收前端采购立项表单数据
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SrmProcurementProjectCreateReq extends WorkFlowReq {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    private String projectName;
    /**
     * 采购业务类型
     */
    @NotNull(message = "采购业务类型不能为空")
    private Long serviceTypeId;
    /**
     * 寻源方式
     */
    @NotNull(message = "寻源方式不能为空")
    private BaseServiceTypeFieldService.BuyWayEnum sourcingType;
    /**
     * 邀请方式
     */
    @NotNull(message = "邀请方式不能为空")
    private InviteMethodEnum inviteMethod;

    /**
     * 是否邀请回执 0否 1是
     */
    private Integer inviteReceipt;

    /**
     * 采购方式
     */
    @NotBlank(message = "采购方式不能为空")
    private String sourcingMethod;

    /**
     * 是否资格预审
     */
    private Integer preQualification;

    /**
     * 需求部门
     */
    @NotNull(message = "需求部门不能为空")
    private String purchaseDeptId;

    /**
     * 采购部门
     */
    @NotNull(message = "采购部门不能为空")
    private Long buyerDeptId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 详细地址
     */
    @Length(max = 50, message = "详细地址长度不能超过50")
    private String address;

    /**
     * 项目预算
     */
    @Min(value = 1L, message = "项目预算不能小于1")
    private BigDecimal budgetAmount;

    /**
     * 采购意图
     */
    @Length(max = 100, message = "采购意图长度不能超过100")
    private String purchaseReason;

    /**
     * 供应商要求
     */
    @Length(max = 100, message = "供应商要求长度不能超过100")
    private String supplierRequirement;

    /**
     * 项目概况
     */
    @Length(max = 300, message = "项目概况长度不能超过300")
    private String projectDesc;

    /**
     * 招标代理机构ID
     */
    private Long agencyOrgId;

    /**
     * 招标代理机构名称
     */
    private String agencyOrgName;

    /**
     * 报名开始时间（可选，启用资质预审时必填）
     */
    private LocalDateTime registerStartTime;
    /**
     * 报名截止时间（可选，启用资质预审时必填）
     */
    private LocalDateTime registerEndTime;
    /**
     * 预审开始时间（可选，启用资质预审时必填）
     */
    private LocalDateTime preReviewStartTime;
    /**
     * 预审截止时间（可选，启用资质预审时必填）
     */
    private LocalDateTime preReviewEndTime;
    /**
     * 标书费缴纳开始时间
     */
    private LocalDateTime tenderFeePayStartTime;

    /**
     * 标书费缴纳截止时间
     */
    private LocalDateTime tenderFeePayEndTime;

    /**
     * 标书获取开始时间
     */
    private LocalDateTime tenderFileGainStartTime;

    /**
     * 标书获取截止时间
     */
    private LocalDateTime tenderFileGainEndTime;

    /**
     * 澄清开始时间
     */
    private LocalDateTime tenderClarifyStartTime;

    /**
     * 澄清截止时间
     */
    private LocalDateTime tenderClarifyEndTime;
    /**
     * 报价/投标/竞价开始时间
     */
    @NotNull(message = "报价/投标/竞价开始时间不能为空")
    private LocalDateTime quoteStartTime;
    /**
     * 报价/投标/竞价截止时间
     */
    @NotNull(message = "报价/投标/竞价截止时间不能为空")
    private LocalDateTime quoteEndTime;
    /**
     * 开标时间
     */
    private LocalDateTime bidOpenTime;

    /**
     * 附件路径
     */
    private List<Attachment> attachmentList;

    /**
     * 支付信息
     */
    @NotNull(message = "支付信息不能为空")
    @Valid
    private List<PaymentInfo> paymentInfoList;

    /**
     * 项目小组成员（必填，至少1人）
     */
    @NotNull(message = "项目小组成员不能为空")
    @Valid
    private List<ProjectMember> projectMembers;

    /**
     * 标段信息
     */
    @NotNull(message = "标段信息不能为空")
    @Valid
    private List<Section> sections;


    /**
     * 支付信息
     */
    @Data
    public static class PaymentInfo {
        /**
         * 支付方式
         */
        @NotNull(message = "支付方式不能为空")
        private Long paymentMethodId;
        /**
         * 账期ID
         */
        private Long paymentPeriodId;
        /**
         * 账期备注
         */
        @Length(max = 100, message = "账期备注长度不能超过100")
        private String paymentRemark;
    }

    /**
     * 项目成员
     */
    @Data
    public static class ProjectMember {
        /**
         * 项目角色
         */
        @NotNull(message = "项目角色不能为空")
        private ProjectMemberRoleEnum role;
        /**
         * 用户ID
         */
        @NotNull(message = "用户ID不能为空")
        private Long userId;
        /**
         * 联系电话
         */
        @NotBlank(message = "联系电话不能为空")
        private String contactPhone;

        private Long deptId;

        private Long tenantId;
    }

    /**
     * 标段
     */
    @Data
    public static class Section {
        /**
         * 标段名称
         */
        @Length(max = 30, message = "标段名称长度不能超过30")
        private String sectionName;
        /**
         * 标段编号
         */
        @Length(max = 30, message = "标段编号长度不能超过30")
        private String sectionCode;

        /**
         * 项目清单
         */
        @NotNull(message = "项目清单不能为空")
        private List<ProjectItem> projectItems;

    }


    @Data
    public static class ProjectItem {
        /**
         * 采购计划ID
         */
        private Long planId;
        /**
         * 需求行号
         */
        private String requireNo;

        /**
         * 动态字段
         */
        @NotNull(message = "动态字段不能为空")
        private List<DynamicFieldVo> dynamicFieldList;

    }


    @Data
    public static class Attachment {

        private String fileName;

        private String filePath;


    }

} 