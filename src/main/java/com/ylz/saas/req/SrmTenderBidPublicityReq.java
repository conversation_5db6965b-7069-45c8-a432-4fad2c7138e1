package com.ylz.saas.req;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 定标公示
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SrmTenderBidPublicityReq extends WorkFlowReq{
    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 邀请供应商
     */
    @Valid
    @NotNull(message = "请选择邀请供应商！")
    @Size(min = 1, message = "请选择邀请供应商！")
    private List<InviteSupplierReq> inviteSupplierList;

    /**
     * 变更中标公示--是否发布中标公示
     */
    @NotNull(message = "请选择是否发布中标公示！")
    private Boolean isPublicity;

    /**
     * 变更中标公示--公示开始时间
     */
    private LocalDateTime publicityStartTime;

    /**
     * 变更中标公示--公示结束时间
     */
    private LocalDateTime publicityEndTime;

    /**
     * 公示标题
     */
    private String publicityTitle;

    /**
     * 引用模版ID
     */
    private Long publicityTemplateId;

    /**
     * 公示内容
     */
    private String publicityContent;

    /**
     * 附件信息
     */
    private List<AttachmentInfoReq> attachmentInfos;


}