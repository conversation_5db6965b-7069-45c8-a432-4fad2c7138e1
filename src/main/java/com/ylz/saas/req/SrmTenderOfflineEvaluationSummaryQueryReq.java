package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 线下评标汇总查询请求类
 * <AUTHOR>
 * @createDate 2025-07-14
 */
@Data
@Schema(description = "线下评标汇总查询请求类")
public class SrmTenderOfflineEvaluationSummaryQueryReq implements Serializable {

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    private static final long serialVersionUID = 1L;
}
