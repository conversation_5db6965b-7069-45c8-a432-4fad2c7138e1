package com.ylz.saas.req;

import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.BidsSegmentTypeEnum;
import com.ylz.saas.enums.ChangeTypeEnum;
import com.ylz.saas.enums.TenderWayEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 招标公告表
 * @TableName srm_tender_notice
 */
@Data
public class SrmTenderNoticeChangeAddReq implements Serializable {
    /**
     * 变更数据id
     */
    private Long id;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 变更类型
     */
    @NotNull(message = "变更类型不能为空")
    private ChangeTypeEnum changeTypeEnum;

    /**
     * 变更中标公示--是否发布中标公示
     */
    private Boolean isPublicity;

    /**
     * 是否发布中标公告
     */
    private Boolean isPublishNotice;

    /**
     * 变更中标公示--公示开始时间
     */
    private LocalDateTime publicityStartTime;

    /**
     * 变更中标公示--公示结束时间
     */
    private LocalDateTime publicityEndTime;

    /**
     * 变更中标公示--引用模版ID
     */
    private Long publicityTemplateId;

    /**
     * 变更中标公示/公告--邀请供应商
     */
    private List<InviteSupplierReq> inviteSupplierList;

    /**
     * 变更中标公告--公告开始时间
     */
    private LocalDateTime publishNoticeStartTime;

    /**
     * 变更中标公告--公告结束时间
     */
    private LocalDateTime publishNoticeEndTime;

    /**
     * 变更中标公告--引用模版ID
     */
    private Long noticeTemplateId;

    /**
     * 变更原因
     */
    private String changeDigest;

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公示标题
     */
    private String publicityTitle;

    /**
     * 邀请函标题
     */
    private String inviteTitle;

    /**
     * 招标公告内容（富文本）
     */
    private String noticeContent;

    /**
     * 开标方式
     */
    private TenderWayEnum tenderWay;

    /**
     * 开标地点
     */
    private String bidOpeningAddress;

    /**
     * 采购时间要求
     */
    private PurchaseDateDemand purchaseDateDemand;

    /**
     * 开标人
     */
    private Long bidOpener;

    /**
     * 报价要求
     */
    private QuotationDemand quotationDemand;

    /**
     * 资质要求、报名响应条件、保证金设置
     */
    private List<BidsSegment> bidsSegments;

    /**
     * 报价须知
     */
    private String quotationNotice;

    /**
     * 评审规则(COMPREHENSIVE-综合评标低价法、LOWEST_PRICE-最低价法、COMPREHENSIVE_SCORE-综合评分法、HIGHEST_PRICE-最高价法)
     */
    private String evaluationMethod;

    /**
     * 联系方式
     */
    private ContactInfo contactInfo;

    /**
     * 附件信息
     */
    private List<AttachmentInfoReq> attachmentInfos;

    /**
     * 定标信息--物料信息
     */
    private List<ProjectItem> projectItemList;

    /**
     * 定标信息--定标模板id
     */
    private Long awardTemplateId;

    /**
     * 定标报告内容
     */
    private String awardReportContent;

    /**
     * 定标报告时间
     */
    private LocalDateTime awardReportTime;

    /**
     * 变更审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum approvedStatus;

    /**
     * 文件递交地址
     */
    private String fileSubmissionAddress;

    /**
     * 标书费是否收取
     */
    private Boolean bidFeeCollection;

    /**
     * 投标须知
     */
    private String biddingNotice;

    /**
     * 文件获取开始时间
     */
    private LocalDateTime fileObtainStartTime;

    /**
     * 文件获取截止时间
     */
    private LocalDateTime fileObtainEndTime;

    /**
     * 标书费缴纳开始时间
     */
    private LocalDateTime bidDocPayStartTime;

    /**
     * 标书费缴纳截止时间
     */
    private LocalDateTime bidDocPayEndTime;

    /**
     * 报价开始时间
     */
    private LocalDateTime quoteStartTime;

    /**
     * 报价截止时间
     */
    private LocalDateTime quoteEndTime;

    /**
     * 评标标准
     */
    private List<SrmNegotiationAddReq.EvaluationStandardInfo> evaluationStandardInfos;

    /**
     * 需求名称
     */
    private String demandName;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料名称
     */
    private String materialCode;

    /**
     * 需求牧场id
     */
    private Long usageLocationId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 需求数量
     */
    private BigDecimal requiredQuantity;

    /**
     * 需求单位
     */
    private String unit;

    /**
     * 拦标价
     */
    private BigDecimal interceptPrice;

    /**
     * 线下通知供应商
     */
    private Boolean offlineNoticeSupplier;

    /**
     * 供货时间段开始
     */
    private LocalDateTime supplyStartTime;

    /**
     * 供货时间段结束
     */
    private LocalDateTime supplyEndTime;

    /**
     * 是否收取保证金
     */
    private Boolean needDeposit;

    /**
     * 要求名称/资质证件ID
     */
    private String requirementName;

    /**
     * 要求内容/补充说明/金额银行信息（json）
     */
    private String requirementContent;

    /**
     * 保证金设置--保证金额
     */
    private BigDecimal guaranteeAmount;

    /**
     * 保证金设置--付款账号
     */
    private String payAccount;

    /**
     * 保证金设置--付款银行
     */
    private String payBank;

    /**
     * 保证金设置--付款开户行
     */
    private String openAccountBank;

    /**
     * 是否牛信支付
     */
    private Boolean isNxPay;

    /**
     * 采购联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 固定电话
     */
    private String contactFixedPhone;

    /**
     * 电子邮件
     */
    private String contactEmail;

    /**
     * 采购执行人id
     */
    private Long executorId;

    /**
     * 报价有效期
     */
    private Integer quoteValidity;

    /**
     * 付款方式
     */
    private String projectPaymentStr;

    /**
     * 备注
     */
    private String remark;

    /**
     * 邀请供应商--供应商信息
     */
    private List<SrmTenderNoticeAddReq.InviteSuppliers> inviteSuppliers;

    @Data
    public static class ProjectItem implements Serializable {
        /**
         * 主键ID
         */
        private Long id;

        /**
         * 采购立项ID
         */
        private Long projectId;

        /**
         * 标段ID
         */
        private Long sectionId;

        /**
         * 租户供应商ID
         */
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        private String supplierName;

        /**
         * 服务类型ID
         */
        private Long serviceTypeId;

        /**
         * 寻源方式(INQUIRY-询价、FAST_INQUIRY-快速询价、BIDDING-竞价、TENDER-招标采购、COMPETITIVE_NEGOTIATION-竞争性谈判、DIRECT-直接委托)
         */
        private String sourcingType;

        /**
         * 物料编码
         */
        private String materialCode;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 规格型号
         */
        private String specModel;

        /**
         * 单位
         */
        private String unit;

        /**
         * 需求数量
         */
        private BigDecimal requiredQuantity;

        /**
         * 采购计划ID
         */
        private Long planId;

        /**
         * 需求行号
         */
        private String requireNo;

        /**
         * 备注
         */
        private String remark;

        /**
         * 中标数量
         */
        private BigDecimal bidQuantity;

        /**
         * 是否中标
         */
        private Boolean isBid;

        /**
         * 支付方式id
         */
        private Long projectPaymentId;

        /**
         * 中标金额
         */
        private BigDecimal bidAmount;

        /**
         * 中标数量
         */
        private BigDecimal awardedQuantity;
    }

    /**
     * 报价要求
     */
    @Data
    public static class QuotationDemand implements Serializable {
        /**
         * 项目所在地区-省
         */
        private String province;

        /**
         * 项目所在地区-市
         */
        private String city;

        /**
         * 项目所在地区-区
         */
        private String district;

        /**
         * 项目详细地址
         */
        private String address;

        /**
         * 报价是否含税（0-否、1-是）
         */
        private Integer includeTax;

        /**
         * 发票要求(SPECIAL-专票、NORMAL-普票、NONE-无要求)
         */
        private String certificateType;

    }

    /**
     * 采购时间要求
     */
    @Data
    public static class PurchaseDateDemand implements Serializable {

        /**
         * 报名截止开始时间
         */
        private LocalDateTime registerStartTime;

        /**
         * 报名截止时间
         */
        private LocalDateTime registerEndTime;

        /**
         * 资格审核开始时间
         */
        private LocalDateTime auditStartTime;

        /**
         * 资格审核截止时间
         */
        private LocalDateTime auditEndTime;

        /**
         * 报价/投标/竞价开始时间
         */
        private LocalDateTime quoteStartTime;

        /**
         * 报价/投标/竞价截止时间
         */
        private LocalDateTime quoteEndTime;

        /**
         * 开标时间
         */
        private LocalDateTime bidOpenTime;

        /**
         * 开标人
         */
        private Long bidOpener;

        /**
         * 文件递交地址
         */
        private String fileSubmissionAddress;

        /**
         * 文件获取开始时间
         */
        private LocalDateTime fileObtainStartTime;

        /**
         * 文件获取结束时间
         */
        private LocalDateTime fileObtainEndTime;

        /**
         * 投标缴费开始时间
         */
        private LocalDateTime bidDocPayStartTime;

        /**
         *  投标缴费截止时间
         */
        private LocalDateTime bidDocPayEndTime;
    }

    /**
     * 联系方式
     */
    @Data
    public static class ContactInfo implements Serializable {
        /**
         * 采购联系人
         */
        private String contactPerson;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 固定电话
         */
        private String contactFixedPhone;

        /**
         * 电子邮件
         */
        private String contactEmail;
    }

    /**
     * 标段：资质要求、报名响应条件、保证金设置
     */
    @Data
    public static class BidsSegment implements Serializable {

        /**
         * 要求类型(QUALIFICATION-资质要求、CONDITION-响应条件、FEE-费用、QUOTE_NOTICE-报价须知)
         */
        private BidsSegmentTypeEnum requirementType;

        /**
         * 标段ID
         */
        private Long sectionId;

        /**
         * 要求名称/资质证件ID
         */
        private String requirementName;

        /**
         * 要求内容/补充说明/金额银行信息（json）
         */
        private String requirementContent;

        /**
         * 保证金设置--保证金收取，不收取
         */
        private Boolean amountSet;

        /**
         * 保证金设置--保证金信息
         */
        private FeeInfo feeInfo;

        /**
         * 标书费相关信息
         */
        private BidFeeInfoReq bidFeeInfo;

        /**
         * 附件信息
         */
        private List<AttachmentInfoReq> attachmentInfos;

        /**
         * 中标公示--供应商信息
         */
        private List<InviteSupplierReq> inviteSupplierList;

    }

    /**
     * 保证金信息
     */
    @Data
    public static class FeeInfo implements Serializable {
        /**
         * 保证金设置--保证金额
         */
        private BigDecimal guaranteeAmount;

        /**
         * 保证金设置--付款账号
         */
        private String payAccount;

        /**
         * 保证金设置--付款银行
         */
        private String payBank;

        /**
         * 保证金设置--付款开户行
         */
        private String openAccountBank;
    }

}