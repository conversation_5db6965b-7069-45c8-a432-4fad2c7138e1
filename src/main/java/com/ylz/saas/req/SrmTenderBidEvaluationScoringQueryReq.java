package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 专家打分查询请求类
 */
@Data
@Schema(description = "专家打分查询请求类")
public class SrmTenderBidEvaluationScoringQueryReq implements Serializable {

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评价委员会ID
     */
    @NotNull(message = "评价委员会ID不能为空")
    private Long evaluationId;

    /**
     * 专家用户ID（必传）
     */
//    @NotNull(message = "专家用户ID不能为空")
    @Schema(description = "专家用户ID（必传）")
    private Long userId;

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 节点名称
     */
    @Schema(description = "节点名称")
    private String nodeName;

    /**
     * 供应商ID（可选）
     */
    @Schema(description = "供应商ID（可选）")
    private Long tenantSupplierId;

    private static final long serialVersionUID = 1L;
}
