package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 在线报名结果查询
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Schema(description = "在线报名结果查询请求类")
public class SrmRecruitResultPageReq implements Serializable {

    /**
     * 招募公告id
     */
    @Schema(description = "招募公告id")
    @NotNull(message = "招募公告id不能为空")
    private Long recruitId;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long supplierId;


    @Serial
    private static final long serialVersionUID = 1L;
}
