package com.ylz.saas.req;

import com.ylz.saas.enums.ProjectMemberRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 评标委员会请求类
 */
@Data
@Schema(description = "评标委员会请求类")
public class SrmTenderBidEvaluationReq implements Serializable {

    /**
     * 主键ID（更新时需要）
     */
    @Schema(description = "主键ID（更新时需要）")
    private Long id;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评标委员会名称
     */
    @NotBlank(message = "评标委员会名称不能为空")
    @Schema(description = "评标委员会名称")
    private String name;

    /**
     * 评标地址
     */
//    @NotBlank(message = "评标地址不能为空")
    @Schema(description = "评标地址")
    private String address;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空")
    @Schema(description = "联系人")
    private String contacts;

    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话不能为空")
    @Schema(description = "联系人电话")
    private String contactsPhone;


//    /**
//     * 审核状态
//     */
//    @Schema(description = "审核状态")
//    private String approveStatus;

    /**
     * 评标委员会成员列表
     */
    @Valid
    @Schema(description = "评标委员会成员列表")
    private List<EvaluationMember> memberList;

    /**
     * 评标委员会成员
     */
    @Data
    @Schema(description = "评标委员会成员")
    public static class EvaluationMember implements Serializable {

        /**
         * 用户ID
         */
        @NotNull(message = "用户ID不能为空")
        @Schema(description = "用户ID")
        private Long userId;

        /**
         * 角色
         *     EVALUATION_LEADER "评标小组负责人"),
         *     EVALUATION_MEMBER "评标小组成员"),
         */
        @NotNull(message = "角色不能为空")
        @Schema(description = "角色")
        private ProjectMemberRoleEnum role;

        /**
         * 联系电话
         */
        @NotBlank(message = "联系电话不能为空")
        @Schema(description = "联系电话")
        private String contactPhone;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
