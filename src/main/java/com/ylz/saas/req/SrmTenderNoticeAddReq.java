package com.ylz.saas.req;

import com.ylz.saas.enums.BidsSegmentTypeEnum;
import com.ylz.saas.enums.TenderWayEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 招标公告表
 * @TableName srm_tender_notice
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SrmTenderNoticeAddReq extends WorkFlowReq implements Serializable {
    /**
     * 公告标题
     */
    @NotBlank(message = "公告标题不能为空")
    private String noticeTitle;

    /**
     * 引用模版ID
     */
    @NotNull(message = "引用模版ID不能为空")
    private Long noticeTemplateId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 公告内容（富文本）
     */
    private String noticeContent;

    /**
     * 采购时间要求
     */
    private PurchaseDateDemand purchaseDateDemand;

    /**
     * 报价要求
     */
    private QuotationDemand quotationDemand;

    /**
     * 标书费是否收取
     */
    private Boolean bidFeeCollection;

    /**
     * 资质要求、报名响应条件、保证金设置
     */
    private List<BidsSegment> bidsSegments;

    /**
     * 报价须知
     */
    private String quotationNotice;

    /**
     * 评审规则(COMPREHENSIVE-综合评标低价法、LOWEST_PRICE-最低价法、COMPREHENSIVE_SCORE-综合评分法、HIGHEST_PRICE-最高价法)
     */
    private String evaluationMethod;

    /**
     * 联系方式
     */
    private ContactInfo contactInfo;

    /**
     * 附件信息
     */
    private List<AttachmentInfoReq> attachmentInfos;

    /**
     * 开标方式
     */
    private TenderWayEnum tenderWay;

    /**
     * 开标地点
     */
    private String bidOpeningAddress;


    /**
     * 报价要求
     */
    @Data
    public static class QuotationDemand implements Serializable{
        /**
         * 项目所在地区-省
         */
        private String province;

        /**
         * 项目所在地区-市
         */
        private String city;

        /**
         * 项目所在地区-区
         */
        private String district;

        /**
         * 项目详细地址
         */
        private String address;

        /**
         * 报价是否含税（0-否、1-是）
         */
        private Integer includeTax;

        /**
         * 发票要求(SPECIAL-专票、NORMAL-普票、NONE-无要求)
         */
        private String certificateType;
    }

    /**
     * 采购时间要求
     */
    @Data
    public static class PurchaseDateDemand implements Serializable{

        /**
         * 报名截止开始时间
         */
        private LocalDateTime registerStartTime;

        /**
         * 报名截止时间
         */
        private LocalDateTime registerEndTime;

        /**
         * 资格审核开始时间
         */
        private LocalDateTime auditStartTime;

        /**
         * 资格审核截止时间
         */
        private LocalDateTime auditEndTime;

        /**
         * 报价/投标/竞价开始时间
         */
        private LocalDateTime quoteStartTime;

        /**
         * 报价/投标/竞价截止时间
         */
        private LocalDateTime quoteEndTime;

        /**
         * 开标时间
         */
        private LocalDateTime bidOpenTime;

        /**
         * 开标人
         */
        private Long bidOpener;

        /**
         * 文件递交地址
         */
        private String fileSubmissionAddress;

        /**
         * 文件获取开始时间
         */
        private LocalDateTime fileObtainStartTime;

        /**
         * 文件获取结束时间
         */
        private LocalDateTime fileObtainEndTime;

        /**
         * 投标缴费开始时间
         */
        private LocalDateTime bidDocPayStartTime;

        /**
         *  投标缴费截止时间
         */
        private LocalDateTime bidDocPayEndTime;
    }

    /**
     * 联系方式
     */
    @Data
    public static class ContactInfo implements Serializable {
        /**
         * 采购联系人
         */
        private String contactPerson;

        /**
         * 联系电话
         */
        private String contactPhone;

        /**
         * 固定电话
         */
        private String contactFixedPhone;

        /**
         * 电子邮件
         */
        private String contactEmail;
    }

    /**
     * 标段：资质要求、报名响应条件、保证金设置
     */
    @Data
    public static class BidsSegment implements Serializable {

        /**
         * 要求类型(QUALIFICATION-资质要求、CONDITION-响应条件、FEE-费用、QUOTE_NOTICE-报价须知、INVITE_SUPPLIERS-邀请供应商)
         */
        private BidsSegmentTypeEnum requirementType;

        /**
         * 标段ID
         */
        private Long sectionId;

        /**
         * 要求名称/资质证件ID
         */
        private String requirementName;

        /**
         * 要求内容/补充说明/金额银行信息（json）
         */
        private String requirementContent;

        /**
         * 保证金设置--保证金收取，不收取
         */
        private Boolean amountSet;

        /**
         * 保证金设置--保证金信息
         */
        private FeeInfo feeInfo;

        /**
         * 投标保证金信息
         */
        private BidFeeInfoReq bidFeeInfo;

        /**
         * 邀请供应商--供应商信息
         */
        private List<InviteSuppliers> inviteSuppliers;

    }

    /**
     * 邀请供应商
     */
    @Data
    public static final class InviteSuppliers implements Serializable {

        /**
         * 标段ID
         */
        private Long sectionId;

        /**
         * 租户供应商ID
         */
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        private String supplierName;

        /**
         * 联系人名称
         */
        private String contactName;

        /**
         * 联系人手机
         */
        private String contactPhone;
    }

    /**
     * 保证金信息
     */
    @Data
    public static class FeeInfo implements Serializable {
        /**
         * 保证金设置--保证金额
         */
        private BigDecimal guaranteeAmount;

        /**
         * 保证金设置--付款账号
         */
        private String payAccount;

        /**
         * 保证金设置--付款银行
         */
        private String payBank;

        /**
         * 保证金设置--付款开户行
         */
        private String openAccountBank;
    }

}