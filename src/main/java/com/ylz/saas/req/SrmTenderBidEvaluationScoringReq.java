package com.ylz.saas.req;

import com.ylz.saas.entity.SrmTenderBidEvaluationScoring;
import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 评标专家打分请求类
 */
@Data
@Schema(description = "评标专家打分请求类")
public class SrmTenderBidEvaluationScoringReq implements Serializable {

    /**
     * 主键ID（更新时需要）
     */
    @Schema(description = "主键ID（更新时需要）")
    private Long id;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 专家用户id
     */
    @NotNull(message = "专家用户id不能为空")
    @Schema(description = "专家用户id")
    private Long userId;

    /**
     * 评分详情id
     */
    @NotNull(message = "评分详情id不能为空")
    @Schema(description = "评分详情id")
    private Long scoringDetailId;

    /**
     * 投标响应表ID
     */
    @Schema(description = "投标响应表ID")
    private Long responseId;

    /**
     * 供应商id
     */
    @NotNull(message = "供应商id不能为空")
    @Schema(description = "供应商id")
    private Long tenantSupplierId;

    /**
     * 分值（评分项）
     */
    @Schema(description = "分值（评分项）")
    private Integer score;



    /**
     * 是否符合（评审项）
     */
    @Schema(description = "是否符合（评审项）")
    private Integer isConform;

    /**
     * 类型（评审项、评分项）
     */
    @NotBlank(message = "类型不能为空")
    @Schema(description = "类型（评审项、评分项）")
    private String type;

    /**
     * 评审结论
     */
    @Schema(description = "评审结论")
    private String conclusion;

    private static final long serialVersionUID = 1L;
}
