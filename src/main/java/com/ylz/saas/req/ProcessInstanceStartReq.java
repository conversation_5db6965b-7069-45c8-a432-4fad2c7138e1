package com.ylz.saas.req;

import com.ylz.saas.service.SrmProcessConfigService;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/12 16:22
 * @Description
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProcessInstanceStartReq extends WorkFlowReq {

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空")
    private SrmProcessConfigService.BizTypeEnum bizType;

    /**
     * 业务key
     */
    @NotBlank(message = "业务key不能为空")
    private String bizKey;


    /**
     * 业务主流程id
     */
    private Long bizId;

    /**
     * 设置json参数，顺序必须与流程定义中json的参数（%s）顺序一致
     */
    private List<Object> args;
}