package com.ylz.saas.req;

import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import lombok.Data;

import java.util.List;

/**
 * 青贮信息查询列表，小程序使用
 */
@Data
public class SrmEnsilagePageForAppletReq {

    /**
     * 项目编号、物料名称，需求名称，需求牧场
     */
    private String searchContent;

    /**
     * 项目进度
     */
    private List<ProjectProgressStatusEnum> progressStatus;

    /**
     *项目状态
     */
    private List<ApproveStatusEnum> approveStatus;

    /**
     * 节点状态
     */
    private List<SrmProcurementProject.NodeStatus> nodeStatus;

}
