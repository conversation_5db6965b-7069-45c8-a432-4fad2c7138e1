package com.ylz.saas.req;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.enums.ViewMethodEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 青贮信息查询列表，小程序使用
 */
@Data
public class SrmEnsilagePageForAppletReq {

    /**
     * 项目编号、物料名称，需求名称，需求牧场
     */
    private String searchContent;

    /**
     * 寻源方式
     *  XJCG("询价采购"),
     *         JZTP("竞争性谈判"),
     *         ZJWT("直接委托"),
     *         JJCG("竞价采购"),
     *         KSXJ("快速询价"),
     *         QZXJ("青贮询价"),
     *         ZB("招标");
     */
    private BaseServiceTypeFieldService.BuyWayEnum sourcingType;

    /**
     * 项目进度
     */
    private List<ProjectProgressStatusEnum> progressStatus;

    /**
     *项目状态
     */
    private List<ApproveStatusEnum> approveStatus;

    /**
     * 审批状态（节点状态）
     */
    private List<SrmProcurementProject.NodeStatus> nodeStatus;

    /**
     * 报价开始时间
     */
    private LocalDateTime quoteStartTime;

    /**
     * 报价截止时间
     */
    private LocalDateTime quoteEndTime;

    /**
     * 供方枚举，采方枚举
     *  PURCHASE, // 采购方
     *     SUPPLIER, // 供应方
     */
    private ViewMethodEnum viewMethod;


}
