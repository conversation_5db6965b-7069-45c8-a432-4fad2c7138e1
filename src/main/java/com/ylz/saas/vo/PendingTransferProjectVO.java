package com.ylz.saas.vo;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.service.SrmProcurementProjectService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 待处理的转交项目
 */
@Data
@Schema(description = "待处理的转交项目")
public class PendingTransferProjectVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 转交批次ID
     */
    @Schema(description = "转交批次")
    private String transferBatchId;
    /**
     * 发起人名称
     */
    @Schema(description = "发起人名称")
    private String senderUserName;
    /**
     * 发起时间
     */
    @Schema(description = "发起时间")
    private LocalDateTime transferTime;
    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private Long projectId;
    /**
     * 项目编码
     */
    @Schema(description = "项目编码")
    private String projectCode;
    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String projectName;
    /**
     * 采购业务类型名称
     */
    @Schema(description = "采购业务类型名称")
    private String serviceTypeName;
    /**
     * 寻源方式
     */
    @Schema(description = "寻源方式")
    private BaseServiceTypeFieldService.BuyWayEnum sourcingType;
    /**
     * 采购方式
     */
    @Schema(description = "采购方式")
    private String sourcingMethod;
    /**
     * 邀请方式(PUBLIC-公开、INVITE-邀请)
     */
    @Schema(description = "邀请方式")
    private InviteMethodEnum inviteMethod;


    /**
     * 需求部门名称
     */
    @Schema(description = "需求部门名称")
    private String purchaseDeptName;

    /**
     * 采购部门
     */
    @Schema(description = "采购部门")
    private String buyerDeptName;


    /**
     * 项目负责人名称
     */
    @Schema(description = "项目负责人名称")
    private String projectLeaderName;
    /**
     * 报名截止时间
     */
    @Schema(description = "报名截止时间")
    private LocalDateTime registerEndTime;
    /**
     * 报价截止时间
     */
    @Schema(description = "报价截止时间")
    private LocalDateTime quoteEndTime;


    /**
     * 详细地址
     */
    @Schema(description = "详细地址")
    private String address;

    /**
     * 是否邀请回执 0否 1是
     */
    @Schema(description = "是否邀请回执")
    private Integer inviteReceipt;

    /**
     * 项目预算
     */
    @Schema(description = "项目预算")
    private BigDecimal budgetAmount;

    /**
     * 采购意图
     */
    @Schema(description = "采购意图")
    private String purchaseReason;

    /**
     * 供应商要求
     */
    @Schema(description = "供应商要求")
    private String supplierRequirement;

    /**
     * 项目概况
     */
    @Schema(description = "项目概况")
    private String projectDesc;


    /**
     * 项目状态
     */
    @Schema(description = "项目状态")
    private SrmProcurementProjectService.DocumentStatus status;

    /**
     * 审批状态
     */
    @Schema(description = "审批状态")
    private ApproveStatusEnum approveStatus;

    /**
     * 采购进度
     */
    @Schema(description = "采购进度")
    private ProjectProgressStatusEnum progressStatus;

    /**
     * 关联采购计划
     */
    @Schema(description = "关联采购计划")
    private List<String> relationPlanCodeList;
    

}