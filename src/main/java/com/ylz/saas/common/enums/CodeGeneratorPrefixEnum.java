package com.ylz.saas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/12 15:39
 * @Description
 */
@AllArgsConstructor
@Getter
public enum CodeGeneratorPrefixEnum {

    CGJH("采购计划", 6),
    <PERSON><PERSON>("需求行号", 6),
    <PERSON>("牧场",6),
    <PERSON><PERSON>("代理机构",6),
    <PERSON><PERSON>("专家",6),
    ZFFS("专家",6),
    GYS("供应商", 6),
    CGXM("采购项目", 6),
    BD("标段", 6),
    PGYS("平台供应商", 6),
    ZBGG("招标公告", 6),

    ;

    private final String  desc;

    private final Integer length;
}
