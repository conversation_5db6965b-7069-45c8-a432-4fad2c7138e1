/*
 * Copyright (c) 2020 canpan Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.ylz.saas;

import com.ylz.saas.common.feign.annotation.EnableSaasFeignClients;
import com.ylz.saas.common.job.annotation.EnableSaasXxlJob;
import com.ylz.saas.common.security.annotation.EnableSaasResourceServer;
import com.ylz.saas.common.swagger.annotation.EnableOpenApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR> procurement-platform 单体版本启动器，只需要运行此模块则整个系统启动
 */
@EnableOpenApi(value = "admin", isMicro = false)
@EnableSaasResourceServer
@SpringBootApplication
@EnableDiscoveryClient
@EnableSaasFeignClients
@EnableSaasXxlJob
public class ProcurementPlatformApplication {

    public static void main(String[] args) {
        SpringApplication.run(ProcurementPlatformApplication.class, args);
    }

}
