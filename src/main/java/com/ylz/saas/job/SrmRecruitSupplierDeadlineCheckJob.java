package com.ylz.saas.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ylz.saas.entity.SrmRecruitSupplier;
import com.ylz.saas.entity.SrmRecruitSupplierChange;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.service.SrmRecruitSupplierChangeService;
import com.ylz.saas.service.SrmRecruitSupplierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 招募公告截止时间检查定时任务
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SrmRecruitSupplierDeadlineCheckJob {

    private final SrmRecruitSupplierService srmRecruitSupplierService;
    private final SrmRecruitSupplierChangeService srmRecruitSupplierChangeService;

    /**
     * 招募公告截止时间检查任务
     * 检查招募中状态且设置了招募截止时间的招募公告，如果超过截止时间，则修改招募进程为END招募结束
     */
    @XxlJob("srmRecruitSupplierDeadlineCheckHandler")
    public void checkRecruitDeadline() {
        long startTime = System.currentTimeMillis();
        log.info("开始执行招募公告截止时间检查任务");
        
        try {
            // 查询招募中状态且设置了招募截止时间且已超过截止时间的招募公告
            LambdaQueryWrapper<SrmRecruitSupplier> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SrmRecruitSupplier::getProcess, SrmRecruitSupplier.Process.PROCESSING)
                    .isNotNull(SrmRecruitSupplier::getDeadline)
                    .lt(SrmRecruitSupplier::getDeadline, LocalDateTime.now());
            
            List<SrmRecruitSupplier> expiredRecruits = srmRecruitSupplierService.list(queryWrapper);
            
            if (expiredRecruits.isEmpty()) {
                log.info("未发现需要处理的过期招募公告");
                return;
            }
            
            log.info("发现 {} 个过期的招募公告，开始处理", expiredRecruits.size());
            
            // 批量更新招募进程为END
            for (SrmRecruitSupplier recruit : expiredRecruits) {
                recruit.setProcess(SrmRecruitSupplier.Process.END);
                log.info("招募公告 {} ({}) 已超过截止时间 {}，状态已更新为招募结束", 
                        recruit.getRecruitCode(), recruit.getRecruitTitle(), recruit.getDeadline());
            }
            
            // 批量更新
            srmRecruitSupplierService.updateBatchById(expiredRecruits);
            List<Long> ids = expiredRecruits.stream().map(SrmRecruitSupplier::getId).toList();
//            todo 需要将已审核中的进行撤销操作 ，并更新其状态为驳回
            srmRecruitSupplierChangeService.lambdaUpdate().in(SrmRecruitSupplierChange::getRecruitId,ids)
                    .eq(SrmRecruitSupplierChange::getApprovalStatus, ApproveStatusEnum.APPROVING)
                    .set(SrmRecruitSupplierChange::getApprovalStatus, ApproveStatusEnum.APPROVE_REJECT)
                    .update();
            log.info("成功处理 {} 个过期招募公告", expiredRecruits.size());
            
        } catch (Exception e) {
            log.error("执行招募公告截止时间检查任务异常", e);
        }
        
        log.info("招募公告截止时间检查任务执行完成，耗时：{} ms", System.currentTimeMillis() - startTime);
    }
}
