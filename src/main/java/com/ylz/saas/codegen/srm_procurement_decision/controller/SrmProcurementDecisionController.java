package com.ylz.saas.codegen.srm_procurement_decision.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.srm_procurement_decision.entity.SrmProcurementDecisionEntity;
import com.ylz.saas.codegen.srm_procurement_decision.param.PageReq;
import com.ylz.saas.codegen.srm_procurement_decision.param.UpsertReq;
import com.ylz.saas.codegen.srm_procurement_decision.service.SrmProcurementDecisionService;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

/**
 * 采购决策
 *
 * <AUTHOR>
 * @date 2025/8/5 16:34
 * @since v0.0.1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/srmProcurementDecision")
@Tag(description = "srmProcurementDecision", name = "采购决策管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SrmProcurementDecisionController {

    private final SrmProcurementDecisionService srmProcurementDecisionService;

    /**
     * 新增、编辑
     *
     * @param req 采购决策表
     * @return R
     */
    @Operation(summary = "新增采购决策表", description = "新增采购决策表")
    @SysLog("新增采购决策表")
    @PostMapping("/upsert")
    public R<Void> upsert(@Valid @RequestBody UpsertReq req) {
        srmProcurementDecisionService.upsert(req);
        return R.ok();
    }
    /**
     * 详情
     *
     * @param id 决策id
     * @return {@link R<SrmProcurementDecisionEntity> } resp
     */
    @GetMapping("/detail/{id}")
    public R<SrmProcurementDecisionEntity> detail(@PathVariable("id") Long id) {
        return R.ok(srmProcurementDecisionService.detail(id));
    }
    /**
     * 分页
     *
     * @param req req
     * @return {@link R<Page<SrmProcurementDecisionEntity>> } resp
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/page")
    public R<Page<SrmProcurementDecisionEntity>> page(@RequestBody PageReq req, Page page) {
        return R.ok(srmProcurementDecisionService.page(page, req));
    }
    /**
     * 删除（逻辑删）
     *
     * @param id id
     */
    @DeleteMapping("/logicDelete/{id}")
    public R<Void> logicDelete(@PathVariable("id") Long id) {
        srmProcurementDecisionService.logicDelete(id);
        return R.ok();
    }
    /**
     * 撤销审批
     * @param id 决策id
     */
    @PutMapping("/approveRevoke/{id}")
    @Operation(summary = "撤销审批", description = "根据code撤销审批")
    public R<Void> approveRevoke(@PathVariable("id") Long id) {
        srmProcurementDecisionService.approveRevoke(id);
        return R.ok();
    }
    /**
     * 作废
     *
     * @param id id
     */
    @PutMapping("/decisionInvalid/{id}")
    @Operation(summary = "作废", description = "根据code作废")
    public R<Void> decisionInvalid(@PathVariable("id") Long id) {
        srmProcurementDecisionService.decisionInvalid(id);
        return R.ok();
    }
}