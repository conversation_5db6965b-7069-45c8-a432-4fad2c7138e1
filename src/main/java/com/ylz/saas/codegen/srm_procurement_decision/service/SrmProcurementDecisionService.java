package com.ylz.saas.codegen.srm_procurement_decision.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.srm_procurement_decision.entity.SrmProcurementDecisionEntity;
import com.ylz.saas.codegen.srm_procurement_decision.param.PageReq;
import com.ylz.saas.codegen.srm_procurement_decision.param.UpsertReq;
import com.ylz.saas.service.SrmProcessConfigService;

/**
 * SrmProcurementDecisionService
 *
 * <AUTHOR>
 * @date 2025/8/5 16:32
 * @since v0.0.1
 */
public interface SrmProcurementDecisionService extends IService<SrmProcurementDecisionEntity> {

    /**
     * 新增/编辑
     */
    void upsert(UpsertReq req);
    /**
     * 详情
     */
    SrmProcurementDecisionEntity detail(Long id);
    /**
     * 分页
     */
    Page<SrmProcurementDecisionEntity> page(Page page, PageReq req);
    /**
     * 逻辑删
     */
    void logicDelete(Long id);
    /**
     * 作废
     */
    void decisionInvalid(Long id);
    /**
     * 审批撤销
     */
    void approveRevoke(Long id);
    /**
     * 审批通过回调处理
     * 1. 新建待审批：通过
     * 2. 作废待审批：通过
     */
    void approvePassCallback(Long id);
    /**
     * 审批拒绝回调处理
     * 1. 新建待审批：不通过
     * 2. 作废待审批：不通过
     */
    void approveRejectCallback(Long id);
    /**
     * 审批撤销回调处理（前端调用撤销）
     * 1. 新建待审批：撤回
     * 2. 作废待审批：撤回
     */
    void approveRevokeCallback(Long id);

    /**
     * 提交审批回调
     * 生效审核 SRM_PROCUREMENT_DECISION_AUDIT：        状态 => 新建待审核
     * 作废审核 SRM_PROCUREMENT_DECISION_INVALID_AUDIT：状态 => 作废待审核
     * @param id      决策id
     * @param bizType 审批类型
     */
    void submitApprovalCallback(Long id, SrmProcessConfigService.BizTypeEnum bizType);
}