package com.ylz.saas.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.ylz.saas.admin.api.dto.MessageSmsDTO;
import com.ylz.saas.admin.service.SysMessageService;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.common.security.util.SecurityUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/8 10:35
 * @Description
 */
@SpringBootTest
@Slf4j
public class SrmQuoteTest {

    @Autowired
    private SysMessageService sysMessageService;


    @Resource
    private SysUserService sysUserService;
    @Test
    public void testCollection(){
        List<Long> list1 = Lists.newArrayList(1L, 2L, 1032L);
        List<Long> list2 =Lists.newArrayList(1L, 3L, 1032L);
        System.out.println("是否相同：" + CollectionUtils.isEqualCollection(list1, list2));
    }


    @Test
    public void testSms(){
        MessageSmsDTO build = MessageSmsDTO.builder()
                .biz("SMS_NORMAL_CODE")
                .mobile("")
                .param("code", "1234")
                .build();
        log.info("build:{}", JSONObject.toJSONString(build));
        sysMessageService.sendSms(build);
    }

    @Test
    public void selectUserbyId(){
        System.out.println(sysUserService.getById("1932698915510792200").toString());
    }


}
