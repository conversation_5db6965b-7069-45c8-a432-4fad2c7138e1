# 采购平台项目需求交接文档

## 1. 项目概述

采购平台是一个用于支持企业采购流程数字化管理的系统，主要面向企业采购部门、供应商和平台管理员。系统提供了从采购计划、招标、投标到合同管理的全流程功能，旨在提升采购效率与透明度。

## 2. 技术架构

- JDK版本：Java 17
- 核心框架：Spring Boot 2.x
- ORM框架：MyBatis
- 接口文档：SpringDoc + Knife4j 3.0.5
- Web容器：Undertow
- 构建工具：Maven
- 部署方式：Docker容器化部署

## 3. 已实现功能需求

### 3.1 专家分类功能

#### 功能概述
实现了专家分类管理功能，将专家分为内部专家（INTERNAL）和外部专家（EXTERNAL）两类，根据分类采用不同的用户账号处理策略。

#### 核心逻辑
- 内部专家（INTERNAL）：新增时关联已有的用户账号，来源于SysUser表
- 外部专家（EXTERNAL）：新增时会新增一个用户账号

#### 接口变更
在[BaseExpertVo](file:///C:/ylzProject/procurement-platform/src/main/java/com/ylz/saas/codegen/base_expert/vo/BaseExpertVo.java#L15-L15)中新增了以下字段：
- password：用户密码（外部专家新建账号时使用，如果不提供则使用默认密码123456）
- nickname：用户昵称（外部专家新建账号时使用，如果不提供则使用姓名）
- lockFlag：锁定标记（外部专家新建账号时使用，0-正常，9-锁定，默认为0）

#### 接口说明
- `POST /baseExpert/save`：根据expertCategory字段进行不同处理逻辑

### 3.2 物料分类树形结构功能

#### 功能概述
为物料分类模块添加了完整的树形结构功能，支持多层级的物料分类管理。

#### 新增文件
1. 树形VO类：[BaseMaterialCategoryTreeVo](file:///C:/ylzProject/procurement-platform/src/main/java/com/ylz/saas/codegen/base_material_category/vo/BaseMaterialCategoryTreeVo.java#L11-L11)
2. 树形工具类：[TreeUtil](file:///C:/ylzProject/procurement-platform/src/main/java/com/ylz/saas/codegen/base_material_category/util/TreeUtil.java#L10-L10)
3. 测试控制器：MaterialCategoryTreeTestController

#### 新增接口
1. `GET /baseMaterialCategory/tree`：获取所有物料分类的树形结构
2. `GET /baseMaterialCategory/tree/status/{status}`：根据状态获取树形结构
3. `GET /baseMaterialCategory/tree/parent/{parentCategoryCode}`：根据父级编码获取子树
4. `GET /baseMaterialCategory/children/{parentCategoryCode}`：获取所有子节点编码
5. `GET /baseMaterialCategory/parents/{categoryCode}`：获取所有父节点编码

#### 技术特点
- 支持多租户数据隔离
- 支持无限层级的树形结构
- 性能优化，一次查询构建完整树形结构
- 工具类可复用于其他模块

### 3.3 物料基础信息表添加备注字段

#### 功能概述
为物料基础信息表([base_material_info](file:///C:/ylzProject/procurement-platform/src/main/java/com/ylz/saas/codegen/base_material_info/entity/BaseMaterialInfoEntity.java#L14-L14))添加remark备注字段，用于存储物料的额外说明信息。

#### 数据库变更
```sql
ALTER TABLE `base_material_info` 
ADD COLUMN `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注' AFTER `status`;
```

#### 代码变更
涉及以下文件的修改：
1. 实体类：[BaseMaterialInfoEntity](file:///C:/ylzProject/procurement-platform/src/main/java/com/ylz/saas/codegen/base_material_info/entity/BaseMaterialInfoEntity.java#L14-L14)
2. VO类：[BaseMaterialInfoVo](file:///C:/ylzProject/procurement-platform/src/main/java/com/ylz/saas/codegen/base_material_info/vo/BaseMaterialInfoVo.java#L11-L11)
3. Excel导入类：[BaseMaterialInfoReq](file:///C:/ylzProject/procurement-platform/src/main/java/com/ylz/saas/codegen/base_material_info/excel/BaseMaterialInfoReq.java#L13-L13)
4. Excel导出类：[BaseMaterialInfoResp](file:///C:/ylzProject/procurement-platform/src/main/java/com/ylz/saas/codegen/base_material_info/excel/BaseMaterialInfoResp.java#L12-L12)
5. 查询参数类：[BaseMaterialInfoParam](file:///C:/ylzProject/procurement-platform/src/main/java/com/ylz/saas/codegen/base_material_info/param/BaseMaterialInfoParam.java#L12-L12)

#### 功能特性
- 支持最多500个字符的备注信息
- 支持按备注内容进行模糊查询
- Excel导入导出时支持备注字段
- 现有API接口自动支持备注字段

### 3.4 质量指标属性值表添加字段

#### 功能概述
为质量指标属性值表([base_quality_indicator_attribute_value](file:///C:/ylzProject/procurement-platform/src/main/java/com/ylz/saas/codegen/base_quality_indicator_attribute_value/entity/BaseQualityIndicatorAttributeValueEntity.java#L11-L11))添加三个新字段，用于存储更详细的质量标准信息。

#### 数据库变更
```sql
ALTER TABLE `base_quality_indicator_attribute_value`
    ADD COLUMN `value_content` varchar(500) NULL DEFAULT NULL COMMENT '属性值内容';

ALTER TABLE `base_quality_indicator_attribute_value`
    ADD COLUMN `rejection_standard` varchar(500) NULL DEFAULT NULL COMMENT '拒收标准';

ALTER TABLE `base_quality_indicator_attribute_value`
    ADD COLUMN `penalty_standard` varchar(500) NULL DEFAULT NULL COMMENT '扣款标准';
```

## 4. 部署与运行

### 4.1 构建项目
```bash
mvn clean package
```

### 4.2 本地运行
```bash
mvn spring-boot:run
# 或者直接运行 ProcurementPlatformApplication.java
```

### 4.3 Docker部署
```bash
docker build -t procurement-platform .
```

## 5. 注意事项

1. 所有功能均已考虑多租户数据隔离
2. 树形结构功能需要确保parentCategoryCode字段的数据完整性，避免循环引用
3. 物料备注字段最大长度为500字符，前端需要做相应长度校验
4. 质量指标相关字段可能包含业务敏感信息，需注意权限控制

## 6. 后续优化建议

1. 树形结构功能可考虑添加Redis缓存提升查询性能
2. 物料备注字段如经常用于查询，建议考虑添加索引
3. 可为常用的备注内容提供模板选择功能
4. 可扩展支持树形节点的拖拽排序功能